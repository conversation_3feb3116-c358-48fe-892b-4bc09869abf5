﻿using AutoMapper;
using Contract.Configuration;
using Contract.ConsolidatedReport;
using Contract.DataIngestion;
using Contract.Documents;
using Contract.Funds;
using Contract.InternalReport;
using Contract.KPI;
using Contract.PortfolioCompany;
using Contract.Utility;
using CurrencyRates.Interfaces;
using CurrencyRates.Models;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.UnitOfWork;
using DataAnalytic;
using Financials;
using Financials.Helpers;
using Financials.Models;
using Imports.Helpers;
using Master;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using PortfolioCompany.Interfaces;
using Shared;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Utility.Helpers;
using Utility.Services;

namespace PortfolioCompany.Services
{
    public partial class DataIngestionService : IDataIngestionService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDapperGenericRepository _dapperGenericRepository;
        private readonly ILogger<DataIngestionService> _logger;
        private readonly ICurrencyConversionRate _currencyConversionRate;
        private readonly IKpiService _kpiService;
        private readonly IPageDetailsConfigurationService _pageDetailConfigService;
        private readonly IMapper _mapper;
        private readonly IEncryption _encryption;
        public DataIngestionService(IUnitOfWork unitOfWork, IDapperGenericRepository dapperGenericRepository, ILogger<DataIngestionService> logger, ICurrencyConversionRate currencyConversionRate, IKpiService kpiService, IPageDetailsConfigurationService pageDetailConfigService, IMapper mapper, IEncryption encryption)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _dapperGenericRepository = dapperGenericRepository ?? throw new ArgumentNullException(nameof(dapperGenericRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _currencyConversionRate = currencyConversionRate ?? throw new ArgumentNullException(nameof(currencyConversionRate));
            _kpiService = kpiService ?? throw new ArgumentNullException(nameof(kpiService));
            _pageDetailConfigService = pageDetailConfigService ?? throw new ArgumentNullException(nameof(pageDetailConfigService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _encryption = encryption ?? throw new ArgumentNullException(nameof(encryption));
        }

        /// <summary>
        /// PublishData
        /// </summary>
        /// <param name="publishModels"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<bool> PublishData(List<PublishModel> publishModels, string connectionString, string processId , int userId)
        {
            _logger.LogInformation("Start processing and publishing KPI data for all tabs");
            foreach (var data in publishModels)
            {
                try
                {
                    if (data.KpiList.Count > 0)
                    {
                        _logger.LogInformation("Processing and publishing KPI data for company ID: {CompanyId}", data.CompanyId);
                        data.ConnectionString = connectionString;
                        // Extract KPI headers for FX rate conversion
                        var kpiHeaders = ExtractKpiHeaders(data.KpiList);
                        // Get methodology IDs for currency conversion
                        string methodologyIds = GetMethodologyIds(data.KpiList);
                        // Get company details for financial year end
                        var companyDetails = await GetCompanyDetails(data.CompanyId);
                        var fyMonth = companyDetails?.GetFinancialYearEnd() ?? 12; // Default to December if not found

                        if (data.UnitCurrency?.ToLower() != Constants.Thousands)
                        {
                            UnitConversionToThousands(data);
                        }
                        if (data.CurrencyId != companyDetails.ReportingCurrencyId && data.CurrencyId > 0)
                        {
                            // Get currency conversion rates
                            var fxRates = await GetCurrencyConversionRates(methodologyIds, data.CurrencyId, companyDetails.ReportingCurrencyId, fyMonth, kpiHeaders);
                            // Apply currency conversion using FX rates
                            ApplyCurrencyConversion(data.KpiList, fxRates);
                        }
                        data.UserId = userId;
                        await ProcessExcelDataIntoDbAsync(data);
                        _logger.LogInformation("Successfully published KPI data for company ID: {CompanyId}", data.CompanyId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error publishing KPI data for company ID: {CompanyId}", data.CompanyId);
                }
            }
            _logger.LogInformation("End processing and publishing KPI data for all tabs");
            var check= await UpdateDocumentsToDocumentCollection(processId, userId);
            return true;
        }

        /// <summary>
        /// SpecificKpisPublish
        /// </summary>
        /// <param name="data"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public async Task<bool> SpecificKpisPublish(SpecificKpiPublishModel data, string connectionString)
        {
            _logger.LogInformation("Start processing and publishing SpecificKpis data for all tabs");
            data.ConnectionString = connectionString;
            await SaveFundKpisData(data);
            await BulkSavePortfolioCompanyStaticFields(data.CompaniesDetails, data.UserId);

            foreach (var companyData in data.CompaniesDetails)
            {
                _logger.LogInformation("Processing and publishing SpecificKpis data for company ID: {CompanyId}", companyData.CompanyId);
                try
                {
                    await PublishSpecificKpis(data, companyData);
                    await PublishSpecificFinancialsKpis(data, companyData);
                    await ProcessAndSaveAllKpisData(data, companyData, companyData.CustomKpis, (int)KpiModuleType.CustomTable1);
                    await ProcessAndSaveAllKpisData(data, companyData, companyData.OtherKpis, (int)KpiModuleType.OtherKPI1);

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error publishing SpecificKpis data for company ID: {CompanyId}", companyData.CompanyId);
                }
            }

            _logger.LogInformation("End processing and publishing SpecificKpis data for all tabs");
            return true;
        }
        /// <summary>
        /// PublishSpecificFinancialsKpis
        /// </summary>
        /// <param name="data"></param>
        /// <param name="companyData"></param>
        /// <returns></returns>
        private async Task PublishSpecificFinancialsKpis(SpecificKpiPublishModel data, PCDataModel companyData)
        {
            await ProcessAndSaveAllKpisData(data, companyData, companyData.ProfitLossKpis, (int)KpiModuleType.ProfitAndLoss, true);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.BalancesheetKpis, (int)KpiModuleType.BalanceSheet, true);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.Cashflow, (int)KpiModuleType.CashFlow, true);
        }
        /// <summary>
        /// PublishSpecificKpis
        /// </summary>
        /// <param name="data"></param>
        /// <param name="companyData"></param>
        /// <returns></returns>
        private async Task PublishSpecificKpis(SpecificKpiPublishModel data, PCDataModel companyData)
        {
            await ProcessAndSaveAllKpisData(data, companyData, companyData.TradingKpis, (int)KpiModuleType.TradingRecords);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.CreditKpis, (int)KpiModuleType.CreditKPI);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.InvestmentKpis, (int)KpiModuleType.Investment);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.ImpactKpis, (int)KpiModuleType.Impact);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.CompanyKpis, (int)KpiModuleType.Company);
            await ProcessAndSaveAllKpisData(data, companyData, companyData.OperationalKpis, (int)KpiModuleType.Operational);
        }
        /// <summary>
        /// ProcessAndSaveAllKpisData
        /// </summary>
        /// <param name="data"></param>
        /// <param name="companyData"></param>
        /// <param name="list"></param>
        /// <param name="moduleId"></param>
        /// <param name="isFinancials"></param>
        /// <returns></returns>
        private async Task ProcessAndSaveAllKpisData(SpecificKpiPublishModel data, PCDataModel companyData, List<KpiList> list, int moduleId, bool isFinancials = false)
        {
            if (list?.Count > 0)
            {
                data.IsFinancial = isFinancials;
                await ProcessAndSaveKpiData(list, data, companyData, moduleId);
            }
        }
        /// <summary>
        /// SaveFundKpisData
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private async Task SaveFundKpisData(SpecificKpiPublishModel data)
        {
            if (data?.FundKpiSections.FundKpis.Count > 0)
            {
                _logger.LogInformation("Processing and publishing Fund KPI data for Fund ID: {FundId}", data.FundId);
                    var kpiHeaders = ExtractKpiHeaders(data.FundKpiSections.FundKpis);
                    string methodologyIds = GetMethodologyIds(data.FundKpiSections.FundKpis);
                    var fundData = await GetFundDetails(data.FundId);
                    if (data.FundUnitCurrency?.ToLower() != Constants.Absolute)
                    {
                        UnitConversionToAbsolute(data.FundUnitCurrency, data.FundKpiSections.FundKpis);
                    }
                    if (fundData.FundCurrencyId != data.FundCurrencyId && data.FundCurrencyId > 0)
                    {
                        var fxRates = await GetCurrencyConversionRates(methodologyIds, data.FundCurrencyId, fundData.FundCurrencyId, 12, kpiHeaders);
                        ApplyCurrencyConversion(data.FundKpiSections.FundKpis, fxRates);
                    }
                    await SaveFundKpisToDb(data, data.FundKpiSections);
            }
        }
        /// <summary>
        /// SaveFundKpisToDb
        /// </summary>
        /// <param name="data"></param>
        /// <param name="fundKpiSection"></param>
        /// <returns></returns>
        private static async Task SaveFundKpisToDb(SpecificKpiPublishModel data, FundSectiionKpis fundKpiSection)
        {
            PublishModel publishModel = new()
            {
                UserId = data.UserId,
                ConnectionString = data.ConnectionString,
                KpiList = fundKpiSection.FundKpis,
                IsFinancial = false,
                FundId = data.FundId,
                ProcessId = data.ProcessId
            };
            await InsertDataIntoDb<KpiList>(publishModel, Constants.FundFinancialsKpiStaging, SqlConstants.SPBulkUploadFundFinancials, true);
        }
        /// <summary>
        /// ProcessAndSaveKpiData
        /// </summary>
        /// <param name="data"></param>
        /// <param name="specificKpiPublishModel"></param>
        /// <param name="companyDataModel"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        private async Task ProcessAndSaveKpiData(List<KpiList> data, SpecificKpiPublishModel specificKpiPublishModel, PCDataModel companyDataModel, int moduleId)
        {
            if (data?.Count > 0)
            {
                _logger.LogInformation("Processing and publishing KPI data for company ID: {CompanyId}", companyDataModel.CompanyId);
                var kpiHeaders = ExtractKpiHeaders(data);
                string methodologyIds = GetMethodologyIds(data);
                var companyDetails = await GetCompanyDetails(companyDataModel.CompanyId);
                var fyMonth = companyDetails?.GetFinancialYearEnd() ?? 12;

                UnitCurrencyConversionForSpecficKpis(data, specificKpiPublishModel, companyDataModel);

                if (companyDataModel.CurrencyId != companyDetails.ReportingCurrencyId && companyDataModel.CurrencyId > 0)
                {
                    var fxRates = await GetCurrencyConversionRates(methodologyIds, companyDataModel.CurrencyId, companyDetails.ReportingCurrencyId, fyMonth, kpiHeaders);
                    ApplyCurrencyConversion(data, fxRates);
                }
                PublishModel publishModel = SetPublishModelForSpecificKpis(data, specificKpiPublishModel, companyDataModel, moduleId);
                await ProcessExcelDataIntoDbAsync(publishModel);
                _logger.LogInformation("Successfully published KPI data for company ID: {CompanyId}", companyDataModel.CompanyId);
            }
        }
        /// <summary>
        /// SetPublishModelForSpecificKpis
        /// </summary>
        /// <param name="data"></param>
        /// <param name="specificKpiPublishModel"></param>
        /// <param name="companyDataModel"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        private static PublishModel SetPublishModelForSpecificKpis(List<KpiList> data, SpecificKpiPublishModel specificKpiPublishModel, PCDataModel companyDataModel, int moduleId)
        {
            return new()
            {
                UserId = specificKpiPublishModel.UserId,
                ConnectionString = specificKpiPublishModel.ConnectionString,
                KpiList = data,
                CompanyId = companyDataModel.CompanyId,
                IsFinancial = specificKpiPublishModel.IsFinancial,
                ModuleId = moduleId,
                FundId = specificKpiPublishModel.FundId,
            };
        }
        /// <summary>
        /// UnitCurrencyConversionForSpecficKpis
        /// </summary>
        /// <param name="data"></param>
        /// <param name="specificKpiPublishModel"></param>
        /// <param name="companyDataModel"></param>
        private void UnitCurrencyConversionForSpecficKpis(List<KpiList> data, SpecificKpiPublishModel specificKpiPublishModel, PCDataModel companyDataModel)
        {
            if (specificKpiPublishModel.IsFinancial)
            {
                if (companyDataModel.UnitCurrency?.ToLower() != Constants.Thousands)
                {
                    ConvertoToThousands(data, companyDataModel.UnitCurrency);
                }
            }
            else
            {
                if (companyDataModel.UnitCurrency?.ToLower() != Constants.Absolute)
                {
                    UnitConversionToAbsolute(companyDataModel.UnitCurrency, data);
                }
            }
        }
        /// <summary>
        /// UnitConversionToAbsolute
        /// </summary>
        /// <param name="unitCurrency"></param>
        /// <param name="data"></param>
        private void UnitConversionToAbsolute(string unitCurrency, List<KpiList> data)
        {
            // Convert values based on UnitCurrency property
            _logger.LogInformation("Converting values from {UnitCurrency} to thousands format", unitCurrency);

            foreach (var item in data)
            {
                // Only convert numeric values that can be parsed as decimal and are currency types
                if (decimal.TryParse(item.KpiValue, out decimal value) && (item.KPIInfo == Constants.KpiInfoCurrency))
                {
                    var convertedValue = unitCurrency.ToLower() switch
                    {
                        Constants.Thousands => value * 1000,
                        Constants.Millions => value * 1000000,
                        Constants.Billions => value * 1000000000,
                        _ => value,
                    };
                    item.KpiValue = convertedValue.ToString();
                    _logger.LogDebug("Converted {KpiInfo} value from {UnitCurrency}: {OriginalValue} to thousands: {ConvertedValue}", item.KPIInfo, unitCurrency, value, item.KpiValue);
                }
            }
        }
        /// <summary>
        /// UnitConversionToThousands
        /// </summary>
        /// <param name="data"></param>
        private void UnitConversionToThousands(PublishModel data)
        {
            string unitCurrency = data.UnitCurrency?.ToLower() ?? "absolute";
            _logger.LogInformation("Converting values from {UnitCurrency} to thousands format", unitCurrency);
            ConvertoToThousands(data.KpiList, unitCurrency);
        }
        /// <summary>
        /// ConvertoToThousands
        /// </summary>
        /// <param name="KpiList"></param>
        /// <param name="unitCurrency"></param>
        private void ConvertoToThousands(List<KpiList> KpiList, string unitCurrency)
        {
            foreach (var item in KpiList)
            {
                if (decimal.TryParse(item.KpiValue, out decimal value) && (item.KPIInfo == Constants.KpiInfoCurrency))
                {
                    var convertedValue = unitCurrency switch
                            {
                                Constants.Absolute => value / 1000,// Convert from absolute to thousands
                                Constants.Thousands => value,// Already in thousands, no conversion needed
                                Constants.Millions => value * 1000,// Convert from millions to thousands
                                Constants.Billions => value * 1000000,// Convert from billions to thousands
                                _ => value,// Default case (treat as absolute)
                            };

                    // Format to retain decimal places and update the value
                    item.KpiValue = convertedValue.ToString();
                    _logger.LogDebug("Converted {KpiInfo} value from {UnitCurrency}: {OriginalValue} to thousands: {ConvertedValue}", item.KPIInfo, unitCurrency, value, item.KpiValue);
                }
            }
        }

        /// <summary>
        /// Gets KPI modules with chart values greater than 0
        /// </summary>
        /// <returns>List of KPI module information</returns>
        public async Task<List<KpiPageConfigInfo>> GetKpiModulesPageConfigDetails()
        {
            return await GetKpiModulesPageConfigDetails(false);
        }
        public async Task<List<KpiPageConfigInfo>> GetKpiModulesPageConfigDetails(bool isSpecificKpi = false)
        {
            try
            {
                _logger.LogInformation("Fetching KPI modules with chart values");

                List<M_SubPageDetails> subPageDetails = [];
                if (isSpecificKpi)
                    subPageDetails = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(
                        x => !x.IsDeleted &&
                            (x.Name == Constants.FundKeyPerformanceIndicator ||
                             x.Name == Constants.KeyPerformanceIndicator));
                else
                    subPageDetails = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(
                        x => !x.IsDeleted && x.Name == Constants.CompanyFinancials);

                if (subPageDetails == null || subPageDetails.Count == 0)
                {
                    _logger.LogWarning("No subpage details found for KPI or Other KPIs");
                    return [];
                }

                var subPageIds = subPageDetails.Select(sp => sp.SubPageID).ToList();
                var subPageLookup = subPageDetails.ToDictionary(sp => sp.SubPageID);

                var subPageFields = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(
                    x => !x.IsDeleted && x.IsActive && subPageIds.Contains(x.SubPageID));
                if (subPageFields == null || !subPageFields.Any())
                {
                    _logger.LogWarning("No subpage fields found for the selected subpages");
                    return [];
                }

                var fieldIds = subPageFields.Select(f => f.FieldID).ToList();

                // Main KPI modules
                var kpiModules = await _unitOfWork.M_KpiModulesRepository.FindAllAsync(
                    x => !x.IsDeleted && x.IsActive &&
                        (
                            x.ModuleID == (int)KpiModuleType.ProfitAndLoss ||
                            x.ModuleID == (int)KpiModuleType.BalanceSheet ||
                            x.ModuleID == (int)KpiModuleType.CashFlow ||
                            x.ModuleID == (int)KpiModuleType.CreditKPI ||
                            x.ModuleID == (int)KpiModuleType.TradingRecords
                        ));

                var validKpiModules = kpiModules.Where(m => !string.IsNullOrEmpty(m.PageConfigFieldName)).ToList();

                var fundKpiModules = await _unitOfWork.MFundKpiModulesRepository.FindAllAsync(
                x => !x.IsDeleted && x.IsActive == true && x.ModuleId == (int)FundFinancials.FundFinancials);

                var validFundKpiModules = fundKpiModules.Where(m => !string.IsNullOrEmpty(m.PageConfigFieldName)).ToList();

                var mSubSectionFields = await _unitOfWork.MSubSectionFieldsRepository.FindAllAsync(
                    x => !x.IsDeleted && fieldIds.Contains(x.FieldID) && !string.IsNullOrEmpty(x.ChartValue));

                var allValueTypes = await _unitOfWork.M_ValueTypesRepository.FindAllAsync(vt => !vt.IsDeleted);
                var valueTypesDict = allValueTypes.ToDictionary(vt => vt.HeaderValue, vt => vt.ValueTypeID);

                var fieldLookup = subPageFields.ToLookup(f => f.Name);
                var subSectionFieldLookup = mSubSectionFields.ToLookup(msf => msf.FieldID);

                // Main KPI modules logic
                var result = (
                    from module in validKpiModules
                    from field in fieldLookup[module.PageConfigFieldName]
                    where subPageLookup.ContainsKey(field.SubPageID)
                    let matchingFields = subSectionFieldLookup[field.FieldID].ToList()
                    where matchingFields.Any()
                    let subsectionFieldsList = matchingFields.Select(msf => new SubSectionFieldInfo
                    {
                        ValueTypId = valueTypesDict.TryGetValue(msf.Name, out int valueTypeId) ? valueTypeId : 0,
                        Name = msf.Name,
                        AliasName = msf.AliasName,
                        ChartValue = msf.ChartValue
                    }).ToList()
                    where subsectionFieldsList.Count > 0
                    select new KpiPageConfigInfo
                    {
                        ModuleName = module.Name,
                        ModuleId = module.ModuleID,
                        PageConfigAliasName = field.AliasName,
                        SubSectionFields = subsectionFieldsList
                    }
                ).ToList();

                // Fund Financials modules logic (same as above)
                var fundResult = (
                    from module in validFundKpiModules
                    from field in fieldLookup[module.PageConfigFieldName]
                    where subPageLookup.ContainsKey(field.SubPageID)
                    let matchingFields = subSectionFieldLookup[field.FieldID].ToList()
                    where matchingFields.Any()
                    let subsectionFieldsList = matchingFields.Select(msf => new SubSectionFieldInfo
                    {
                        ValueTypId = valueTypesDict.TryGetValue(msf.Name, out int valueTypeId) ? valueTypeId : 0,
                        Name = msf.Name,
                        AliasName = msf.AliasName,
                        ChartValue = msf.ChartValue
                    }).ToList()
                    where subsectionFieldsList.Count > 0
                    select new KpiPageConfigInfo
                    {
                        ModuleName = module.Name,
                        ModuleId = module.ModuleId,
                        PageConfigAliasName = field.AliasName,
                        SubSectionFields = subsectionFieldsList
                    }
                ).ToList();

                // Add fundResult to main result
                result.AddRange(fundResult);

                // Old logic: flat field-based, only for isSpecificKpi
                if (isSpecificKpi)
                {
                    foreach (var field in subPageFields)
                    {
                        if (!result.Any(r => r.PageConfigAliasName == field.AliasName && r.ModuleName == field.Name))
                        {
                            result.Add(new KpiPageConfigInfo
                            {
                                PageConfigAliasName = field.AliasName,
                                ModuleName = field.Name,
                            });
                        }
                    }

                    var staticInformation = await _unitOfWork.SubPageDetailsRepository.GetFirstOrDefaultAsync(
                        x => !x.IsDeleted && x.Name == Constants.StaticInformation);

                    if (staticInformation != null)
                    {
                        var staticFields = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(
                            x => !x.IsDeleted && x.SubPageID == staticInformation.SubPageID);

                        if (staticFields != null && staticFields.Any())
                        {
                            result.Add(new KpiPageConfigInfo
                            {
                                ModuleName = staticInformation.Name?.Replace(" ", ""),                             PageConfigAliasName = staticInformation.AliasName,
                                ModuleId = (int)StaticInformation.StaticInformation,

                                SubSectionFields = staticFields.Select(f => new SubSectionFieldInfo
                                {
                                    Name = f.Name,
                                    AliasName = f.AliasName,
                                }).ToList()
                            });
                        }
                    }
                }

                _logger.LogInformation("Retrieved {Count} KPI modules with chart values", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving KPI modules with chart values");
                return [];
            }
        }
        public async Task<List<KpiPageConfigInfo>> GetPageConfigDetails()
        {
            return await GetKpiModulesPageConfigDetails(true);
        }
        /// <summary>
        /// GetUserFunds
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<FundNameModel>> GetUserFunds(int userId)
        {
            var queryData = await _dapperGenericRepository.Query<FundsQueryModel>(SqlConstants.QueryByGetAllFunds, new { UserId = userId });
            var data = queryData?.Select(fundDetails => new FundNameModel
            {
                FundId = (int)fundDetails.FundID,
                FundName = fundDetails.FundName,
                EncryptedFundId = fundDetails.EncryptedFundId,
            }).AsQueryable();
            return data?.ToList();
        }
        /// <summary>
        /// GetFundKpis
        /// </summary>
        /// <returns></returns>
        public async Task<List<SubPageFieldsModel>> GetFundKpis()
        {
            List<SubPageFieldModel> fundIngestionFields = await _pageDetailConfigService.GetActiveFieldsBySubPageIdWithSequenceNo((int)PageConfigurationSubFeature.FundIngestion);
            return fundIngestionFields.Select(x => new SubPageFieldsModel()
            {
                FieldId = x.Id,
                SubPageId = x.SubPageID,
                Name = x.Name,
                IsCustom = x.IsCustom,
                AliasName = x.DisplayName,
                DataTypeId = x.DataTypeId
            }).ToList();
        }
        public async Task<List<PageFieldValueModelTrackRecord>> GetFundIngestionDynamicColumns()
        {
            List<SubPageFieldModel> trackDelaFields = await _pageDetailConfigService.GetActiveFieldsBySubPageIdWithSequenceNo((int)PageConfigurationSubFeature.FundIngestion);
            return trackDelaFields.Where(X => X.DisplayName != "Year").Select(x => new PageFieldValueModelTrackRecord()
            {
                SubPageID = x.SubPageID,
                FieldID = x.Id,
                Name = x.Name,
                Value = "NA",
                DisplayName = x.DisplayName,
                Sequence = x.SequenceNo,
                IsActive = x.IsActive,
                IsCustom = x.IsCustom,
                DataType = x.DataTypeId
            }).ToList();
        }
        public async Task<string> GetsubpagedetailsText(int fundIngestion)
        {
            return await _pageDetailConfigService.GetsubpagedetailsText((int)PageConfigurationSubFeature.FundIngestion);
        }
        /// <summary>
        /// GetFundIngestionList
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<List<FundIngestionModel>> GetFundIngestionList(FundIngestionFilter filter)
        {
            var result = new List<FundIngestionModel>();
            var data = await _unitOfWork.FundIngestionRepository.FindAllAsync(x => !x.IsDeleted);
            if (data.Any(x => x.EncryptedFundIngestionId == null))
            {
                var list = data.Where(x => x.EncryptedFundIngestionId == null).ToList();
                _ = list.Select(x => { x.EncryptedFundIngestionId = _encryption.Encrypt(x.FundIngestionId.ToString()); return x; }).ToList();
                _unitOfWork.Save();
            }
            if (filter != null)
            {
                if (filter.FundDetailsId > 0)
                {
                    data = data.Where(x => x.FundDetailsId == filter.FundDetailsId).ToList();
                }
                // Map to model using AutoMapper
                result = _mapper.Map<List<FundIngestionModel>>(data);
            }
            return result;
        }
        /// <summary>
        /// ExtractKpiHeaders
        /// </summary>
        /// <param name="kpiList"></param>
        /// <returns></returns>
        private static List<FxRatePeriodModel> ExtractKpiHeaders(List<KpiList> kpiList)
        {
            return kpiList.Select(x => new FxRatePeriodModel { Month = x.Month, Year = x.Year, Quarter = x.Quarter, PeriodType = x.ValueType }).DistinctBy(x => new { x.Month, x.Year, x.Quarter, x.PeriodType }).ToList();
        }
        /// <summary>
        /// GetMethodologyIds
        /// </summary>
        /// <param name="kpiList"></param>
        /// <returns></returns>
        private static string GetMethodologyIds(List<KpiList> kpiList)
        {
            return string.Join(",", kpiList.Where(x => x.KPIInfo == Constants.KpiInfoCurrency && x.MethodologyId != 0).Select(x => x.MethodologyId).Distinct());
        }
        /// <summary>
        /// GetCompanyDetails
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        private async Task<PortfolioCompanyDetails> GetCompanyDetails(int companyId)
        {
            return await _dapperGenericRepository.QueryFirstAsync<PortfolioCompanyDetails>(SqlConstants.QueryByPCWithID, new { companyId });
        }
        private async Task<List<PortfolioCompanyDetails>> GetCompaniesDetails(List<int> Ids)
        {
            // Correcting the return type to match the expected List<PortfolioCompanyDetails>  
            return await _dapperGenericRepository.Query<PortfolioCompanyDetails>(SqlConstants.QueryToGetCompaniesWithIds, new { Ids });
        }
        /// <summary>
        /// GetCurrencyConversionRates
        /// </summary>
        /// <param name="methodologyIds"></param>
        /// <param name="fromCurrencyId"></param>
        /// <param name="fyMonth"></param>
        /// <param name="kpiHeaders"></param>
        /// <returns></returns>
        private async Task<List<FxRateModel>> GetCurrencyConversionRates(string methodologyIds, int fromCurrencyId, int toCurrencyId, int fyMonth, List<FxRatePeriodModel> kpiHeaders)
        {
            // Default to System Api if not specified
            string currencyRateSource = Constants.CurrencyRatesBulkUploadSource;

            var fxRateModel = new FxRateMethodologyModel
            {
                Query = SqlFinancialConstants.QueryByGetFxRatesByMethodologies,
                MethodologyIds = methodologyIds.TrimEnd(','),
                Fy = fyMonth,
                ConversionRateSource = currencyRateSource,
                FromCurrencyId = fromCurrencyId,
                ToCurrencyId = toCurrencyId,
                PeriodJson = JsonSerializer.Serialize(kpiHeaders)
            };

            return await _currencyConversionRate.GetFxRatesByMethodology(fxRateModel);
        }
        /// <summary>
        /// ApplyCurrencyConversion
        /// </summary>
        /// <param name="kpiList"></param>
        /// <param name="fxRates"></param>
        private void ApplyCurrencyConversion(List<KpiList> kpiList, List<FxRateModel> fxRates)
        {
            foreach (var item in kpiList)
            {
                // Only convert currency KPIs
                if (item.KPIInfo != Constants.KpiInfoCurrency)
                    continue;

                if (!decimal.TryParse(item.KpiValue, out decimal value))
                    continue;

                var rate = fxRates?.FirstOrDefault(x =>
                    x.MethodologyId == item.MethodologyId &&
                    x.Quarter == (string.IsNullOrEmpty(item.Quarter) ? null : item.Quarter) &&
                    x.Year == item.Year &&
                    x.Month == (item.Month == 0 ? null : item.Month))?.FxRate;

                if (rate.HasValue)
                {
                    item.KpiValue = (value * rate.Value).ToString();
                    _logger.LogDebug("Converted KPI value {OriginalValue} with rate {Rate} to {ConvertedValue}", value, rate.Value, item.KpiValue);
                }
                else
                {
                    item.KpiValue = null;
                }
            }
        }
        /// <summary>
        /// ProcessExcelDataIntoDbAsync
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private static async Task ProcessExcelDataIntoDbAsync(PublishModel data)
        {
            switch (data.ModuleId)
            {
                case (int)KpiModuleType.Investment:
                    await InsertDataIntoDb<KpiList>(data, Constants.InvestmentKpiStaging, SqlConstants.QueryByInvestmentKpiDetails);
                    break;
                case (int)KpiModuleType.TradingRecords:
                    await InsertDataIntoDb<KpiList>(data, Constants.TradingKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                    break;
                case (int)KpiModuleType.Impact:
                    await InsertDataIntoDb<KpiList>(data, Constants.ImpactKpiStaging, SqlConstants.QueryByImpactKpiDetails);
                    break;
                case (int)KpiModuleType.ProfitAndLoss:
                    await InsertDataIntoDb<KpiList>(data, Constants.ProfitAndLossStaging, SqlConstants.QueryByFinancialKpiDetails);
                    break;
                case (int)KpiModuleType.BalanceSheet:
                    await InsertDataIntoDb<KpiList>(data, Constants.BalanceSheetStaging, SqlConstants.QueryByFinancialKpiDetails);
                    break;
                case (int)KpiModuleType.CashFlow:
                    await InsertDataIntoDb<KpiList>(data, Constants.CashFlowStaging, SqlConstants.QueryByFinancialKpiDetails);
                    break;
                case (int)KpiModuleType.CreditKPI:
                    await InsertDataIntoDb<KpiList>(data, Constants.CreditKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                    break;
                case (int)KpiModuleType.Company:
                    await InsertDataIntoDb<KpiList>(data, Constants.CompanyKpiStaging, SqlConstants.QueryByCompanyKpiDetails);
                    break;
                case (int)KpiModuleType.Operational:
                    await InsertDataIntoDb<KpiList>(data, Constants.OperationalKpiStaging, SqlConstants.QueryByOperationalKpiDetails);
                    break;
                case (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4:
                    await InsertDataIntoDb<KpiList>(data, Constants.CustomKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                    break;
                case (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5
                or (int)KpiModuleType.OtherKPI6 or (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9 or (int)KpiModuleType.OtherKPI10:
                    await InsertDataIntoDb<KpiList>(data, Constants.OtherKpiStaging, SqlConstants.QueryByMasterKpiDetails);
                    break;
            }
        }
        /// <summary>
        /// InsertDataIntoDb
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="tableName"></param>
        /// <param name="spName"></param>
        /// <param name="isFundKpi"></param>
        /// <returns></returns>
        private static async Task InsertDataIntoDb<T>(PublishModel data, string tableName, string spName, bool isFundKpi = false)
        {
            string tableStagingName = BulkUploadHelper.CreateTableName(tableName, data.UserId);
            string createTableQuery = CommonQuery<T>.CreateTable(tableStagingName);
            var tableData = data.KpiList.ToDataTable(tableStagingName);
            await ProcessValueIntoActualTable(data, spName, tableStagingName, createTableQuery, tableData, false, isFundKpi);
        }
        /// <summary>
        /// ProcessValueIntoActualTable
        /// </summary>
        /// <param name="data"></param>
        /// <param name="spName"></param>
        /// <param name="tableName"></param>
        /// <param name="query"></param>
        /// <param name="tableData"></param>
        /// <param name="isCap"></param>
        /// /// <param name="isFundKpi"></param>
        /// <returns></returns>
        private static async Task ProcessValueIntoActualTable(PublishModel data, string spName, string tableName, string query, DataTable tableData, bool isCap = false, bool isFundKpi= false)
        {
            await ImportHelper.ImportCreateTableQueryAtRuntime(tableName, query, data.ConnectionString);
            await ImportHelper.InsertDataQueryUsingSqlBulkCopy(tableName, tableData, data.ConnectionString);
            await ImportHelper.ImportStagingTblToActualTblQueryWithDocuments(data, spName, tableName, isCap, isFundKpi);
            await ImportHelper.ImportDropTableQueryAtRuntime(tableName, data.ConnectionString);
        }
        /// Retrieves the details of a fund based on the provided fund ID.
        /// </summary>
        /// <param name="fundId">The unique identifier of the fund.</param>
        /// <returns>
        /// A <see cref="FundDetailsModel"/> object containing the fund's details, 
        /// including its name, ID, currency information, and the count of associated companies.
        /// If the fund ID is 0 or the fund is not found, an empty <see cref="FundDetailsModel"/> is returned.
        /// </returns>
        /// <remarks>
        /// This method fetches the fund details from the repository and, if applicable, 
        /// retrieves the currency information associated with the fund. Additionally, 
        /// it queries the list of companies associated with the fund and calculates the company count.
        /// </remarks>
        public async Task<FundDetailsModel> GetFundDetails(int fundId)
        {
            var result = new FundDetailsModel();

            var fundDetails = await _dapperGenericRepository.QueryFirstAsync<FundDetailsModel>(SqlConstants.QueryByGetFundDetailById, new { @FundId = fundId });
            if (fundDetails != null)
            {
                result = _mapper.Map<FundDetailsModel>(fundDetails);
            }
            result.CurrencyList = await _dapperGenericRepository.Query<CurrencyListModel>(SqlConstants.QueryGetAllCurrency);
            return result;
        }
        /// <summary>
        /// Retrieves a list of companies and their associated KPIs for a given fund ID.
        /// </summary>
        /// <param name="fundId">The ID of the fund for which to retrieve companies and KPIs.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of <see cref="CompaniesAndKpiResponseModel"/>
        /// objects, each representing a company and its related KPI data. Returns an empty list if the fund ID is 0.
        /// </returns>
        public async Task<List<CompaniesAndKpiResponseModel>> GetCompaniesAndKpiByFundId(int fundId)
        {
            try
            {
                _logger.LogInformation("Retrieving companies and KPIs for fund ID: {FundId}", fundId);
                var result = new List<CompaniesAndKpiResponseModel>();
                if (fundId == 0)
                {
                    return result;
                }

                // Get portfolio company static fields
                var pcStaticFields = await _pageDetailConfigService.GetActiveFieldsByPageId((int)PageConfigurationFeature.PortfolioCompany);

                // Cache display names to avoid repeated lookups
                var tradingRecordsDisplayName = pcStaticFields.FirstOrDefault(x => x.Name == PortfolioSections.TradingRecords);
                var creditKpiDisplayName = pcStaticFields.FirstOrDefault(x => x.Name == PortfolioSections.CreditKPI);
                var staticInfoFields = pcStaticFields.Where(x => x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation).ToList();

                // Query for company list and KPI data in a single database call
                var queryResult = await _dapperGenericRepository.QueryWithTwoResultSets<CompanyListByFundModel, KpiByCompanyModel>(
                    SqlConstants.QueryByGetCompaniesAndKpiByFundId,
                    new { @FundId = fundId }
                );

                var companyList = queryResult.Item1;
                var allKpiList = queryResult.Item2;

                // Group KPIs by company and module for efficient access
                var kpisByCompanyAndModule = allKpiList
                    .GroupBy(k => new { k.CompanyId, k.ModuleId })
                    .ToDictionary(
                        g => g.Key,
                        g => g.ToList()
                    );

                int uniqueId = 0;

                foreach (var company in companyList)
                {
                    var model = new CompaniesAndKpiResponseModel
                    {
                        Id = ++uniqueId,
                        FieldId = company.CompanyId,
                        Text = company.CompanyName,
                        Items = new List<KpiItemsModel>()
                    };

                    // Add Trading Records KPIs
                    var tradingRecordModel = CreateKpiItemsModel(
                        ++uniqueId,
                        (int)KpiModuleType.TradingRecords,
                        tradingRecordsDisplayName?.DisplayName ?? string.Empty,
                        tradingRecordsDisplayName?.Name ?? string.Empty
                    );

                    // Get trading KPIs for current company if any exist
                    var tradingKpiKey = new { CompanyId = company.CompanyId, ModuleId = (int)KpiModuleType.TradingRecords };
                    if (kpisByCompanyAndModule.TryGetValue(tradingKpiKey, out var tradingKpis))
                    {
                        AddKpisToModel(tradingKpis, tradingRecordModel, ref uniqueId);
                    }
                    model.Items.Add(tradingRecordModel);

                    // Add Credit KPIs
                    var creditKpiModel = CreateKpiItemsModel(
                        ++uniqueId,
                        (int)KpiModuleType.CreditKPI,
                        creditKpiDisplayName?.DisplayName ?? string.Empty,
                        creditKpiDisplayName?.Name ?? string.Empty
                    );

                    // Get credit KPIs for current company if any exist
                    var creditKpiKey = new { CompanyId = company.CompanyId, ModuleId = (int)KpiModuleType.CreditKPI };
                    if (kpisByCompanyAndModule.TryGetValue(creditKpiKey, out var creditKpis))
                    {
                        AddKpisToModel(creditKpis, creditKpiModel, ref uniqueId);
                    }
                    model.Items.Add(creditKpiModel);

                    // Add Master Data
                    var masterData = CreateKpiItemsModel(
                        ++uniqueId,
                        uniqueId, // Using uniqueId as ItemId for master data
                        "Master Data",
                        "MasterData"
                    );

                    // Add static data fields
                    foreach (var item in staticInfoFields)
                    {
                        masterData.Items.Add(new KpiItemModel
                        {
                            Id = ++uniqueId,
                            KpiId = item.Id,
                            Text = item.DisplayName,
                            Name = item.Name
                        });
                    }
                    model.Items.Add(masterData);

                    result.Add(model);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving companies and KPIs for fund ID: {FundId}", fundId);
                return new List<CompaniesAndKpiResponseModel>();
            }
        }
        /// <summary>
        /// Creates a new KpiItemsModel with the specified properties
        /// </summary>
        private static KpiItemsModel CreateKpiItemsModel(int id, int itemId, string text, string name)
        {
            return new KpiItemsModel
            {
                Id = id,
                ItemId = itemId,
                Text = text,
                Name = name,
                Items = new List<KpiItemModel>()
            };
        }
        /// <summary>
        /// Maps and adds KPIs to the KPI items model
        /// </summary>
        private void AddKpisToModel(List<KpiByCompanyModel> kpis, KpiItemsModel model, ref int uniqueId)
        {
            foreach (var item in kpis)
            {
                var kpi = _mapper.Map<KpiItemModel>(item);
                kpi.Id = ++uniqueId;
                model.Items.Add(kpi);
            }
        }
        /// <summary>
        /// Bulk saves static fields data for multiple portfolio companies
        /// </summary>
        /// <param name="companiesData">List of company data with static fields</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>True if operation is successful; otherwise, false</returns>
        private async Task<bool> BulkSavePortfolioCompanyStaticFields(List<PCDataModel> companiesData, int userId)
        {
            try
            {
                _logger.LogInformation("Starting bulk processing of static fields data for {Count} companies", companiesData?.Count ?? 0);
                return await SavePortfolioCompanyStaticFields(companiesData, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk saving static fields data: {Message}", ex.Message);
                return false;
            }
        }
        /// <summary>
        /// Bulk saves static fields data for multiple portfolio companies
        /// </summary>
        /// <param name="companiesData">List of company data with static fields</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <returns>True if operation is successful; otherwise, false</returns>
        private async Task<bool> SavePortfolioCompanyStaticFields(List<PCDataModel> companiesData, int userId)
        {
            try
            {
                _logger.LogInformation("Starting bulk save of static fields data for {Count} companies", companiesData?.Count ?? 0);
                if (companiesData?.Count == 0)
                {
                    _logger.LogWarning("No company data provided for bulk saving");
                    return false;
                }

                var validCompanyData = companiesData.Where(c => c.StaticFields?.Count > 0).ToList();

                if (validCompanyData.Count == 0)
                {
                    _logger.LogWarning("No static fields data found in any of the companies");
                    return false;
                }

                var now = DateTime.UtcNow;
                var companyIds = validCompanyData.Select(c => c.CompanyId).ToHashSet();

                var companiesTask = GetCompaniesDetails(companyIds.ToList());
                var fieldMappingsTask = _unitOfWork.SubPageFieldsRepository.GetManyAsync(x => x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation && !x.IsDeleted);
                var customValuesTask = _unitOfWork.PageConfigurationFieldValueRepository.GetManyAsync(x => x.PageID == (int)PageConfigurationFeature.PortfolioCompany && companyIds.Contains(x.PageFeatureId) && x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation);

                await Task.WhenAll(companiesTask, fieldMappingsTask, customValuesTask);

                var portfolioCompanies = companiesTask.Result.ToDictionary(pc => pc.PortfolioCompanyId);
                var fieldMappings = fieldMappingsTask.Result.ToDictionary(f => f.FieldID, f => f.Name);
                var customValuesByCompany = customValuesTask.Result.GroupBy(x => x.PageFeatureId).ToDictionary(g => g.Key, g => g.ToDictionary(fv => fv.FieldID));

                var companiesToUpdate = new List<PortfolioCompanyDetails>();
                var customFieldsToCreate = new List<PageConfigurationFieldValue>();
                var customFieldsToUpdate = new List<PageConfigurationFieldValue>();

                foreach (var companyData in validCompanyData)
                {
                    if (!portfolioCompanies.TryGetValue(companyData.CompanyId, out var portfolioCompany))
                    {
                        _logger.LogWarning("Portfolio company with ID {CompanyId} not found", companyData.CompanyId);
                        continue;
                    }

                    customValuesByCompany.TryGetValue(companyData.CompanyId, out var existingCustomValuesDict);
                    existingCustomValuesDict ??= [];

                    foreach (var field in companyData.StaticFields.Where(f => !string.IsNullOrWhiteSpace(f?.FieldValue)))
                    {
                        if (!fieldMappings.TryGetValue(field.FieldId, out var fieldName))
                        {
                            _logger.LogWarning("Field mapping not found for FieldId: {FieldId}", field.FieldId);
                            continue;
                        }

                        SetCompanyStaticField(userId, customFieldsToCreate, customFieldsToUpdate, now, portfolioCompany, existingCustomValuesDict, field, fieldName, field.FieldValue);
                    }

                    portfolioCompany.ModifiedBy = userId;
                    portfolioCompany.ModifiedOn = now;
                    companiesToUpdate.Add(portfolioCompany);
                }

                await UpsertCompanyStaticAndCustomFieldsValue(companiesToUpdate, customFieldsToCreate, customFieldsToUpdate);

                _logger.LogInformation("Successfully bulk saved static fields data for {Count} companies", companiesToUpdate.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk saving static fields data: {Message}", ex.Message);
                return false;
            }
        }
        private async Task UpsertCompanyStaticAndCustomFieldsValue(List<PortfolioCompanyDetails> companiesToUpdate, List<PageConfigurationFieldValue> customFieldsToCreate, List<PageConfigurationFieldValue> customFieldsToUpdate)
        {
            if (companiesToUpdate.Count > 0)
            {
                _unitOfWork.PortfolioCompanyDetailRepository.UpdateBulk(companiesToUpdate);
            }
            if (customFieldsToCreate.Count > 0)
            {
                await _unitOfWork.PageConfigurationFieldValueRepository.AddBulkAsyn(customFieldsToCreate);
            }
            if (customFieldsToUpdate.Count > 0)
            {
                _unitOfWork.PageConfigurationFieldValueRepository.UpdateBulk(customFieldsToUpdate);
            }
            await _unitOfWork.SaveAsync();
        }
        private void SetCompanyStaticField(int userId, List<PageConfigurationFieldValue> customFieldsToCreate, List<PageConfigurationFieldValue> customFieldsToUpdate, DateTime now, PortfolioCompanyDetails portfolioCompany, Dictionary<int, PageConfigurationFieldValue> existingCustomValuesDict, StaticDataModel field, string fieldName, string fieldValue)
        {
            try
            {
                switch (fieldName)
                {
                    case Constants.CompanyName:
                        if (portfolioCompany.CompanyName != fieldValue)
                            portfolioCompany.CompanyName = fieldValue;
                        break;
                    case Constants.CompanyLegalName:
                        if (portfolioCompany.CompanyLegalName != fieldValue)
                            portfolioCompany.CompanyLegalName = fieldValue;
                        break;
                    case Constants.BusinessDescription:
                        if (portfolioCompany.BussinessDescription != fieldValue)
                            portfolioCompany.BussinessDescription = fieldValue;
                        break;
                    case Constants.CompanyStatus:
                        if (portfolioCompany.Status != fieldValue)
                            portfolioCompany.Status = fieldValue;
                        break;
                    case Constants.Sector:
                        if (int.TryParse(fieldValue, out int sectorId) && portfolioCompany.SectorId != sectorId)
                            portfolioCompany.SectorId = sectorId;
                        break;
                    case Constants.SubSector:
                        if (int.TryParse(fieldValue, out int subSectorId) && portfolioCompany.SubSectorId != subSectorId)
                            portfolioCompany.SubSectorId = subSectorId;
                        break;
                    case Constants.Currency:
                        if (int.TryParse(fieldValue, out int currencyId) && portfolioCompany.ReportingCurrencyId != currencyId)
                            portfolioCompany.ReportingCurrencyId = currencyId;
                        break;
                    case Constants.Website:
                        if (portfolioCompany.Website != fieldValue)
                            portfolioCompany.Website = fieldValue;
                        break;
                    case Constants.HeadquarterID:
                        if (int.TryParse(fieldValue, out int hqId) && portfolioCompany.HeadquarterId != hqId)
                            portfolioCompany.HeadquarterId = hqId;
                        break;
                    case Constants.StockExchange_Ticker:
                        if (portfolioCompany.StockExchangeTicker != fieldValue)
                            portfolioCompany.StockExchangeTicker = fieldValue;
                        break;
                    case Constants.Customfield:
                        if (existingCustomValuesDict.TryGetValue(field.FieldId, out var existingField))
                        {
                            if (existingField.FieldValue != field.FieldValue)
                            {
                                existingField.FieldValue = field.FieldValue;
                                existingField.ModifiedBy = userId;
                                existingField.ModifiedOn = now;
                                customFieldsToUpdate.Add(existingField);
                            }
                        }
                        else
                        {
                            customFieldsToCreate.Add(new PageConfigurationFieldValue
                            {
                                FieldID = field.FieldId,
                                FieldValue = field.FieldValue,
                                PageID = (int)PageConfigurationFeature.PortfolioCompany,
                                SubPageID = (int)PageConfigurationSubFeature.StaticInformation,
                                PageFeatureId = portfolioCompany.PortfolioCompanyId,
                                CreatedBy = userId,
                                CreatedOn = now,
                                IsActive = true,
                                IsDeleted = false
                            });
                        }
                        break;
                    default:
                        _logger.LogWarning("Unknown field name: {FieldName}, value not mapped", fieldName);
                        break;
                }
            }
            catch (FormatException ex)
            {
                _logger.LogError(ex, "Error converting value '{FieldValue}' for field '{FieldName}' to appropriate type", fieldValue, fieldName);
            }
        }
        /// <summary>
        ///  this will update the documents to document collection during ingestion publish
        /// </summary>
        /// <param name="ProcessId"></param>
        /// <param name="UserId"></param>
        /// <returns></returns>
        private async Task<bool> UpdateDocumentsToDocumentCollection(string ProcessId, int UserId)
        {
            try
            {
                Guid processguid;
                if (!Guid.TryParse(ProcessId, out processguid))
                {
                    return false;
                }
                _logger.LogInformation("Starting document collection update for process ID: {ProcessId}", ProcessId);
                var documentsToMove = await _dapperGenericRepository.Query<DataIngestionDocumentMappingModel>(SqlConstants.GetDocumentsConfigFromIngestionTable, new { DIProcessID = processguid.ToString() });
                int countofDocs = documentsToMove.Count;
                if (documentsToMove == null || !documentsToMove.Any())
                {
                    _logger.LogInformation("No documents found for process ID: {ProcessId}", ProcessId);
                    return false;
                }

                // Group documents by DocumentTypeId/Year/Month/Quarter to find unique documents
                // Using LINQ's GroupBy and ToDictionary to simplify the logic
                var uniqueDocuments = documentsToMove
                    .GroupBy(doc => $"{doc.DocumentTypeId}/{doc.Year}/{doc.Month}/{doc.Quarter}")
                    .ToDictionary(
                        group => group.Key,
                        group => group.ToList() // Take the first document from each group
                    );
                foreach (var document in uniqueDocuments)
                {
                    var firstdocument = document.Value.FirstOrDefault();
                    if (firstdocument != null && firstdocument.DocumentTypeId != null)
                    {
                        RepositoryDocumentMappingDetail repoMappingDetails = await UpdateFolderMapping(UserId, firstdocument);
                        var documentCollectionStores = document.Value.Select(doc => new DocumentCollectionStore
                        {
                            ID = Guid.NewGuid(),
                            SourceTypeId = doc.SourceTypeId,
                            Type = doc.Extension,
                            FileName = doc.FileName + doc.Extension,
                            S3Path = doc.S3Path,
                            FolderMappingId = repoMappingDetails.ID,
                            CreatedBy = UserId,
                            CreatedOn = DateTime.UtcNow,
                            IsDeleted = false,
                            UploadType =(int) UploadType.DataIngestion,
                        }).ToList();
                        await _unitOfWork.DocumentCollectionRepository.AddBulkAsyn(documentCollectionStores);
                        await _unitOfWork.SaveAsync();

                    }
                }
                _logger.LogInformation("Completed document collection update for process ID: {ProcessId} and moved {countofDocs} docs", ProcessId, countofDocs);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating documents to document collection for process ID: {ProcessId}", ProcessId);
                return false;
            }

        }
        /// <summary>
        /// This method updates the folder mapping for the documents that are going to uplaod into document collection.
        /// </summary>
        /// <param name="UserId"></param>
        /// <param name="firstdocument"></param>
        /// <returns></returns>
        private async Task<RepositoryDocumentMappingDetail> UpdateFolderMapping(int UserId, DataIngestionDocumentMappingModel firstdocument)
        {
            var repoMappingDetails = new RepositoryDocumentMappingDetail()
            {
                FeatureId = firstdocument.FeatureId,
                EntityId = firstdocument.CompanyId,
                DocTypeID = firstdocument.DocumentTypeId ?? 0,
                CreatedBy = UserId,
                CreatedOn = DateTime.UtcNow,
                IsDeleted = false
            };
            if (!string.IsNullOrEmpty(firstdocument.PeriodType))
            {
                if (firstdocument.PeriodType.ToLowerInvariant() == Constants.PeriodTypeYear)
                {
                    repoMappingDetails.Year = firstdocument.Year;
                }
                else if (firstdocument.PeriodType.ToLowerInvariant() == Constants.PeriodTypeQuarter)
                {
                    repoMappingDetails.Year = firstdocument.Year;
                    repoMappingDetails.Quarter = firstdocument.Quarter.Replace("Q","Quarter "); // quarter text save needs transformation
                }
                else if (firstdocument.PeriodType.ToLowerInvariant() == Constants.PeriodTypeMonth)
                {
                    repoMappingDetails.Year = firstdocument.Year;
                    repoMappingDetails.Month = firstdocument.Month;
                }
            }
            var existingMapping = await _unitOfWork.DocumentMappingRepository.FindAllAsync(d =>
                d.FeatureId == firstdocument.FeatureId &&
                d.EntityId == firstdocument.CompanyId &&
                !d.IsDeleted &&
                d.DocTypeID == repoMappingDetails.DocTypeID &&
                d.Year == repoMappingDetails.Year &&
                d.Quarter == repoMappingDetails.Quarter &&
                d.Month == repoMappingDetails.Month);
            if (existingMapping?.FirstOrDefault() != null)
            {
                repoMappingDetails.ID = existingMapping.FirstOrDefault()!.ID;
            }
            else
            {
                _unitOfWork.DocumentMappingRepository.Insert(repoMappingDetails);
                await _unitOfWork.SaveAsync();
            }

            return repoMappingDetails;
        }


    }
}
