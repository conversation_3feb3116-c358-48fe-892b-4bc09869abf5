using Contract.Configuration;
using Contract.Funds;
using Contract.KPI;
using Contract.Pdf;
using Report.Enums;
using Shared;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
namespace Report.LpReport.Helpers
{
    /// <summary>
    /// Provides utility methods for LP (Limited Partner) report generation and data manipulation.
    /// </summary>
    public static class LpUtilities
    {
        /// <summary>
        /// Converts a comma-separated string of integers to a List of integers.
        /// </summary>
        /// <param name="commaSeparatedString">The comma-separated string to convert.</param>
        /// <returns>A List of integers.</returns>
        public static List<int> ConvertCommaSeparatedStringToList(this string commaSeparatedString)
        {
            if (string.IsNullOrWhiteSpace(commaSeparatedString))
            {
                return new List<int>();
            }
            return commaSeparatedString
                               .Split(',')
                               .Select(int.Parse)
                               .ToList();
        }

        /// <summary>
        /// Tries to convert a string to a nullable decimal.
        /// </summary>
        /// <param name="value">The string value to convert.</param>
        /// <returns>A nullable decimal if the conversion is successful; otherwise, null.</returns>
        public static decimal? TryConvertToNullableDecimal(this string value)
        {
            return decimal.TryParse(value, out decimal result) ? result : (decimal?)null;
        }

        /// <summary>
        /// Converts a string value by a specified unit.
        /// </summary>
        /// <param name="actualValue">The string value to convert.</param>
        /// <param name="unit">The unit to use for conversion.</param>
        /// <returns>A nullable decimal representing the converted value.</returns>
        public static decimal? ConvertValueByUnit(this string actualValue, int unit)
        {
            decimal? value = actualValue.TryConvertToNullableDecimal();
            return value switch
            {
                null => null,
                _ => unit switch
                {
                    (int)ValueTypeFeature.Absolute => value,
                    (int)ValueTypeFeature.Thousands => value / 1000,
                    (int)ValueTypeFeature.Millions => value / 1000000,
                    (int)ValueTypeFeature.Billions => value / 1000000000,
                    _ => value / 1000000
                }
            };
        }

        /// <summary>
        /// Gets the unit suffix for a specified unit type.
        /// </summary>
        /// <param name="unitType">The unit type.</param>
        /// <returns>The unit suffix as a string.</returns>
        public static string GetUnitSuffix(int unitType)
        {
            return unitType switch
            {
                1 => "K", // Thousands
                2 => "Mn", // Millions
                3 => "Bn", // Billions
                _ => string.Empty
            };
        }

        /// <summary>
        /// Formats a value string based on KPI information and decimal places.
        /// </summary>
        /// <param name="value">The value string to format.</param>
        /// <param name="kpiInfo">The KPI information.</param>
        /// <param name="decimalPlaces">The number of decimal places.</param>
        /// <param name="isSymbol">Indicates whether to include a symbol.</param>
        /// <returns>The formatted value string.</returns>
        public static string FormatValues(string value, string kpiInfo, int decimalPlaces = 0, bool isSymbol = true)
        {
            if (string.IsNullOrEmpty(value) || value == Constants.NotAvailable)
                return Constants.NotAvailable;

            if (!decimal.TryParse(value, out decimal decimalValue))
                return value;

            decimal roundedValue = Math.Round(decimalValue, decimalPlaces, MidpointRounding.AwayFromZero);
            string formatString = decimalPlaces > 0 ? $"#,##0.{new string('0', decimalPlaces)}" : "#,##0";
            string formattedValue = roundedValue.ToString(formatString);
            if (isSymbol && (kpiInfo == Constants.KpiInfoPercent || kpiInfo == Constants.KpiInfoMultiple))
            {
                formattedValue += kpiInfo;
            }
            return formattedValue;
        }

        /// <summary>
        /// Custom page configuration format for a value string.
        /// </summary>
        /// <param name="val">The value string to format.</param>
        /// <param name="dataType">The data type.</param>
        /// <param name="decimalPlaces">The number of decimal places.</param>
        /// <returns>The formatted value string.</returns>
        public static string CustomPageConfigFormat(string val, int dataType, int decimalPlaces = 0)
        {
            if(dataType==(int)PageSubFieldsDatatTypes.FreeText)
            {
                return val;
            }
            val = val.Replace(Constants.KpiInfoMultiple, string.Empty).Replace(Constants.KpiInfoPercent, string.Empty).Replace(Constants.KpiInfoCurrency, string.Empty);
            return dataType switch
            {
                (int)PageSubFieldsDatatTypes.Number => FormatValues(val, Constants.KpiInfoNumber, decimalPlaces),
                (int)PageSubFieldsDatatTypes.Currency => FormatValues(val, Constants.KpiInfoCurrency, decimalPlaces),
                (int)PageSubFieldsDatatTypes.Percentage => FormatValues(val, Constants.KpiInfoPercent, decimalPlaces),
                (int)PageSubFieldsDatatTypes.Multiple => FormatValues(val, Constants.KpiInfoMultiple, decimalPlaces),
                (int)PageSubFieldsDatatTypes.Date => val.FormatDate(),
                _ => val,
            };
        }

        /// <summary>
        /// Formats a date string.
        /// </summary>
        /// <param name="val">The date string to format.</param>
        /// <returns>The formatted date string.</returns>
        public static string FormatDate(this string val)
        {
            if (!string.IsNullOrEmpty(val) && DateTime.TryParse(val, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dDate))
            {
                return dDate.ToString(HtmlConstants.DateFormatter, CultureInfo.InvariantCulture);
            }
            return val;
        }

        /// <summary>
        /// Formats a negative value string.
        /// </summary>
        /// <param name="originalValue">The original value string.</param>
        /// <param name="formattedValue">The formatted value string.</param>
        /// <param name="kpiInfo">The KPI information.</param>
        /// <returns>The formatted negative value string.</returns>
        public static string FormatNegativeValue(string originalValue, string formattedValue, string kpiInfo = Constants.KpiInfoMultiple)
        {
            if (double.TryParse(originalValue, out double numericValue) && numericValue < 0)
            {
                return $"({formattedValue.TrimStart('-').Trim()})";
            }
            return formattedValue.Trim();
        }

        /// <summary>
        /// Gets the CSS class for bold text based on a boolean value.
        /// </summary>
        /// <param name="isBold">Indicates whether the text should be bold.</param>
        /// <returns>The CSS class for bold text.</returns>
        public static string GetKpiBoldClass(this bool isBold)
        {
            return isBold ? HtmlConstants.BoldText : string.Empty;
        }

        /// <summary>
        /// Removes value type strings from a period type string.
        /// </summary>
        /// <param name="periodType">The period type string.</param>
        /// <returns>The modified period type string.</returns>
        public static string RemoveValueTypeString(string periodType)
        {
            var replacements = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { Constants.ActualString, "A" },
                { Constants.BudgetString, "B" },
                { Constants.ForecastString, "F" },
                { Constants.ICString, "IC" },
            };
            var replacement = replacements
            .Where(r => periodType.Contains(r.Key, StringComparison.OrdinalIgnoreCase))
            .FirstOrDefault();
            if (!replacement.Equals(default(KeyValuePair<string, string>)))
            {
                return periodType.Replace(replacement.Key, replacement.Value, StringComparison.OrdinalIgnoreCase).Trim();
            }

            return periodType;
        }

        /// <summary>
        /// Gets a dictionary mapping KPI strings to their order values.
        /// </summary>
        /// <returns>A dictionary mapping KPI strings to their order values.</returns>
        public static Dictionary<string, int> GetOrderMap()
        {
            return new Dictionary<string, int>
            {
                {Constants.ActualString, KpiOrderConstants.ActualOrder },
                {Constants.BudgetString, KpiOrderConstants.BudgetOrder },
                {Constants.ForecastString, KpiOrderConstants.ForecastOrder },
                {Constants.ICString, KpiOrderConstants.ICOrder },
                {KpiOrderConstants.ActualLTM, KpiOrderConstants.ActualLTMOrder },
                {KpiOrderConstants.BudgetLTM, KpiOrderConstants.BudgetLTMOrder },
                {KpiOrderConstants.ForecastLTM, KpiOrderConstants.ForecastLTMOrder },
                {KpiOrderConstants.ActualYTD, KpiOrderConstants.ActualYTDOrder },
                {KpiOrderConstants.BudgetYTD, KpiOrderConstants.BudgetYTDOrder },
                {KpiOrderConstants.ForecastYTD, KpiOrderConstants.ForecastYTDOrder }
            };
        }

        /// <summary>
        /// Gets a dictionary mapping LP sections to their static information sub-feature IDs.
        /// </summary>
        /// <returns>A dictionary mapping LP sections to their static information sub-feature IDs.</returns>
        public static Dictionary<LpSection, int> GetStaticInformationMap()
        {
            return new Dictionary<LpSection, int>
            {
                { LpSection.CompanyName, (int)PageConfigurationSubFeature.StaticInformation },
                { LpSection.CompanyLogo, (int)PageConfigurationSubFeature.StaticInformation },
                { LpSection.StaticInformation, (int)PageConfigurationSubFeature.StaticInformation },
                { LpSection.BusinessDescription, (int)PageConfigurationSubFeature.StaticInformation }
            };
        }

        /// <summary>
        /// Gets a dictionary mapping LP sections to their investment professionals sub-feature IDs.
        /// </summary>
        /// <returns>A dictionary mapping LP sections to their investment professionals sub-feature IDs.</returns>
        public static Dictionary<LpSection, int> GetInvestmentProfessionalsMap()
        {
            return new Dictionary<LpSection, int>
            {
                { LpSection.InvestmentProfessionals, (int)PageConfigurationSubFeature.InvestmentProfessionals }
            };
        }

        /// <summary>
        /// Gets a dictionary mapping LP sections to their commentary sub-feature IDs.
        /// </summary>
        /// <returns>A dictionary mapping LP sections to their commentary sub-feature IDs.</returns>
        public static Dictionary<LpSection, int> GetCommentaryMap()
        {
            return new Dictionary<LpSection, int>
            {
                { LpSection.Commentary, (int)PageConfigurationSubFeature.Commentary }
            };
        }

        /// <summary>
        /// Gets a dictionary mapping LP sections to their geographic locations sub-feature IDs.
        /// </summary>
        /// <returns>A dictionary mapping LP sections to their geographic locations sub-feature IDs.</returns>
        public static Dictionary<LpSection, int> GetGeographicLocationsMap()
        {
            return new Dictionary<LpSection, int>
            {
                { LpSection.GeographicLocations, (int)PageConfigurationSubFeature.GeographicLocations }
            };
        }

        /// <summary>
        /// Gets a merged dictionary of LP sections and their sub-feature IDs.
        /// </summary>
        /// <returns>A merged dictionary of LP sections and their sub-feature IDs.</returns>
        public static Dictionary<LpSection, int> GetPortfolioMergedMap()
        {
            return GetStaticInformationMap()
                .Concat(GetInvestmentProfessionalsMap())
                .Concat(GetCommentaryMap())
                .Concat(GetGeographicLocationsMap())
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Gets a list of portfolio page configuration models based on company ID and field IDs.
        /// </summary>
        /// <param name="portfolioDetails">The list of portfolio page configuration models.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="fieldIds">The field IDs.</param>
        /// <returns>A list of portfolio page configuration models.</returns>
        public static List<PortfolioPageConfigModel> GetFields(
           List<PortfolioPageConfigModel> portfolioDetails,
           int companyId,
           string fieldIds)
        {
            if (portfolioDetails == null || string.IsNullOrEmpty(fieldIds))
            {
                return new List<PortfolioPageConfigModel>();
            }

            var companyDetails = portfolioDetails
                .Where(x => x.CompanyId == companyId)
                .ToList();

            if (!companyDetails.Any())
            {
                return new List<PortfolioPageConfigModel>();
            }
            List<int> configFields = fieldIds.ConvertCommaSeparatedStringToList();
            return companyDetails
                .Select(cd => {
                    cd.PageConfigMappedFields = cd.PageConfigMappedFields
                        .Where(x => configFields.Contains(x.FieldID))
                        .OrderBy(x => x.Sequence)
                        .ToList();
                    return cd;
                })
                .Where(cd => cd.PageConfigMappedFields.Any())
                .ToList();
        }

        /// <summary>
        /// Gets a list of page field value models for a company based on static data, company ID, and field IDs.
        /// </summary>
        /// <param name="staticData">The static data model.</param>
        /// <param name="companyId">The company ID.</param>
        /// <param name="fieldIds">The field IDs.</param>
        /// <returns>A list of page field value models.</returns>
         public static List<PageFieldValueModel> GetCompanyFields(LpReportConfigDataModel staticData, int companyId, string fieldIds)
        {
            if (staticData == null || string.IsNullOrEmpty(fieldIds))
            {
                return new List<PageFieldValueModel>();
            }
            var portfolioCompanyDetails = staticData.PortfolioCompanyDetails
                                                 .Where(x => x.CompanyId == companyId)
                                                 .ToList();
            if (!portfolioCompanyDetails.Any())
            {
                return new List<PageFieldValueModel>();
            }
            List<int> configFields = fieldIds.ConvertCommaSeparatedStringToList();
            var allFields = portfolioCompanyDetails[0]?.PageConfigMappedFields
                                                    .Where(x => configFields.Contains(x.FieldID))
                                                    .OrderBy(x => x.Sequence)
                                                    .ToList();
            return allFields;
        }

        /// <summary>
        /// Gets a list of sub-feature IDs based on mapped LP report configurations and a section-to-sub-feature map.
        /// </summary>
        /// <param name="mappedLpReportConfigs">The list of mapped LP report configurations.</param>
        /// <param name="sectionToSubFeatureMap">The section-to-sub-feature map.</param>
        /// <returns>A list of sub-feature IDs.</returns>
        public static List<int> GetSubFeatureIds(List<MappedLpReportConfigModel> mappedLpReportConfigs, Dictionary<LpSection, int> sectionToSubFeatureMap)
        {
            return mappedLpReportConfigs
                .Where(config => Enum.IsDefined(typeof(LpSection), config.SectionId) &&
                                 sectionToSubFeatureMap.TryGetValue((LpSection)config.SectionId, out _))
                .Select(config => sectionToSubFeatureMap[(LpSection)config.SectionId])
                .ToList();
        }

        /// <summary>
        /// Gets the log path for a company based on its ID.
        /// </summary>
        /// <param name="companyId">The company ID.</param>
        /// <returns>The log path as a string.</returns>
        public static string GetLogPath(int companyId)
        {
            return $"{LpReportConstants.LogsPath}{companyId}/";
        }
        /// <summary>
        /// Converts a date string from "MM/dd/yyyy" format to "MMMM yyyy" format.
        /// </summary>
        /// <param name="date">The date string to convert.</param>
        /// <returns>
        /// A string representing the date in "MMMM yyyy" format if the conversion is successful;
        /// otherwise, returns a constant indicating that the data is not available.
        /// </returns>
        public static string ConvertToMonthYear(this string date)
        {
            if (string.IsNullOrWhiteSpace(date))
            {
                return Constants.NotAvailable;
            }
            if (DateTime.TryParseExact(date, HtmlConstants.DateFormatter, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
            {
                return parsedDate.ToString(HtmlConstants.DateFormatMonthYear, CultureInfo.InvariantCulture);
            }
            return Constants.NotAvailable;
        }
        public static int GetCustomDecimalPlaces(this int dataTypeId) =>
        dataTypeId == (int)PageSubFieldsDatatTypes.Number
        ? KpiOrderConstants.DecimalPlacesDefault
        : KpiOrderConstants.SingleDecimal;
        /// <summary>
        /// Determines the appropriate currency code based on module type and client requirements
        /// </summary>
        /// <param name="moduleId">The module identifier</param>
        /// <param name="kpiTableHeader">The KPI table header currency information</param>
        /// <param name="clientCode">The client code for client-specific customizations</param>
        /// <returns>The appropriate currency code string based on client and module settings</returns>
        public static string GetCurrencyCodeByModule(this KpiTableHeaderCurrency kpiTableHeader, int moduleId, string clientCode)
        {
            if (!string.IsNullOrEmpty(clientCode) &&
                clientCode.Equals(HtmlConstants.ClientCodeHimera, StringComparison.OrdinalIgnoreCase)&&
                 (moduleId == (int)Audit.Enums.KpiModuleType.TradingRecords || moduleId == (int)Audit.Enums.KpiModuleType.Impact))
            {
                return kpiTableHeader?.FundCurrencyCode;
            }
            return moduleId == (int)Audit.Enums.KpiModuleType.Investment
                ? kpiTableHeader?.FundCurrencyCode
                : kpiTableHeader?.CurrencyCode;
        }

    }
}
