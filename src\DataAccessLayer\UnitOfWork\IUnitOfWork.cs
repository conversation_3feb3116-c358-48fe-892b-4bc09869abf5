﻿using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DataCollection;
using DataAccessLayer.Models.Documents;
using DataAccessLayer.Models.MonthlyReport;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.Models.Workflow;
using DataAccessLayer.Models.LpReport;
using System.Threading.Tasks;
using DataAccessLayer.Models.GrowthReport;
using DataAccessLayer.Models.SDG;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.CLO.Clo_Details;
using DataAccessLayer.Models.CLO.CloCommentries;
using DataAccessLayer.Models.CLO.CLOPageConfig;
using DataAccessLayer.Models.CLO.InvestmentCompany;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.Models.Fund;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.Models.DashboardTracker;

namespace DataAccessLayer.UnitOfWork {
    public partial interface IUnitOfWork {

        /// <summary>
        /// Save method.
        /// </summary>
        int Save ();
        Task<int> SaveAsync ();

        bool AutoDetectChangesEnabled { get; set; }

        #region   Repository Creation properties...
        /// <summary>
        /// Get/Set Property for M_ImpactKPI repository.
        /// </summary>
        IGenericRepository<MImpactKpi> M_ImpactKPIRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for PCInvestmentKpiQuarterlyValue repository.
        /// </summary>
        IGenericRepository<PCInvestmentKpiQuarterlyValue> PCInvestmentKpiQuarterlyValueRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PCCompanyKpiMonthlyValue repository.
        /// </summary>
        IGenericRepository<PCCompanyKpiMonthlyValue> PCCompanyKpiMonthlyValueRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for M_CompanyKPI repository.
        /// </summary>
        IGenericRepository<M_CompanyKpi> M_CompanyKPIRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for KPI Company Mapping repository.
        /// </summary>
        IGenericRepository<MappingPortfolioCompanyKpi> PortfolioCompanyKpiMappingRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for KPI Company Mapping repository.
        /// </summary>
        IGenericRepository<MappingPortfolioInvestmentKpi> PortfolioInvestmentKpiMappingRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for KPI Types repository.
        /// </summary>
        IGenericRepository<M_Kpitypes> KPITypesRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for Login attempts repository.
        /// </summary>
        IGenericRepository<LoginDetails> LoginAttemptRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Currency Rate repository.
        /// </summary>
        IGenericRepository<CurrencyRates> CurrencyRateRepository {
            get;
        }
        IGenericRepository<ApiCurrencyRates> ApiCurrencyRateRepository
        {
            get;
        }

        /// <summary>
        /// Get/Set Property for Audit Log repository.
        /// </summary>
        IGenericRepository<AuditLog> AuditLogRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Group repository.
        /// </summary>
        IGenericRepository<EmailTemplate> EmailTemplateRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for Region Country Mapping View repository.
        /// </summary>
        IGenericRepository<View_RegionCountryMapping> View_RegionCountryMappingRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Location Mapping View repository.
        /// </summary>
        IGenericRepository<View_LocationMapping> View_LocationMappingRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Group repository.
        /// </summary>
        IGenericRepository<CashFlowFileLogs> CashflowRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for pipeline status repository.
        /// </summary>
        IGenericRepository<M_PipelineStatus> PipelineStatusRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for pipeline repository.
        /// </summary>
        IGenericRepository<PipelineDetails> PipelineRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for dynamic queries repository.
        /// </summary>
        IGenericRepository<DynamicQueries> DynamicQueryRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for dynamic queries copy repository.
        /// </summary>
        IGenericRepository<DynamicQueriesCopy> DynamicQueryCopyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for firm headquarter repository.
        /// </summary>
        IGenericRepository<Mapping_FirmHeadquarterLocation> FirmHeadquarterRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for firm employee repository.
        /// </summary>
        IGenericRepository<Mapping_FirmGeographicLocation> FirmGeographyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for firm employee repository.
        /// </summary>
        IGenericRepository<Mapping_PCGeographicLocation> PCGeographyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for User repository.
        /// </summary>
        IGenericRepository<UserDetails> UserRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for country repository.
        /// </summary>
        IGenericRepository<M_Country> M_CountryRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for region repository.
        /// </summary>
        IGenericRepository<M_Region> M_RegionRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for stockexchange repository.
        /// </summary>
        IGenericRepository<M_StockExchange> M_StockExchangeRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for sector repository.
        /// </summary>
        IGenericRepository<M_Sector> M_SectorRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for FinancialStatus repository.
        /// </summary>
        IGenericRepository<M_FinancialStatus> M_FinancialStatusRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for OwnershipStatus repository.
        /// </summary>
        IGenericRepository<M_OwnershipStatus> M_OwnershipStatusRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for InvestmentStage repository.
        /// </summary>
        IGenericRepository<M_InvestmentStage> M_InvestmentStageRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PortfolioCompanyDetail repository .
        /// </summary>
        IGenericRepository<PortfolioCompanyDetails> PortfolioCompanyDetailRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for CommentaryDetails repository.
        /// </summary>
        IGenericRepository<CommentaryDetails> CommentaryDetailsRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for CommentaryDetails repository.
        /// </summary>
        IGenericRepository<PortfolioCompanyCommentaryDraft> PortfolioCompanyCommentaryDraftRepository
        {
            get;
        }

        /// <summary>
        ///  Get/Set Property for CommentaryDetails repository.
        /// </summary>
        IGenericRepository<PcCustomCommentaryDraft> PcCustomCommentaryDraftRepository
        {
            get;
        }
        /// <summary>
        /// Get/Set Property for company summary detail repository .
        /// </summary>
        IGenericRepository<CompanySummaryDetails> CompanySummaryDetailRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for Currency repository.
        /// </summary>
        IGenericRepository<M_Currency> M_CurrencyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Mapping_CountryCurrency repository.
        /// </summary>
        IGenericRepository<Mapping_CountryCurrency> Mapping_CountryCurrencyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_FirmType repository.
        /// </summary>
        IGenericRepository<M_FirmType> Mapping_M_FirmTypeRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_State repository.
        /// </summary>
        IGenericRepository<M_State> M_StateRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_City repository.
        /// </summary>
        IGenericRepository<M_City> M_CityRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for FirmDetail repository.
        /// </summary>
        IGenericRepository<FirmDetails> FirmDetailRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for Group repository.
        /// </summary>
        IGenericRepository<M_Groups> GroupRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Financial KPI repository.
        /// </summary>
        IGenericRepository<MFinancialKpi> M_FinancialKPIRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Operational KPI repository.
        /// </summary>
        IGenericRepository<M_OperationalKPI> M_OperationalKPIRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Group repository.
        /// </summary>
        IGenericRepository<M_Features> FeatureRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for UserGroup repository.
        /// </summary>
        IGenericRepository<Mapping_UserGroup> Mapping_UserGroupRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Feature Group repository.
        /// </summary>
        IGenericRepository<Mapping_GroupFeature> Mapping_GroupFeatureRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_Designation repository.
        /// </summary>
        IGenericRepository<M_Designation> M_DesignationRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for EmployeeDetail repository.
        /// </summary>
        IGenericRepository<EmployeeDetails> EmployeeDetailRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for FirmEmployeeDetail repository.
        /// </summary>
        IGenericRepository<Mapping_FirmEmployee> FirmEmployeeDetailRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for  portfolio company EmployeeDetail repository.
        /// </summary>
        IGenericRepository<Mapping_PCEmployee> PCEmployeeDetailRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_Strategy repository.
        /// </summary>
        IGenericRepository<M_Strategy> M_StrategyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for FundDetail repository.
        /// </summary>
        IGenericRepository<FundDetails> FundDetailRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for Mapping_FirmFund repository.
        /// </summary>
        IGenericRepository<Mapping_FirmFund> Mapping_FirmFundRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PortfolioCompanyFundHoldingDetail repository.
        /// </summary>
        IGenericRepository<PortfolioCompanyFundHoldingDetails> PortfolioCompanyFundHoldingDetailRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PortfolioCompanyProfitabilityDetail repository.
        /// </summary>
        IGenericRepository<PortfolioCompanyProfitabilityDetails> PortfolioCompanyProfitabilityDetailRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for DealDetail repository.
        /// </summary>
        IGenericRepository<DealDetails> DealDetailRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_Geography repository.
        /// </summary>
        IGenericRepository<M_Geography> M_GeographyRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_AccountType repository.
        /// </summary>
        IGenericRepository<M_AccountType> M_AccountTypeRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_FundingType repository.
        /// </summary>
        IGenericRepository<M_FundingType> FundingTypeRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for CompanyFundingDetails repository.
        /// </summary>
        IGenericRepository<CompanyFundingDetails> CompanyFundingDetailsRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for GlobalVariable repository.
        /// </summary>
        IGenericRepository<GlobalVariables> GlobalVariableRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Mapping_Locations repository.
        /// </summary>
        IGenericRepository<Mapping_Locations> Mapping_LocationRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for M_SubSector repository.
        /// </summary>
        IGenericRepository<M_SubSector> M_SubSectorRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealBoardSeatRepository repository.
        /// </summary>
        IGenericRepository<M_DealBoardSeat> M_DealBoardSeatRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealExitMethod repository.
        /// </summary>
        IGenericRepository<M_DealExitMethod> M_DealExitMethodRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealInvestmentStage repository.
        /// </summary>
        IGenericRepository<M_DealInvestmentStage> M_DealInvestmentStageRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealSecurtyType repository.
        /// </summary>
        IGenericRepository<M_DealSecurtyType> M_DealSecurtyTypeRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealSourcing repository.
        /// </summary>
        IGenericRepository<M_DealSourcing> M_DealSourcingRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealTransactionRole repository.
        /// </summary>
        IGenericRepository<M_DealTransactionRole> M_DealTransactionRoleRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_DealValuationMethodology repository.
        /// </summary>
        IGenericRepository<M_DealValuationMethodology> M_DealValuationMethodologyRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for M_DealValuationMethodology repository.
        /// </summary>
        IGenericRepository<M_FundHoldingStatus> M_FundHoldingStatusRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for FundTrackRecord repository.
        /// </summary>
        IGenericRepository<FundTrackRecord> FundTrackRecordRepository {
            get;
        }
        IGenericRepository<FundIngestion> FundIngestionRepository
        {
            get;
        }

        /// <summary>
        /// Get/Set Property for M_SectorwiseKPI repository.
        /// </summary>
        IGenericRepository<M_SectorwiseKPI> M_SectorwiseKPIRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PortfolioCompanySectorwiseKPIValue repository.
        /// </summary>
        IGenericRepository<PortfolioCompanySectorwiseKPIValues> PortfolioCompanySectorwiseKPIValueRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PCOperationalKPIMonthlyValue repository.
        /// </summary>
        IGenericRepository<PCOperationalKPIMonthlyValue> PCOperationalKPIMonthlyValueRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for PCFinancialKPIMonthlyValue repository.
        /// </summary>
        IGenericRepository<PCFinancialKPIMonthlyValue> PCFinancialKPIMonthlyValueRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PCCompanywiseKPIMonthlyValue repository.
        /// </summary>
        IGenericRepository<PcCompanywiseKpiMonthlyValue> PCCompanywiseKPIMonthlyValueRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PCSectorwiseKPIMonthlyValue repository.
        /// </summary>
        IGenericRepository<PCSectorwiseKPIMonthlyValue> PCSectorwiseKPIMonthlyValueRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PortfolioCompanySectorwiseKPIQuarter repository.
        /// </summary>
        IGenericRepository<PortfolioCompanySectorwiseKPIMonthly> PortfolioCompanySectorwiseKPIQuarterRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for KPIValuesFileLogs repository.
        /// </summary>
        IGenericRepository<KPIValuesFileLogs> KPIValuesFileRepository {
            get;
        }

        IGenericRepository<DataAuditLog> DataAuditLogRepository {
            get;
        }

        IGenericRepository<PortfolioCompanyOperationalKPIQuarter> PortfolioCompanyOperationalKPIQuarterRepository {
            get;
        }
        IGenericRepository<PortfolioCompanyOperationalKPIValue> PortfolioCompanyOperationalKPIValueRepository
        {
            get;
        }
        /// <summary>
        ///  Get/Set Property for BalanceSheetValues repository.
        /// </summary>
        IGenericRepository<BalanceSheetValues> BalanceSheetValuesRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for M_ProfitAndLoss_LineItems repository.
        /// </summary>
        IGenericRepository<M_ProfitAndLoss_LineItems> M_ProfitAndLoss_LineItemsRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for Mapping_CompanyProfitAndLossLineItems repository.
        /// </summary>
        IGenericRepository<Mapping_CompanyProfitAndLossLineItems> Mapping_CompanyProfitAndLossLineItemsRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for Mapping_EsgKpiRepository repository.
        /// </summary>
        IGenericRepository<Mapping_EsgKpi> Mapping_EsgKpiRepository
        {
            get;
        }


        /// <summary>
        ///  Get/Set Property for M_BalanceSheet_LineItems repository.
        /// </summary>
        IGenericRepository<M_BalanceSheet_LineItems> M_BalanceSheet_LineItemsRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for Mapping_CompanyBalanceSheetLineItems repository.
        /// </summary>
        IGenericRepository<Mapping_CompanyBalanceSheetLineItems> Mapping_CompanyBalanceSheetLineItemsRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for ProfitAndLossValues repository.
        /// </summary>
        IGenericRepository<ProfitAndLossValues> ProfitAndLossValuesRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for PCFinancialsValues repository.
        /// </summary>
        IGenericRepository<PCFinancialsValues> PCFinancialsValuesRepository
        {
            get;
        }
        /// <summary>
        ///  Get/Set Property for M_CashFlow_LineItems repository.
        /// </summary>
        IGenericRepository<M_CashFlow_LineItems> M_CashFlow_LineItemsRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for Mapping_CompanyCashFlowLineItems repository.
        /// </summary>
        IGenericRepository<Mapping_CompanyCashFlowLineItems> Mapping_CompanyCashFlowLineItemsRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for CashFlowValues repository.
        /// </summary>
        IGenericRepository<CashFlowValues> CashFlowValuesRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PCImpactKpiMonthlyValue repository.
        /// </summary>
        IGenericRepository<PcImpactKpiQuarterlyValue> PCImpactKpiQuarterlyValueRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for PCTradingRecordValue repository.
        /// </summary>
        IGenericRepository<PCTradingRecordValues> PCTradingRecordValuesRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for M_InvestmentKPI repository.
        /// </summary>
        IGenericRepository<M_InvestmentKpi> M_InvestmentKPIRepository {
            get;
        }
        /// <summary>
        ///  Get/Set Property for Mapping_ImpactKPI_Order repository.
        /// </summary>
        IGenericRepository<Mapping_ImpactKPI_Order> Mapping_ImpactKPI_OrderRepository {
            get;
        }

        /// <summary>
        ///  Get/Set Property for MappingPortfolioOperationalKpi repository.
        /// </summary>
        IGenericRepository<MappingPortfolioOperationalKpi> MappingPortfolioOperationalKpi_OrderRepository
        {
            get;
        }
        /// <summary>
        /// Get/Set Property for Group repository.
        /// </summary>
        IGenericRepository<M_SubFeature> M_SubFeatureRepository {
            get;
        }

        /// <summary>
        /// Get/Set Property for Mapping_SubFeatureActionRepository.
        /// </summary>
        IGenericRepository<Mapping_SubFeatureAction> Mapping_SubFeatureActionRepository
        {
            get;
        }
        /// <summary>
        /// Get/Set Property for User Sub Feature repository.
        /// </summary>
        IGenericRepository<Mapping_UserSubFeature> Mapping_UserSubFeatureRepository
        {
            get;
        }
        /// <summary>
        ///  Get/Set Property for ImpactKPI_AnnualHistoricalData repository.
        /// </summary>
        IGenericRepository<ImpactKPI_AnnualHistoricalData> ImpactKPI_AnnualHistoricalDataRepository {
            get;
        }
        /// <summary>
        /// Get/Set Property for StandingData Items repository.
        /// </summary>
        IGenericRepository<M_StandingDataItems> M_StandingDataItemsRepository {
            get;
        }

        IGenericRepository<DocumentType> DocumentTypesRepository {
            get;
        }
        IGenericRepository<Document> DocumentsRepository { get; }
        IGenericRepository<DocumentStatus> DocumentStatusRepository
        {
            get;
        }
        IGenericRepository<UserReport> UserReportsRepository { get; }
        IGenericRepository<M_LPReportConfiguration> M_LPReportConfigurationRepository { get; }
        IGenericRepository<Mapping_LPReportConfiguration> Mapping_LPReportConfigurationRepository { get; }
        IGenericRepository<M_ValueTypes> M_ValueTypesRepository { get; }
        IGenericRepository<M_KpiModules> M_KpiModulesRepository { get; }
        IGenericRepository<M_MasterKpis> M_MasterKpisRepository { get; }
        IGenericRepository<MasterKpiAuditLog> MasterKpiAuditLogRepository { get; }
        IGenericRepository<ImpactKpiAuditLog> ImpactKpiAuditLogRepository { get; }
        IGenericRepository<Mapping_Kpis> Mapping_KpisRepository { get; }
        IGenericRepository<PCMasterKpiValues> PCMasterKpiValuesRepository { get; }
        IGenericRepository<M_Methodology> M_MethodologyRepository { get; }
        IGenericRepository<M_Notification> M_NotificationRepository { get; }
        IGenericRepository<M_NotificationModules> M_NotificationModulesRepository { get; }
        IGenericRepository<BalanceSheetICCaseValues> BalanceSheetICCaseValuesRepository { get; }
        IGenericRepository<BalanceSheetForecastData> BalanceSheetForecastDataRepository { get; }
        IGenericRepository<ProfitAndLossIccaseValues> ProfitAndLossIccaseValuesRepository { get; }
        IGenericRepository<ProfitAndLossForecastData> ProfitAndLossForecastDataRepository { get; }
        IGenericRepository<CashFlowICCaseValues> CashFlowICCaseValuesRepository { get; }
        IGenericRepository<CashFlow_ForecastData> CashFlow_ForecastDataRepository { get; }
        IGenericRepository<FinancialValueTypes> FinancialValueTypesRepository { get; }
        IGenericRepository<PageConfiguration> ReportPageConfigurationRepository { get; }
        IGenericRepository<M_PageDetails> PageDetailsRepository { get; }
        IGenericRepository<M_SubPageDetails> SubPageDetailsRepository { get; }
        IGenericRepository<M_SubPageFields> SubPageFieldsRepository { get; }
        IGenericRepository<ReportTemplateConfiguration> ReportTemplateRepository { get; }
        IGenericRepository<MappingWorkflowStatus> MappingWorkflowStatusRepository { get; }
        IGenericRepository<MWorkflowStatus> MWorkflowStatusRepository { get; }
        IGenericRepository<WorkflowRequest> WorkflowRequestRepository { get; }
        IGenericRepository<MappingWorkflowRequest> MappingWorkflowRequestRepository { get; }
        IGenericRepository<MasterGroups> MasterGroupsRepository { get; }
        IGenericRepository<MappingWorkflowRequestComments> MappingWorkflowRequestCommentsRepository { get; }
        IGenericRepository<PortfolioCompanyDetailsDraft> PortfolioCompanyDetailsDraftRepository { get; }
        IGenericRepository<ReportTemplateConfigurationMapping> ReportTemplateMappingRepository { get; }
        IGenericRepository<PeriodTypes> PeriodTypesRepository { get; }

        IGenericRepository<MappingPCGeographicLocationDraft> MappingPCGeographicLocationDraftRepository { get; }
        IGenericRepository<MappingPCEmployeeDraft> MappingPCEmployeeDraftRepository { get; }
        IGenericRepository<WorkflowHistory> WorkflowHistoryRepository { get; }
        IGenericRepository<EmployeeDetailsDraft> EmployeeDetailsDraftRepository { get; }
        IGenericRepository<MappingWorkflowStatusGroup> MappingWorkflowStatusGroupRepository { get; }
        IGenericRepository<PCCompanyKpiMonthlyValueDraft> PCCompanyKpiMonthlyValueDraftRepository { get; }
        IGenericRepository<PCInvestmentKpiQuarterlyValueDraft> PCInvestmentKpiQuarterlyValueDraftRepository { get; }
        IGenericRepository<M_Action> MActionRepository { get; }
        IGenericRepository<DraftAuditLog> DraftAuditLogRepository { get; }
        IGenericRepository<ReportTypes> ReportTypesRepository { get; }
        IGenericRepository<PortfolioCompanyOperationalKPIQuartersDraft> PCOperationalKpiQuarterDraftRepository { get; }
        IGenericRepository<PortfolioCompanyOperationalKPIValuesDraft> PCOperationalKpiValuesDraftRepository { get; }

        IGenericRepository<M_FundReportConfiguration> M_FundReportConfigurationRepository { get; }
        IGenericRepository<FundReportTemplateConfiguration> FundReportTemplateConfigurationRepository { get; }
        IGenericRepository<FundReportTemplateConfigurationMapping> FundReportTemplateConfigurationMappingRepository { get; }
        IGenericRepository<WorkflowMappingHistory> WorkflowMappingHistoryRepository { get; }
        IGenericRepository<PageConfigurationFieldValue> PageConfigurationFieldValueRepository { get; }
        IGenericRepository<PageConfigurationTrackRecordFieldValue> PageConfigurationTrackRecordFieldValueRepository { get; }
        IGenericRepository<M_TrackRecordDataTypes> M_TrackRecordDataTypesRepository { get; }
        IGenericRepository<MInvestorType> M_InvestorTypeRepository { get; }
        IGenericRepository<InvestorType> InvestorTypeRepositoryRepository { get; }
        IGenericRepository<MappingInvestorGeographicLocation> MappingInvestor_GeographicLocationRepositoryRepository { get; }
        IGenericRepository<FundInvestors> FundInvestorsRepository { get; }
        IGenericRepository<InvestorValuation> InvestorValuationRepositoryRepository { get; }
        IGenericRepository<CompanyKPIForecastValues> CompanyKPIForecastValuesRepository { get; }
        IGenericRepository<UnstructuredHistory> UnstructuredMappingHistoryRepository { get; }
        IGenericRepository<PageConfigurationFieldValueDraft> PageConfigurationFieldValueDraftRepository { get; }
        IGenericRepository<FootNotes> FootNotesRepository { get; }
        IGenericRepository<InternalReportConfiguration> InternalReportConfigurationRepository { get; }
        IGenericRepository<MappingInternalReportConfiguration> MappingInternalReportConfigurationRepository { get; }
        IGenericRepository<InternalReportFundPreference> InternalReportFundPreferenceRepository { get; }
        IGenericRepository<InternalReportSectionPreference> InternalReportSectionPreferenceRepository { get; }
        IGenericRepository<InternalReportValueTypePreference> InternalReportValueTypePreferenceRepository { get; }
        IGenericRepository<InternalReportPeriodTypePreference> InternalReportPeriodTypePreferenceRepository { get; }
        IGenericRepository<InternalReportCalculationPreference> InternalReportCalculationPreferenceRepository { get; }
        IGenericRepository<MPeriod> MPeriodRepository { get; }
        IGenericRepository<MCalculation> MCalculationRepository { get; }
        IGenericRepository<MInternalReportExcelTemplate> MInternalReportExcelTemplateRepository { get; }
        IGenericRepository<InternalReportExcelTemplatePreference> InternalReportExcelTemplatePreferenceRepository { get; }
        IGenericRepository<InternalReportPeriodConfigPreference> InternalReportPeriodConfigPreferenceRepository { get; }
        IGenericRepository<ReportDownloadType> ReportDownloadTypeRepository { get; }
        IGenericRepository<ConsolidatedReportConfiguration> ConsolidatedReportConfigurationRepository { get; }
        IGenericRepository<MappingConsolidatedReportConfiguration> MappingConsolidatedReportConfigurationRepository { get; }
        IGenericRepository<ConsolidatedReportPreference> ConsolidatedReportPreferenceRepository { get; }
        IGenericRepository<MConsolidatedReportExcelTemplate> MConsolidatedReportExcelTemplateRepository { get; }
        IGenericRepository<PcMasterKpiValueDraft> PcMasterKpiValueDraftRepository{ get; }
        IGenericRepository<PortfolioCompanyOperationalKPIValuesDraft> PortfolioCompanyOperationalKPIValuesDraftRepository { get; }
        IGenericRepository<MGroupingList> MGroupingListRepository { get; }
        IGenericRepository<PortfolioCompanyCustomListDetails> PortfolioCustomListRepository { get; }
        IGenericRepository<ConsolidatedExcelTemplatePreference> ConsolidatedExcelTemplatePreferenceRepository { get; }

        IGenericRepository<PageConfigurationCommentaryCustomFieldValue> PageConfigurationCommentaryCustomFieldValueRepository { get; }
        IGenericRepository<ValuationModelDetail> ValuationModelDetail { get; }
        IGenericRepository<MValuationFinancialKpi> MValuationFinancialKpiRepository { get; }
        IGenericRepository<BackgroundJobReportHistory> BackgroundJobReportHistoryRepository { get; }
        IGenericRepository<MSubSectionFields> MSubSectionFieldsRepository { get; }
        IGenericRepository<DataAnalytics> DataAnalyticsRepository { get; }
        IGenericRepository<Esg_Kpi_DataRecords> Esg_Kpi_DataRecordsRepository { get; }
        IGenericRepository<Valuation_ImpliedEVRecord> ImpliedEVRecordRepository { get; }
        IGenericRepository<Valuation_AdjustmentDetail> AdjustmentDetailRepository { get; }
        IGenericRepository<Valuation_TargetCompanyKPIRecord> TargetCompanyKPIRecordRepository { get;}
        IGenericRepository<Valuation_UnselectedRecords> Valuation_UnselectedRecordsRepository { get; }
        IGenericRepository<M_ESGKPI> M_ESGKpisRepository { get; }
        IGenericRepository<FinancialAuditLog> FinancialAuditLogRepository { get; }
        IGenericRepository<File_Upload_Status> File_Upload_StatusRepository { get; }
        IGenericRepository<ValuationCompanyEquityCalculation> ValuationCompanyEquityCalculationRepository { get; }
        IGenericRepository<ValuationEquityValue> ValuationEquityValueRepository { get; }
        IGenericRepository<MappingCompanyValuationHeaderType> MappingCompanyValuationHeaderTypeRepository { get; }
        IGenericRepository<M_ValuationHeaderType> M_ValuationHeaderTypeRepository { get; }
        IGenericRepository<DocumentsInformation> DocumentsInformationRepository { get; }
        IGenericRepository<DocumentComments> DocumentCommentsRepository { get; }
        IGenericRepository<MCapTable> MCapTableRepository { get; }
        IGenericRepository<MappingCapTable> MappingCapTableRepository { get; }
        IGenericRepository<MKpiType> MKpiTypeRepository { get; }
        IGenericRepository<PcCapTableValues> PcCapTableValuesRepository { get; }
        IGenericRepository<CapTablePeriod> CapTablePeriodRepository { get; }
        IGenericRepository<CapTableAuditLog> CapTableAuditLogRepository { get; }
        IGenericRepository<DataRequest> DataRequestRepository { get; }
        IGenericRepository<DataRequestGroup> DataRequestGroupRepository { get; }
        IGenericRepository<DataRequestReminders> DataRequestReminderRepository { get; }
        IGenericRepository<DataRequestAttachments> DataRequestAttachmentRepository { get; }
        IGenericRepository<SDGImages> SDGImagesRepository { get; }
        IGenericRepository<ExternalUser> ExternalUserRepository { get; }
        IGenericRepository<UserBrowserDetails> UserBrowserDetailsRepository { get; }
        IGenericRepository<MMonthlyReport> MMonthlyReportRepository { get; }
        IGenericRepository<MappingMonthlyReport> MappingMonthlyReportRepository { get; }
        IGenericRepository<MLpReportTemplate> MLpReportTemplateRepository { get; }
        IGenericRepository<MappingLpReportKpiSection> MappingLpReportKpiSectionRepository { get; }
        IGenericRepository<MappingLpReportPeriodSection> MappingLpReportPeriodSectionRepository { get; }
        IGenericRepository<MappingLpReportSection> MappingLpReportSectionRepository { get; }
        IGenericRepository<MappingLpReportTemplate> MappingLpReportTemplateRepository { get; }
        IGenericRepository<MLpReportSection> MLpReportSectionRepository { get; }
        IGenericRepository<MappingLpReportCommentarySection> MappingLpReportCommentarySectionRepository { get; }
        IGenericRepository<LPReportHeader> LpReportHeaderRepository { get; }

        IGenericRepository<GrowthReportHeader> GrowthReportHeaderRepository { get; }
        IGenericRepository<MappingGrowthReport> MappingGrowthReportRepository { get; }
        IGenericRepository<GrowthReportHeaderModule> GrowthReportHeaderModuleRepository { get; }
        IGenericRepository<GrowthReportHeaderKpi> GrowthReportHeaderKpiRepository { get; }
        IGenericRepository<GrowthReportColumnKpi> GrowthReportColumnKpiRepository { get; }
        IGenericRepository<DataExtractionTypes> DataExtractionTypesRepository { get; }
        IGenericRepository<UserInformation> UserInfoRepository { get; }
        IGenericRepository<User_Documents> UserDocumentsRepository { get; }
        IGenericRepository<M_UserCategory> MUserCategoryRepository { get; }
        IGenericRepository<MSourceTypes> MSourceTypesRepository { get; }
        IGenericRepository<DocCollectionFrequencyConfig> DocCollectionConfigRepository { get; }
        IGenericRepository<DocumentCollectionStore> DocumentCollectionRepository { get; }
        IGenericRepository<RepositoryDocumentMappingDetail> DocumentMappingRepository { get; }
        IGenericRepository<MFundSectionKpi> MFundSectionKpiRepository { get; }
        IGenericRepository<MappingFundSectionKpi> MappingFundSectionKpiRepository { get; }
        IGenericRepository<FundMasterKpiValues> FundMasterKpiValuesRepository { get; }
        IGenericRepository<FundKpiAuditLog> FundKpiAuditLogRepository { get; }
        IGenericRepository<MFundKpiModules> MFundKpiModulesRepository { get; }
        IGenericRepository<DashboardTrackerConfig> DashboardTrackerConfigRepository { get; }

       
        #region CLO_Module

        /// <summary>
        /// Get/Set Property for InvestmentCompanyDetails repository .
        /// </summary>
        IGenericRepository<CLO_InvestmentCompanyDetails> InvestmentCompanyDetailsRepository
        {
            get;
        }
        IGenericRepository<M_clopagedetails> CLOPageDetailsRepository
        {
            get;
        }
        IGenericRepository<CLO_M_Table> CLOTableDetailsRepository
        {
            get;
        }
        IGenericRepository<clo_commentries> CloCommentriesRepository
        {
            get;
        }
        IGenericRepository<CloDetails> CloDetailsRepository
        {
            get;
        }

        IGenericRepository<TableFootnoteSchema> TableFootnoteSchemaRepository
        {
            get;
        }
        #endregion

        IGenericRepository<CompanyEmailGroup> CompanyEmailGroupRepository
        {
            get;
        }
        IGenericRepository<EmailListMember> EmailListMemberRepository
        {
            get;
        }
        IGenericRepository<EmailNotificationGroup> EmailNotificationGroupRepository
        {
            get;
        }
        IGenericRepository<EmailReminder> EmailReminderRepository
        {
            get;
        }
        IGenericRepository<EmailReminderRecipients> EmailReminderRecipientsRepository
        {
            get;
        }
        IGenericRepository<EmailReminderConfig> EmailReminderConfigRepository
        {
            get;
        }
        IGenericRepository<EmailReminderSchedule> EmailReminderScheduleRepository
        {
            get;
        }

        #endregion   Repository Creation properties...

        object DynamicSQLQuery(string sql, params object[] parameters);

        object DynamicSQLPreviewQuery(string sql, params object[] parameters);

        object SQLEditorQuery(string sql, params object[] parameters);

    }
}