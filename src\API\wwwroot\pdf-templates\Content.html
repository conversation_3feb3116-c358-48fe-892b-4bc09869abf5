﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="httpss://cdn.quilljs.com/1.3.6/quill.snow.css">
    <link rel="stylesheet" href="https://cdn.quilljs.com/1.3.6/quill.bubble.css">
    <link rel="stylesheet" href="[styleCSS]" />
    <!--[styleCSS]-->
    <style>
        html {
            height: 0;
        }
        .display-none {
            display: none;
        }

        .display-block {
            display: block;
        }

        .container-body {
            background: #fff;
            padding: 0;
            margin: 0;
            width: 340mm;
            height: 100%;
            /* margin: 30mm 0mm 0mm 0mm;*/
            text-align: left;
            font-size: 24.5px;
            font-family: 'Source Sans Pro', sans-serif !important;
        }

        .pdf-col-85 {
            width: 85%;
        }

        .pdf-col-2 {
            width: 20%;
        }

        .pdf-col-8 {
            width: 80%;
        }

        .pdf-left {
            float: left;
        }

        .pdf-col-15 {
            width: 15%;
        }

        .pdf-legal-trading {
            background: #596485;
            color: #fff;
            padding: 5px;
        }

        .pdf-mar-10 {
            margin-top: 10px;
        }

        .pdf-mar-8 {
            margin-top: 8px;
        }

        .pdf-col-35 {
            width: 40%;
        }

        .pdf-pad-5 {
            padding: 8px 5px;
        }

        .pdf-col-55 {
            width: 58%;
        }

        .pdf-header {
            color: #fff;
            padding: 5px 0;
        }

        .pdf-label {
            color: #596485;
        }

        .pdf-data-table {
            margin-top: 15px;
        }

        .black {
            color: #3f3b41;
        }

        .pdf-data-table td {
            color: #3f3b41;
            padding: 10px 5px !important;
        }

        .pdf-h4, h4 {
            font-size: 33px;
            padding: 5px;
            margin: 25px 0 0 0;
        }

        .pdf-p {
            font-size: 26px;
            line-height: 1.4;
            color: #3f3b41;
            letter-spacing: 0.3px;
            text-align: justify;
        }

        table {
            border: 0;
        }

            table td {
                text-align: left;
                padding: 8px 0;
            }

            table th {
                text-align: left;
                padding: 8px 0;
                border-left: 1px solid #fff;
            }

        .text-right {
            text-align: right;
        }

        .pdf-mainDiv {
            width: 100%;
        }

        .pdf-valueDiv {
            width: 50%;
            float: left;
            padding: 8px 0;
        }

        .pdf-valueLeft {
            width: 60%;
            float: left;
            color: #596485;
        }

        .pdf-valueRight {
            width: 40%;
            float: left;
            color: #3f3b41;
        }

        .pdf-clearfix {
            clear: both;
        }

        .pdf-border-orange {
            border-bottom: 3px solid #f26524;
        }

        .pdf-border-blue {
            border-bottom: 3px solid #596485;
        }

        .pdf-border-black {
            border-bottom: 3px solid #3f3b41;
        }

        .blue-text {
            color: #596485 !important;
        }

        thead {
            display: table-header-group
        }

        tr {
            page-break-inside: avoid
        }

        .section-header-break {
            page-break-inside: avoid;
        }

        .bold-text, .header-text {
            font-weight: bold !important;
        }

        .wrap-text {
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow-wrap: break-word;
        }

        .padding-4 {
            padding: 4px;
        }

        .pdf-legal-trading {
            background: #596485;
            color: #fff;
            padding: 5px;
            margin-top: 15px;
        }

        .company-logo-detail {
            overflow: hidden;
            width: 100%
        }

        .pdf-company-name, .pdf-company-logo {
            display: inline-block;
            vertical-align: middle;
        }

        .align-left {
            float: left;
        }

        .align-right {
            float: right;
        }

        .width-100 {
            width: 100% !important;
        }

        .align-center {
            text-align: center;
            display: inline-block;
            width: 80%
        }

        .align-center-100-width {
            text-align: center;
            display: inline-block;
            width: 90%
        }

        .padding-10 {
            padding: 10px;
        }

        .padding-top-50 {
            padding-top: 50px;
        }

        .pdf-company-name-span {
            display: block;
            font-weight: bold;
            font-size: 33px;
            unicode-bidi: isolate;
        }

        .ql-editor {
            padding: 0 !important;
            margin-top: -25px !important;
            color: #3f3b41 !important;
            font-size: 26px !important;
            letter-spacing: 0.3px !important;
            text-align: justify !important;
            white-space: pre-line !important;
        }

            .ql-editor ol,
             .ql-editor ul {
                padding-left: 0 !important;
                color: #3f3b41 !important;
                font-size: 26px !important;
                letter-spacing: 0.3px !important;
                text-align: justify !important;
                white-space: pre-line !important;
            }
            .ql-editor, .ql-editor * {
    color: #3f3b41 !important;
}
        .pdf-kpi-table thead tr {
            background-color: #596485
        }
            .pdf-kpi-table thead tr th:first-child {
                text-align: left;
                padding: 10px 5px !important;
                font-style: italic;
            }
        .pdf-kpi-table tbody tr td:first-child {
            text-align: left;
            padding: 10px 5px !important;
        }
        .pdf-kpi-table thead tr th {
            text-align: center;
            color: #fff;
            padding: 5px 0;
            border-left: 1px solid #fff;
        }
        .pdf-kpi-table tbody tr td {
            text-align: right;
            padding: 10px 5px !important;
        }
        .impact-icons {
            height: 120px;
            padding: 3px 5px 3px 0;
            margin: 5px 5px 5px 0;
        }
        .word-wrap {
            word-wrap: break-word !important;
        }
        .text-align{
            text-align:center !important;
        }
        .logo-img {
            height: 100px;
            object-fit: contain;
            max-width: 100%;
            display: inline-block;
        }
        .ql-editor [class^="ql-size-"] {
            font-size: 26px !important;
            text-align: justify !important;
        }
        .impact-kpi-theme{
            padding-top: 20px !important;
        }
    </style>
</head>
<body>
    <div class="container-body">
      <content-body/>       
    </div>
</body>
</html>
