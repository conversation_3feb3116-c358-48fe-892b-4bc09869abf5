IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_CompanyBalanceSheetLineItems') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_CompanyBalanceSheetLineItems') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_CompanyBalanceSheetLineItems] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_CompanyCashFlowLineItems') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_CompanyCashFlowLineItems') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_CompanyCashFlowLineItems] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_CompanyProfitAndLossLineItems') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_CompanyProfitAndLossLineItems') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_CompanyProfitAndLossLineItems] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_ImpactKPI_Order') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_ImpactKPI_Order') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_ImpactKPI_Order] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_Kpis') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_Kpis') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_Kpis] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_PortfolioCompanyKPI') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_PortfolioCompanyKPI') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_PortfolioCompanyKPI] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_PortfolioInvestmentKPI') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_PortfolioInvestmentKPI') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_PortfolioInvestmentKPI] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Mapping_PortfolioOperationalKPI') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Mapping_PortfolioOperationalKPI') AND name = 'IsExtraction')
    ALTER TABLE dbo.[Mapping_PortfolioOperationalKPI] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MappingCapTable') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.MappingCapTable') AND name = 'IsExtraction')
    ALTER TABLE dbo.[MappingCapTable] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MappingFundSectionKpi') AND NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.MappingFundSectionKpi') AND name = 'IsExtraction')
    ALTER TABLE dbo.[MappingFundSectionKpi] ADD [IsExtraction] BIT NOT NULL DEFAULT 0;
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_CompanyBalanceSheetLineItems]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_CompanyBalanceSheetLineItems] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_CompanyCashFlowLineItems]') AND name = 'Synonym') 
ALTER TABLE dbo.[Mapping_CompanyCashFlowLineItems] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_CompanyProfitAndLossLineItems]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_CompanyProfitAndLossLineItems] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_ImpactKPI_Order]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_ImpactKPI_Order] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_Kpis]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_Kpis] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_PortfolioCompanyKPI]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_PortfolioCompanyKPI] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_PortfolioInvestmentKPI]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_PortfolioInvestmentKPI] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[Mapping_PortfolioOperationalKPI]') AND name = 'Synonym')
ALTER TABLE dbo.[Mapping_PortfolioOperationalKPI] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MappingCapTable]') AND name = 'Synonym')
ALTER TABLE dbo.[MappingCapTable] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MappingFundSectionKpi]') AND name = 'Synonym')
ALTER TABLE dbo.[MappingFundSectionKpi] ADD Synonym varchar(3000), Definition varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_BalanceSheet_LineItems]') AND name = 'synonym')
    ALTER TABLE dbo.[M_BalanceSheet_LineItems] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_ProfitAndLoss_LineItems]') AND name = 'synonym') 
    ALTER TABLE dbo.[M_ProfitAndLoss_LineItems] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_CashFlow_LineItems]') AND name = 'synonym')
    ALTER TABLE dbo.[M_CashFlow_LineItems] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_ImpactKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_ImpactKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_MasterKpis]') AND name = 'synonym')
    ALTER TABLE dbo.[M_MasterKpis] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_InvestmentKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_InvestmentKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_SectorwiseOperationalKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_SectorwiseOperationalKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[M_CompanyKPI]') AND name = 'synonym')
    ALTER TABLE dbo.[M_CompanyKPI] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MCapTable]') AND name = 'synonym')
    ALTER TABLE dbo.[MCapTable] ADD Synonym varchar(3000)
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[MFundSectionKpi]') AND name = 'synonym')
    ALTER TABLE dbo.[MFundSectionKpi] ADD Synonym varchar(3000)
GO
ALTER PROCEDURE  [dbo].[ProcCreateDuplicateKPI]
(
@KpiType VARCHAR(100),
@KpiId INT,
@UserId INT,
@ModuleId INT=NULL,
@Id INT OUTPUT
)
AS
BEGIN
SET NOCOUNT ON
		 BEGIN TRY
		 IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO M_SectorwiseOperationalKPI(SectorID,KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT SectorID,KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Company KPI')
			BEGIN
				INSERT INTO M_CompanyKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_CompanyKPI WHERE CompanyKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Impact KPI')
			BEGIN
				INSERT INTO M_ImpactKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym FROM M_ImpactKPI WHERE ImpactKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Investment KPI')
			BEGIN
				INSERT INTO M_InvestmentKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsNumeric,OrderBy,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,0,0,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym FROM M_InvestmentKPI WHERE InvestmentKPIId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
				INSERT INTO M_BalanceSheet_LineItems(BalanceSheetLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT BalanceSheetLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO M_CashFlow_LineItems(CashFlowLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT CashFlowLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Profit And Loss KPI')
			BEGIN
				INSERT INTO M_ProfitAndLoss_LineItems(ProfitAndLossLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym) SELECT ProfitAndLossLineItem,KpiInfo,@UserId,GETDATE(),0,1,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,1,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Credit KPI')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,2,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId  = 16)
			BEGIN
				INSERT INTO MMonthlyReport(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId) SELECT Kpi,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId FROM MMonthlyReport WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (11,12,13,14,15))
			BEGIN
				INSERT INTO MCapTable(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym) SELECT KPI,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym FROM MCapTable WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
         END TRY
         BEGIN CATCH
             SET @Id = 0
         END CATCH
END
go
ALTER PROCEDURE [dbo].[ProcCreateDuplicateFundKPI]
(
    @KpiId INT,
    @UserId INT,
    @Id INT OUTPUT
)
AS
BEGIN
    SET NOCOUNT ON
    BEGIN TRY
        BEGIN
            INSERT INTO MFundSectionKpi(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,MethodologyID,IsHeader,IsBoldKPI,Description,
            Formula,FormulaKPIId,ModuleId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,MethodologyID,IsHeader,IsBoldKPI,
            Description,Formula,FormulaKPIId,ModuleId,Synonym FROM MFundSectionKpi
            WHERE FundSectionKpiId = @KpiId
            SET @Id = SCOPE_IDENTITY()
        END
    END TRY
    BEGIN CATCH
        SET @Id = 0
    END CATCH
    
END
go
ALTER PROCEDURE  [dbo].[ProcFundKpiCopyKPIToFunds]
(
@ModuleId INT,
@FundId INT,
@UserId INT,
@FundIds NVARCHAR(MAX)
)
AS
BEGIN
SET NOCOUNT ON

	 BEGIN TRY
		 DECLARE @RowCount INT,@Row INT = 1;
		 DROP TABLE IF EXISTS #tempFundKpiCopyToFunds
			CREATE TABLE #tempFundKpiCopyToFunds
			(
			 Id INT IDENTITY(1,1) PRIMARY KEY,
			 FundId INT,
			 ModuleId INT
			)
			INSERT INTO #tempFundKpiCopyToFunds(FundId)
			SELECT Item AS FundId FROM[dbo].[SplitString](@FundIds,',') WHERE Item!=@FundId
			SET @RowCount = (SELECT Count(FundId) FROM #tempFundKpiCopyToFunds)
			WHILE (@Row <= @RowCount)
			BEGIN
			DECLARE @PortfolioFundId INT
			SELECT @PortfolioFundId=FundId FROM #tempFundKpiCopyToFunds Where ID= @Row
		   DECLARE @MappingFundKpi table(ID INT IDENTITY(1,1), FundId int, KpiId int, CreatedBy int, CreatedOn Datetime,
		   IsDeleted bit, ParentId int, DisplayOrder int, Formula nvarchar(max), FormulaKPIId nvarchar(max), ModuleId int ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
	       DELETE FROM @MappingFundKpi
			BEGIN
					INSERT INTO @MappingFundKpi
					SELECT FundId, KpiId, CreatedBy, GETDATE(), IsDeleted, ParentKpiId, DisplayOrder, Formula, FormulaKPIId, ModuleId,Synonym,Definition,IsExtraction From
					MappingFundSectionKpi where FundId = @FundId AND ModuleId=@ModuleId AND IsDeleted = 0
					
					IF EXISTS(select * from MappingFundSectionKpi where  IsDeleted =0 and FundId = @PortfolioFundId and
					ModuleId=@ModuleId and KpiId IN (SELECT KpiId FROM @MappingFundKpi))
					BEGIN
					    UPDATE MappingFundSectionKpi SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiId,ParentKpiId=B.ParentId,Formula=B.Formula,
						FormulaKPIId=B.FormulaKPIId,Synonym = B.Synonym,Definition = B.Definition,IsExtraction = B.IsExtraction FROM MappingFundSectionKpi A
						Inner Join @MappingFundKpi B On A.KpiId=B.KpiId WHERE A.IsDeleted=0 and A.FundId= @PortfolioFundId and A.ModuleId=@ModuleId
					END
						INSERT INTO MappingFundSectionKpi(FundId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,Formula,FormulaKPIId,ModuleId,Synonym,Definition,IsExtraction)
						SELECT @PortfolioFundId,KpiId,CreatedBy,GETDATE(),0,ParentId,DisplayOrder,Formula,FormulaKPIId,@ModuleId,Synonym,Definition,IsExtraction From @MappingFundKpi
						where KpiId not in  (SELECT KpiId FROM MappingFundSectionKpi
							where FundId =@PortfolioFundId AND IsDeleted=0)
							and IsDeleted=0 and ModuleId=@ModuleId

			END
		
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempFundKpiCopyToFunds		
         END TRY
         BEGIN CATCH
             
         END CATCH
	
END
go
ALTER PROC [dbo].[GetFundkpiNotMappedList](
@FundId INT,
@ModuleId INT
)
AS
BEGIN
    SELECT DISTINCT(K.FundSectionKpiId) 'Id',K.KPI 'Name',K.IsHeader 'IsHeader',K.IsBoldKPI 'IsBoldKPI',K.Formula 'Formula',
    K.FormulaKPIId 'FormulaKPIId',Synonym,Description FROM MFundSectionKpi K 
                                where K.FundSectionKpiId not in  (SELECT KpiId FROM MappingFundSectionKpi 
                                where FundId =@FundId AND IsDeleted=0) 
                                and K.IsDeleted=0 and K.ModuleId=@ModuleId  order by K.KPI
END
GO
ALTER  PROCEDURE [dbo].[GetMasterkpiNotMappedList]
(
@companyId INT,
@Type Varchar(100),
@ModuleId INT = NULL
)
AS
BEGIN  
	IF @Type = 'Company KPI' 
							BEGIN
							SELECT DISTINCT(M.CompanyKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId', M.KpiInfo 'KpiInfo',M.Synonym,M.Description   FROM M_CompanyKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioCompanyKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CompanyKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MappingPortfolioCompanyKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Impact KPI'
							BEGIN
							SELECT DISTINCT(M.ImpactKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ImpactKPI M 
							LEFT JOIN (SELECT *FROM Mapping_ImpactKPI_Order 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ImpactKPIID = MAP.ImpactKPIID 
							WHERE M.IsDeleted =0  AND MAP.ImpactKPIMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Investment KPI'
							BEGIN
							SELECT DISTINCT(M.InvestmentKPIId) 'Id',M.KPI 'Name', M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_InvestmentKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioInvestmentKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.InvestmentKPIId = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioInvestmentKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Balance Sheet KPI'
							BEGIN
							SELECT DISTINCT(M.BalanceSheetLineItemID) 'Id',M.BalanceSheetLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_BalanceSheet_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyBalanceSheetLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.BalanceSheetLineItemID = MAP.BalanceSheetLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyBalanceSheetLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Cashflow KPI'
							BEGIN
							SELECT DISTINCT(M.CashFlowLineItemID) 'Id',M.CashFlowLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_CashFlow_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyCashFlowLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CashFlowLineItemID = MAP.CashFlowLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyCashFlowLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Profit & Loss KPI'
							BEGIN
							SELECT DISTINCT(M.ProfitAndLossLineItemID) 'Id',M.ProfitAndLossLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ProfitAndLoss_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyProfitAndLossLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ProfitAndLossLineItemID = MAP.ProfitAndLossLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyProfitAndLossLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Operational KPI'
							BEGIN
							SELECT DISTINCT(M.SectorwiseOperationalKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_SectorwiseOperationalKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioOperationalKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.SectorwiseOperationalKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioOperationalKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Credit KPI'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 2) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 2  AND Mapping_KpisID IS NULL order by Name
							END
	ELSE IF @Type = 'Trading Records'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 1) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 1  AND Mapping_KpisID IS NULL order by Name
							END
    IF @ModuleId = 16
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKpiId 'FormulaKPIId',M.KpiInfo 'KpiInfo'  FROM MMonthlyReport M 
							LEFT JOIN (SELECT *FROM MappingMonthlyReport 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.KpiId = MAP.KpiId
							WHERE M.IsDeleted =0 AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (11,12,13,14,15)
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',KpiTypeId,M.Synonym,M.Description  FROM MCapTable M 
							LEFT JOIN (SELECT *FROM MappingCapTable 
							where PortfolioCompanyId = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.KpiId = MAP.KpiId 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30)
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND Mapping_KpisID IS NULL order by Name
							END
END
GO
ALTER PROCEDURE [dbo].[ProcCopyKPIToCompanies]
(
@KpiType VARCHAR(100),
@CompanyId INT,
@UserId INT,
@CompanyIds NVARCHAR(MAX),
@ModuleId INT = NULL
)
AS
BEGIN
SET NOCOUNT ON
			BEGIN TRY
			DECLARE @RowCount INT,@Row INT = 1;
			DROP TABLE IF EXISTS #tempCopyToCompany
			CREATE TABLE #tempCopyToCompany
			(
				Id INT IDENTITY(1,1) PRIMARY KEY,
				PortfolioCompanyID Int
			)
			INSERT INTO #tempCopyToCompany(PortfolioCompanyID)
			SELECT Item AS PortfolioCompanyID FROM[dbo].[SplitString](@CompanyIds,',') WHERE Item!=@CompanyId
			SET @RowCount = (SELECT Count(PortfolioCompanyID) FROM #tempCopyToCompany)
			WHILE (@Row <= @RowCount)
			BEGIN
			DROP TABLE IF EXISTS #KPIHierarchy;
            DROP TABLE IF EXISTS #ExistingKPIs;
			CREATE TABLE #ExistingKPIs ( KPIName VARCHAR(500), DestKpiID INT, ParentKPIID INT, ParentKPIName VARCHAR(500), DisplayOrder INT, IsRootLevel BIT ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			CREATE TABLE #KPIHierarchy ( SourceKpiID INT, KPIName VARCHAR(MAX), ParentKPIID INT, ParentKPIName VARCHAR(MAX), Level INT, DisplayOrder INT, HierarchyOrder VARCHAR(MAX), NewDisplayOrder INT, KPITypeID INT ,Formula NVarchar(Max),FormulaKPIId INT,CreatedBy INT,CreatedOn datetime,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @PortfolioCompanyID INT
			SELECT @PortfolioCompanyID=PortfolioCompanyID FROM #tempCopyToCompany Where ID= @Row
			DECLARE @MappingOperational table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCompany table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingImpact table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ImpactKPIID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,KPIOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingInvestment table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),KPIHierarchy Nvarchar(Max),HierarchyOrder NVARCHAR(500),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingBalance table(ID INT IDENTITY(1,1), PortfolioCompanyID int,BalanceSheetLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingProfit table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ProfitAndLossLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCashflow table(ID INT IDENTITY(1,1), PortfolioCompanyID int,CashFlowLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingTrading table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCredit table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCapTable table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingMonthlyReport table(ID INT IDENTITY(1,1),PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,Formula nvarchar(max),FormulaKPIId nvarchar(max));
			DELETE FROM @MappingOperational
			DELETE FROM @MappingCompany
			DELETE FROM @MappingImpact
			DELETE FROM @MappingInvestment
			DELETE FROM @MappingBalance
			DELETE FROM @MappingProfit
			DELETE FROM @MappingCashflow
			DELETE FROM @MappingTrading
			DELETE FROM @MappingCredit
			DELETE FROM @MappingCapTable
			DELETE FROM @MappingMonthlyReport
			Declare @MaxOrderId INT;
			Declare @ID INT,@ParentKPIID INT,@DestParentKPIID INT,@KpiID INT;
			Declare @KPI NVarchar(MAX)=NULL;
			IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO @MappingOperational 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioOperationalKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(SELECT * FROM Mapping_PortfolioOperationalKPI  WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingOperational ))
					BEGIN
						UPDATE Mapping_PortfolioOperationalKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioOperationalKPI A Inner Join @MappingOperational B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID 
					END							
				INSERT INTO Mapping_PortfolioOperationalKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction FROM  @MappingOperational WHERE  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioOperationalKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Company KPI')
			BEGIN
			 	INSERT INTO @MappingCompany 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioCompanyKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_PortfolioCompanyKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and KpiID IN (SELECT KpiID FROM @MappingCompany ))
					BEGIN
						UPDATE Mapping_PortfolioCompanyKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioCompanyKPI A Inner Join @MappingCompany B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_PortfolioCompanyKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCompany where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioCompanyKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Impact KPI')
			BEGIN
			 	INSERT INTO @MappingImpact 
				SELECT PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_ImpactKPI_Order where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_ImpactKPI_Order where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and ImpactKPIID IN (SELECT ImpactKPIID FROM @MappingImpact ))
					BEGIN
						UPDATE Mapping_ImpactKPI_Order SET KPIOrder=B.KPIOrder,ImpactKPIID=B.ImpactKPIID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_ImpactKPI_Order A Inner Join @MappingImpact B On A.ImpactKPIID=B.ImpactKPIID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_ImpactKPI_Order(PortfolioCompanyID,ImpactKPIID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),0,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingImpact where ImpactKPIID NOT IN (SELECT ImpactKPIID FROM Mapping_ImpactKPI_Order WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
			END
			IF(@KpiType = 'Investment KPI')
			BEGIN	
				SELECT @MaxOrderId = ISNULL(MAX(DisplayOrder), 0) FROM Mapping_PortfolioInvestmentKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @PortfolioCompanyID;
				;WITH KPIHierarchyCTE AS (
                    SELECT 
                        src.KpiID AS SourceKpiID,
                         CAST(srcKPI.KPI AS VARCHAR(MAX)) AS KPIName,
                        src.ParentKPIID,
                         CAST(NULL AS VARCHAR(MAX)) AS ParentKPIName,
                        0 AS Level,
                        src.DisplayOrder,
						CAST(RIGHT('000000' + CAST(DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) AS HierarchyOrder,
						src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.ParentKPIID IS NULL
                    AND src.IsDeleted = 0
                    UNION ALL
                    SELECT 
                        src.KpiID,
                        CAST(srcKPI.KPI AS VARCHAR(MAX)),
                        src.ParentKPIID,
                        CAST(parentKPI.KPI AS VARCHAR(MAX)),
                        h.Level + 1,
                        src.DisplayOrder,
						 CAST(h.HierarchyOrder + '.' + RIGHT('000000' + CAST(src.DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) as HierarchyOrder,
						 src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    INNER JOIN M_InvestmentKPI parentKPI 
                        ON src.ParentKPIID = parentKPI.InvestmentKPIId
                    INNER JOIN KPIHierarchyCTE h 
                        ON src.ParentKPIID = h.SourceKpiID
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.IsDeleted = 0
                )
				 INSERT INTO #KPIHierarchy(KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction)
                SELECT KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,CASE WHEN @MaxOrderId=0 THEN
				        ROW_NUMBER() OVER (ORDER BY HierarchyOrder) 
						ELSE @MaxOrderId+1 + ROW_NUMBER() OVER (ORDER BY HierarchyOrder) END AS NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction FROM KPIHierarchyCTE;

				-- Get existing destination KPIs
                INSERT INTO #ExistingKPIs
                SELECT DISTINCT
                    destKPI.KPI,
                    dest.KpiID,
                    dest.ParentKPIID,
                    parentKPI.KPI,
                    dest.DisplayOrder,
                    CASE WHEN dest.ParentKPIID IS NULL THEN 1 ELSE 0 END,
					dest.Synonym,
					dest.Definition,
					dest.IsExtraction
                FROM Mapping_PortfolioInvestmentKPI dest
                INNER JOIN M_InvestmentKPI destKPI 
                    ON dest.KpiID = destKPI.InvestmentKPIId
                LEFT JOIN M_InvestmentKPI parentKPI 
                    ON dest.ParentKPIID = parentKPI.InvestmentKPIId
                WHERE dest.PortfolioCompanyID = @PortfolioCompanyID
                AND dest.IsDeleted = 0;

				--SELECT * FROM #KPIHierarchy

				INSERT INTO @MappingInvestment(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,HierarchyOrder,Synonym,Definition,IsExtraction) 
				SELECT 
                    kh.KPITypeID,
                    @PortfolioCompanyID,
                    kh.SourceKpiID,
                    @UserId,
                    GETDATE(),
                    0,
                    CASE 
					    WHEN kh.ParentKPIID IS NOT NULL THEN
					        CASE WHEN EXISTS (
					            SELECT 1 
					            FROM #ExistingKPIs e 
					            WHERE e.KPIName = kh.ParentKPIName
					            AND e.IsRootLevel = 1
					        )
					        THEN 
					            (SELECT TOP 1 e.DestKpiID 
					             FROM #ExistingKPIs e 
					             WHERE e.KPIName = kh.ParentKPIName
					             AND e.IsRootLevel = 1)
					        ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					        END
					    ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					END ParentKPIID,
					kh.NewDisplayOrder,kh.Formula,kh.FormulaKPIId,kh.HierarchyOrder,kh.Synonym,kh.Definition,kh.IsExtraction
                FROM #KPIHierarchy kh
                LEFT JOIN #ExistingKPIs existing
                    ON existing.KPIName = kh.KPIName
                    AND existing.IsRootLevel = CASE WHEN kh.ParentKPIID IS NULL THEN 1 ELSE 0 END
                WHERE NOT EXISTS (
                    SELECT 1 FROM #ExistingKPIs e
                    WHERE e.KPIName = kh.KPIName
                    AND (
                        (kh.ParentKPIID IS NULL AND e.IsRootLevel = 1) OR
                        (kh.ParentKPIID IS NOT NULL AND e.ParentKPIName = kh.ParentKPIName)
                    )
                );
			   IF EXISTS(select *from Mapping_PortfolioInvestmentKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingInvestment))
				BEGIN
					UPDATE Mapping_PortfolioInvestmentKPI SET DisplayOrder=B.DisplayOrder ,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioInvestmentKPI A Inner Join @MappingInvestment B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
			   END
			   INSERT INTO Mapping_PortfolioInvestmentKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
			   SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingInvestment where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioInvestmentKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
				DROP TABLE IF EXISTS #KPIHierarchy;
                DROP TABLE IF EXISTS #ExistingKPIs;
			
			END
			IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
			 	INSERT INTO @MappingBalance 
				SELECT PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyBalanceSheetLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_CompanyBalanceSheetLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and BalanceSheetLineItemID IN (SELECT BalanceSheetLineItemID FROM @MappingBalance))
					BEGIN
						UPDATE Mapping_CompanyBalanceSheetLineItems SET DisplayOrder=B.DisplayOrder,BalanceSheetLineItemID=B.BalanceSheetLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyBalanceSheetLineItems A Inner Join @MappingBalance B On A.BalanceSheetLineItemID=B.BalanceSheetLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyBalanceSheetLineItems(PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingBalance where  BalanceSheetLineItemID NOT IN (SELECT BalanceSheetLineItemID FROM Mapping_CompanyBalanceSheetLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO @MappingCashflow 
				SELECT PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyCashFlowLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyCashFlowLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and CashFlowLineItemID IN (SELECT CashFlowLineItemID FROM @MappingCashflow))
					BEGIN
						UPDATE Mapping_CompanyCashFlowLineItems SET DisplayOrder=B.DisplayOrder,CashFlowLineItemID=B.CashFlowLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyCashFlowLineItems A Inner Join @MappingCashflow B On A.CashFlowLineItemID=B.CashFlowLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyCashFlowLineItems(PortfolioCompanyID,CashFlowLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCashflow where  CashFlowLineItemID NOT IN (SELECT CashFlowLineItemID FROM Mapping_CompanyCashFlowLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Profit & Loss KPI')
			BEGIN
				INSERT INTO @MappingProfit 
				SELECT PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyProfitAndLossLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyProfitAndLossLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ProfitAndLossLineItemID IN (SELECT ProfitAndLossLineItemID FROM @MappingProfit))
					BEGIN
						UPDATE Mapping_CompanyProfitAndLossLineItems SET DisplayOrder=B.DisplayOrder,ProfitAndLossLineItemID=B.ProfitAndLossLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyProfitAndLossLineItems A Inner Join @MappingProfit B On A.ProfitAndLossLineItemID=B.ProfitAndLossLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyProfitAndLossLineItems(PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingProfit where  ProfitAndLossLineItemID NOT IN (SELECT ProfitAndLossLineItemID FROM Mapping_CompanyProfitAndLossLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
			 	INSERT INTO @MappingTrading 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=1 AND IsDeleted = 0
				
				IF EXISTS(select *from Mapping_Kpis where ModuleID=1 and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingTrading))
					BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingTrading B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=1
					END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingTrading where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=1 )
												
			END
		IF(@KpiType = 'Credit KPI' OR @ModuleId IN (2, 17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
			 	INSERT INTO @MappingCredit 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_Kpis where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingCredit))
				BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingCredit B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=@ModuleId
				END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCredit where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=@ModuleId )												
			END
			IF(@ModuleId = 16)
			BEGIN
			 	INSERT INTO @MappingMonthlyReport 
				SELECT PortfolioCompanyId,KpiId,CreatedBy,GETUTCDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKpiId From MappingMonthlyReport where PortfolioCompanyId = @CompanyId  AND IsDeleted = 0
				IF EXISTS(select *from MappingMonthlyReport where  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingMonthlyReport))
				BEGIN
						UPDATE MappingMonthlyReport SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKpiId,Formula=B.Formula,FormulaKpiId=B.FormulaKpiId FROM MappingMonthlyReport A Inner Join @MappingMonthlyReport B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID
				END							
				INSERT INTO MappingMonthlyReport(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKPIId) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETUTCDATE(),0,ParentKPIID,DisplayOrder,IsHeader,Formula,FormulaKPIId From @MappingMonthlyReport where  KpiID NOT IN (SELECT KpiID FROM MappingMonthlyReport WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID)												
			END
		IF(@ModuleId IN (11,12,13,14,15))
			BEGIN
			 	INSERT INTO @MappingCapTable 
				SELECT 1,PortfolioCompanyId,KpiId,CreatedBy,GETDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From MappingCapTable where PortfolioCompanyId = @CompanyId  AND ModuleId=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from MappingCapTable where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingCapTable))
				BEGIN
						UPDATE MappingCapTable SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM MappingCapTable A Inner Join @MappingCapTable B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID AND A.ModuleId=@ModuleId
				END							
				INSERT INTO MappingCapTable(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCapTable where  KpiID NOT IN (SELECT KpiID FROM MappingCapTable WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and ModuleId=@ModuleId)												
			END
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempCopyToCompany		
			END TRY
			BEGIN CATCH
              IF OBJECT_ID('tempdb..#KPIHierarchy') IS NOT NULL DROP TABLE #KPIHierarchy;
			  IF OBJECT_ID('tempdb..#ExistingKPIs') IS NOT NULL DROP TABLE #ExistingKPIs;
			END CATCH
END
GO
CREATE OR ALTER PROCEDURE [dbo].[GetAllMappedKpi]
(
@CompanyIds NVARCHAR(MAX)
)
AS
BEGIN
	Declare @TempKpiMapping TABLE
	(
		PortfolioCompanyID INT, KpiId INT, KpiName NVARCHAR(255), ParentKPIID INT, DisplayOrder INT, IsMapped BIT, IsHeader BIT,
	    IsBoldKPI BIT, MappingKPIId INT, KpiInfo NVARCHAR(MAX), IsExtraction BIT, Synonym NVARCHAR(255), Definition NVARCHAR(MAX),
	    MethodologyID INT, ModuleId INT
	)
	--BalanceSheet
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.BalanceSheetLineItemID AS KpiId, q2.BalanceSheetLineItem AS KpiName, q1.ParentLineItemID AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.CompanyBalanceSheetLineItemMappingID AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 8 as ModuleId
	--FROM
	--    Mapping_CompanyBalanceSheetLineItems q1
	--INNER JOIN
	--    M_BalanceSheet_LineItems q2 ON q1.BalanceSheetLineItemID = q2.BalanceSheetLineItemID
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----Cashflow
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.CashFlowLineItemID AS KpiId, q2.CashFlowLineItem AS KpiName, q1.ParentLineItemID AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.CompanyCashFlowLineItemMappingID AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 9 as ModuleId
	--FROM
	--    Mapping_CompanyCashFlowLineItems q1
	--INNER JOIN
	--    M_CashFlow_LineItems q2 ON q1.CashFlowLineItemID = q2.CashFlowLineItemID
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----P&L
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.ProfitAndLossLineItemID AS KpiId, q2.ProfitAndLossLineItem AS KpiName, q1.ParentLineItemID AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.CompanyProfitAndLossLineItemMappingID AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 7 as ModuleId
	--FROM
	--    Mapping_CompanyProfitAndLossLineItems q1
	--INNER JOIN
	--    M_ProfitAndLoss_LineItems q2 ON q1.ProfitAndLossLineItemID = q2.ProfitAndLossLineItemID
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----ImpactKpi
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.ImpactKPIID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
	--	q1.KPIOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.ImpactKPIMappingID AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 6 as ModuleId
	--FROM
	--    Mapping_ImpactKPI_Order q1
	--INNER JOIN
	--    M_ImpactKPI q2 ON q1.ImpactKPIID = q2.ImpactKPIID
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----CompanyKpi
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingPortfolioCompanyKPIId AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 5 as ModuleId
	--FROM
	--    Mapping_PortfolioCompanyKPI q1
	--INNER JOIN
	--    M_CompanyKPI q2 ON q1.KpiID = q2.CompanyKPIID
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----InvestmentKpi
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingPortfolioInvestmentKPIId AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 4 as ModuleId
	--FROM
	--    Mapping_PortfolioInvestmentKPI q1
	--INNER JOIN
	--    M_InvestmentKpi q2 ON q1.KpiID = q2.InvestmentKPIId
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----OperationalKpi
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKPIID AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingPortfolioOperationalKPIId AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, 3 as ModuleId
	--FROM
	--    Mapping_PortfolioOperationalKPI q1
	--INNER JOIN
	--    M_SectorwiseOperationalKPI q2 ON q1.KpiID = q2.SectorwiseOperationalKPIID
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	----CapTableKpis
	--INSERT INTO @TempKpiMapping 
	--SELECT
	--	q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKpiId AS ParentKPIID, 
	--	q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.MappingId AS MappingKPIId,
	--    q2.KpiInfo, q1.IsExtraction, q2.Synonym, q1.Definition, q2.MethodologyID, q2.ModuleId
	--FROM
	--    MappingCapTable q1
	--INNER JOIN
	--    MCapTable q2 ON q1.KpiID = q2.KpiId
	--WHERE
	--    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0
	--MasterKpis
	INSERT INTO @TempKpiMapping 
	SELECT
		q1.PortfolioCompanyID, q1.KpiID AS KpiId, q2.KPI AS KpiName, q1.ParentKpiId AS ParentKPIID, 
		q1.DisplayOrder, CAST(1 AS BIT) AS IsMapped, q2.IsHeader, q2.IsBoldKPI, q1.Mapping_KpisID AS MappingKPIId,
	    q2.KpiInfo, q1.IsExtraction, 
		CASE 
        WHEN q1.Synonym IS NOT NULL AND LTRIM(RTRIM(q1.Synonym)) <> '' THEN q1.Synonym
        ELSE q2.Synonym
    END AS Synonym,
		CASE WHEN q1.Definition IS NOT NULL AND LTRIM(RTRIM(q1.Definition)) <> '' THEN q1.Definition
        ELSE q2.Description
    END AS Definition
		, q2.MethodologyID, q2.ModuleId
	FROM
	    Mapping_Kpis q1
	INNER JOIN
	    M_MasterKpis q2 ON q1.KpiID = q2.MasterKpiID
	WHERE
	    q1.IsExtraction = 1 AND q1.PortfolioCompanyID IN (SELECT Item FROM dbo.SplitString(@CompanyIds,',')) AND q1.IsDeleted = 0 AND q2.IsDeleted = 0 and q1.ModuleID IN(1,2)
	
	SELECT * FROM @TempKpiMapping
END
GO
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable6')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable6', 'Capitalization Table 6', 38, NULL, 1, 0, GETDATE(), 1, 6, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable7')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES ( 
        'CapTable7', 'Capitalization Table 7', 38, NULL, 1, 0, GETDATE(), 1, 7, NULL,0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable8')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable8', 'Capitalization Table 8', 38, NULL, 1, 0, GETDATE(), 1, 8, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable9')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable9', 'Capitalization Table 9', 38, NULL, 1, 0, GETDATE(), 1, 9, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'CapTable10')
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'CapTable10', 'Capitalization Table 10', 38, NULL, 1, 0, GETDATE(), 1, 10, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable6')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable6')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable6'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable7')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable7')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable7'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable8')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable8')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable8'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable9')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable9')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable9'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE NAME = 'CapTable10')
    AND NOT EXISTS (
        SELECT 1 FROM MSubSectionFields 
        WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable10')
    )
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'CapTable10'),
        'Period Type', 'Period Type', 38, 1, 0, GETDATE(), 1, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
SET IDENTITY_INSERT M_SubPageDetails ON;
IF NOT EXISTS ( SELECT 1 FROM M_SubPageDetails WHERE SubPageID = 47 AND PageID = 1)
BEGIN
    INSERT INTO M_SubPageDetails (
        SubPageID,Name, AliasName, PageID, Description, isActive, isDeleted, CreatedOn, CreatedBy,SequenceNo, PagePath, IsCustom,IsDynamicFieldSupported, IsDataType, IsDragDrop, IsFootNote)
    VALUES (47,'OtherCapTable', 'Other Capitalization Tables', 1, NULL, 1, 0, GETDATE(), 3, 8, NULL, 0, 0, 0, 1, 0);
END
SET IDENTITY_INSERT M_SubPageDetails OFF;
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable1' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted,CreatedOn, CreatedBy, SequenceNo,PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight
    ) VALUES (
        'OtherCapTable1', 'Other Capitalization Table 1', 47, NULL, 1, 0, GETDATE(), 3, 1, NULL, 0, 0, 0, 0, 0, 0, 0
    )
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable1'))
BEGIN
    INSERT INTO MSubSectionFields (
        FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue
    )
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable1'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 1, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable2' AND SubPageID = 47)
BEGIN
INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable2', 'Other Capitalization Table 2', 47, NULL, 1, 0, GETDATE(), 3, 2, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable2'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable2'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 2, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable3' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable3', 'Other Capitalization Table 3', 47, NULL, 1, 0, GETDATE(), 3, 3, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable3'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable3'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 3, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable4' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable4', 'Other Capitalization Table 4', 47, NULL, 1, 0, GETDATE(), 3, 4, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable4'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable4'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 4, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable5' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable5', 'Other Capitalization Table 5', 47, NULL, 1, 0, GETDATE(), 3, 5, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable5'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable5'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 5, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable6' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable6', 'Other Capitalization Table 6', 47, NULL, 1, 0, GETDATE(), 3, 6, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable6'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable6'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 6, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable7' AND SubPageID = 47)
BEGIN
 INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
 VALUES ('OtherCapTable7', 'Other Capitalization Table 7', 47, NULL, 1, 0, GETDATE(), 3, 7, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable7'))
BEGIN
 INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
 VALUES (
(SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable7'),
'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 7, 'Monthly,Quarterly,Annual',  'Monthly,Quarterly,Annual'
 );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable8' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
    VALUES ('OtherCapTable8', 'Other Capitalization Table 8', 47, NULL, 1, 0, GETDATE(), 3, 8, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable8'))
BEGIN
    INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable8'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 8, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable9' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
    VALUES ('OtherCapTable9', 'Other Capitalization Table 9', 47, NULL, 1, 0, GETDATE(), 3, 9, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable9'))
BEGIN
    INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable9'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 9, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
IF NOT EXISTS (SELECT 1 FROM M_SubPageFields WHERE Name = 'OtherCapTable10' AND SubPageID = 47)
BEGIN
    INSERT INTO M_SubPageFields (Name, AliasName, SubPageID, Description, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, PagePath, IsCustom, isMandatory, DataTypeId, IsListData, ShowOnList, IsChart, IsHighLight)
    VALUES ('OtherCapTable10', 'Other Capitalization Table 10', 47, NULL, 1, 0, GETDATE(), 3, 10, NULL, 0, 0, 0, 0, 0, 0, 0);
END
IF NOT EXISTS ( SELECT 1 FROM MSubSectionFields  WHERE FieldID = (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE NAME = 'OtherCapTable10'))
BEGIN
    INSERT INTO MSubSectionFields (FieldID, Name, AliasName, SubPageID, isActive, isDeleted, CreatedOn, CreatedBy, SequenceNo, Options, ChartValue)
    VALUES (
        (SELECT TOP 1 FieldID FROM M_SubPageFields WHERE Name = 'OtherCapTable10'),
        'Period Type', 'Period Type', 47, 1, 0, GETDATE(), 3, 10, 'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual'
    );
END
GO
IF (NOT EXISTS (SELECT * FROM [dbo].[M_KpiModules] where Name='CapTable6'))
		BEGIN
		SET IDENTITY_INSERT [dbo].[M_KpiModules] ON
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(31,'CapTable6',3,GETDATE(),0,1,0,0,11,'Cap Table6','Cap Table6','CapTable6')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(32,'CapTable7',3,GETDATE(),0,1,0,0,12,'Cap Table7','Cap Table7','CapTable7')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(33,'CapTable8',3,GETDATE(),0,1,0,0,13,'Cap Table8','Cap Table8','CapTable8')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(34,'CapTable9',3,GETDATE(),0,1,0,0,14,'Cap Table9','Cap Table9','CapTable9')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(35,'CapTable10',3,GETDATE(),0,1,0,0,15,'Cap Table10','Cap Table10','CapTable10')

		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(36,'OtherCapTable1',3,GETDATE(),0,1,0,0,11,'Cap Table1','Cap Table1','OtherCapTable1')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(37,'OtherCapTable2',3,GETDATE(),0,1,0,0,12,'Cap Table2','Cap Table2','OtherCapTable2')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(38,'OtherCapTable3',3,GETDATE(),0,1,0,0,13,'Cap Table3','Cap Table3','OtherCapTable3')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(39,'OtherCapTable4',3,GETDATE(),0,1,0,0,14,'Cap Table4','Cap Table4','OtherCapTable4')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(40,'OtherCapTable5',3,GETDATE(),0,1,0,0,15,'Cap Table5','Cap Table5','OtherCapTable5')		
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(41,'OtherCapTable6',3,GETDATE(),0,1,0,0,11,'Cap Table6','Cap Table6','OtherCapTable6')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(42,'OtherCapTable7',3,GETDATE(),0,1,0,0,12,'Cap Table7','Cap Table7','OtherCapTable7')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(43,'OtherCapTable8',3,GETDATE(),0,1,0,0,13,'Cap Table8','Cap Table8','OtherCapTable8')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(44,'OtherCapTable9',3,GETDATE(),0,1,0,0,14,'Cap Table9','Cap Table9','OtherCapTable9')
		INSERT INTO M_KpiModules(ModuleID,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsFinacials,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(45,'OtherCapTable10',3,GETDATE(),0,1,0,0,15,'Cap Table10','Cap Table10','OtherCapTable10')
		SET IDENTITY_INSERT [dbo].[M_KpiModules] OFF
		END
GO
ALTER  PROCEDURE [dbo].[GetMasterkpiNotMappedList]
(
@companyId INT,
@Type Varchar(100),
@ModuleId INT = NULL
)
AS
BEGIN  
	IF @Type = 'Company KPI' 
							BEGIN
							SELECT DISTINCT(M.CompanyKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId', M.KpiInfo 'KpiInfo',M.Synonym,M.Description   FROM M_CompanyKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioCompanyKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CompanyKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MappingPortfolioCompanyKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Impact KPI'
							BEGIN
							SELECT DISTINCT(M.ImpactKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ImpactKPI M 
							LEFT JOIN (SELECT *FROM Mapping_ImpactKPI_Order 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ImpactKPIID = MAP.ImpactKPIID 
							WHERE M.IsDeleted =0  AND MAP.ImpactKPIMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Investment KPI'
							BEGIN
							SELECT DISTINCT(M.InvestmentKPIId) 'Id',M.KPI 'Name', M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_InvestmentKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioInvestmentKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.InvestmentKPIId = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioInvestmentKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Balance Sheet KPI'
							BEGIN
							SELECT DISTINCT(M.BalanceSheetLineItemID) 'Id',M.BalanceSheetLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_BalanceSheet_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyBalanceSheetLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.BalanceSheetLineItemID = MAP.BalanceSheetLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyBalanceSheetLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Cashflow KPI'
							BEGIN
							SELECT DISTINCT(M.CashFlowLineItemID) 'Id',M.CashFlowLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_CashFlow_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyCashFlowLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.CashFlowLineItemID = MAP.CashFlowLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyCashFlowLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Profit & Loss KPI'
							BEGIN
							SELECT DISTINCT(M.ProfitAndLossLineItemID) 'Id',M.ProfitAndLossLineItem 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_ProfitAndLoss_LineItems M 
							LEFT JOIN (SELECT *FROM Mapping_CompanyProfitAndLossLineItems 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.ProfitAndLossLineItemID = MAP.ProfitAndLossLineItemID 
							WHERE M.IsDeleted =0  AND MAP.CompanyProfitAndLossLineItemMappingID IS NULL order by Name
							END
	ELSE IF @Type = 'Operational KPI'
							BEGIN
							SELECT DISTINCT(M.SectorwiseOperationalKPIID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_SectorwiseOperationalKPI M 
							LEFT JOIN (SELECT *FROM Mapping_PortfolioOperationalKPI 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.SectorwiseOperationalKPIID = MAP.KpiID 
							WHERE M.IsDeleted =0  AND MAP.MappingPortfolioOperationalKPIId IS NULL order by Name
							END
	ELSE IF @Type = 'Credit KPI'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 2) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 2  AND Mapping_KpisID IS NULL order by Name
							END
	ELSE IF @Type = 'Trading Records'
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = 1) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = 1  AND Mapping_KpisID IS NULL order by Name
							END
    IF @ModuleId = 16
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKpiId 'FormulaKPIId',M.KpiInfo 'KpiInfo'  FROM MMonthlyReport M 
							LEFT JOIN (SELECT *FROM MappingMonthlyReport 
							where PortfolioCompanyID = @companyId AND IsDeleted=0) MAP ON M.KpiId = MAP.KpiId
							WHERE M.IsDeleted =0 AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45)
							BEGIN
							SELECT DISTINCT(M.KpiId) 'Id',M.Kpi 'Name',M.IsHeader 'IsHeader',M.IsBoldKpi 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',KpiTypeId,M.Synonym,M.Description  FROM MCapTable M 
							LEFT JOIN (SELECT *FROM MappingCapTable 
							where PortfolioCompanyId = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.KpiId = MAP.KpiId 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND MappingId IS NULL order by Name
							END
   IF @ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30)
							BEGIN
							SELECT DISTINCT(M.MasterKpiID) 'Id',M.KPI 'Name',M.IsHeader 'IsHeader',M.IsBoldKPI 'IsBoldKPI',M.Formula 'Formula',M.FormulaKPIId 'FormulaKPIId',M.KpiInfo 'KpiInfo',M.Synonym,M.Description  FROM M_MasterKpis M 
							LEFT JOIN (SELECT *FROM Mapping_Kpis 
							where PortfolioCompanyID = @companyId AND IsDeleted=0 AND ModuleID = @ModuleId) MAP ON M.MasterKpiID = MAP.KpiID 
							WHERE M.IsDeleted =0 AND M.ModuleID = @ModuleId  AND Mapping_KpisID IS NULL order by Name
							END
END
GO
ALTER PROCEDURE [dbo].[ProcCopyKPIToCompanies]
(
@KpiType VARCHAR(100),
@CompanyId INT,
@UserId INT,
@CompanyIds NVARCHAR(MAX),
@ModuleId INT = NULL
)
AS
BEGIN
SET NOCOUNT ON
			BEGIN TRY
			DECLARE @RowCount INT,@Row INT = 1;
			DROP TABLE IF EXISTS #tempCopyToCompany
			CREATE TABLE #tempCopyToCompany
			(
				Id INT IDENTITY(1,1) PRIMARY KEY,
				PortfolioCompanyID Int
			)
			INSERT INTO #tempCopyToCompany(PortfolioCompanyID)
			SELECT Item AS PortfolioCompanyID FROM[dbo].[SplitString](@CompanyIds,',') WHERE Item!=@CompanyId
			SET @RowCount = (SELECT Count(PortfolioCompanyID) FROM #tempCopyToCompany)
			WHILE (@Row <= @RowCount)
			BEGIN
			DROP TABLE IF EXISTS #KPIHierarchy;
            DROP TABLE IF EXISTS #ExistingKPIs;
			CREATE TABLE #ExistingKPIs ( KPIName VARCHAR(500), DestKpiID INT, ParentKPIID INT, ParentKPIName VARCHAR(500), DisplayOrder INT, IsRootLevel BIT ,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			CREATE TABLE #KPIHierarchy ( SourceKpiID INT, KPIName VARCHAR(MAX), ParentKPIID INT, ParentKPIName VARCHAR(MAX), Level INT, DisplayOrder INT, HierarchyOrder VARCHAR(MAX), NewDisplayOrder INT, KPITypeID INT ,Formula NVarchar(Max),FormulaKPIId INT,CreatedBy INT,CreatedOn datetime,Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @PortfolioCompanyID INT
			SELECT @PortfolioCompanyID=PortfolioCompanyID FROM #tempCopyToCompany Where ID= @Row
			DECLARE @MappingOperational table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCompany table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingImpact table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ImpactKPIID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,KPIOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingInvestment table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),KPIHierarchy Nvarchar(Max),HierarchyOrder NVARCHAR(500),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingBalance table(ID INT IDENTITY(1,1), PortfolioCompanyID int,BalanceSheetLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingProfit table(ID INT IDENTITY(1,1), PortfolioCompanyID int,ProfitAndLossLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCashflow table(ID INT IDENTITY(1,1), PortfolioCompanyID int,CashFlowLineItemID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentLineItemID int,DisplayOrder int,Formula nvarchar(max),FormulaKPIId nvarchar(max),SegmentType varchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingTrading table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCredit table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingCapTable table(ID INT IDENTITY(1,1), KPITypeID int,PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,ModuleID int,Formula nvarchar(max),FormulaKPIId nvarchar(max),Synonym varchar(3000),Definition VARCHAR(3000),IsExtraction BIT);
			DECLARE @MappingMonthlyReport table(ID INT IDENTITY(1,1),PortfolioCompanyID int,KpiID int,CreatedBy int,CreatedOn Datetime,IsDeleted bit,ParentKPIID int,DisplayOrder int,IsHeader int,Formula nvarchar(max),FormulaKPIId nvarchar(max));
			DELETE FROM @MappingOperational
			DELETE FROM @MappingCompany
			DELETE FROM @MappingImpact
			DELETE FROM @MappingInvestment
			DELETE FROM @MappingBalance
			DELETE FROM @MappingProfit
			DELETE FROM @MappingCashflow
			DELETE FROM @MappingTrading
			DELETE FROM @MappingCredit
			DELETE FROM @MappingCapTable
			DELETE FROM @MappingMonthlyReport
			Declare @MaxOrderId INT;
			Declare @ID INT,@ParentKPIID INT,@DestParentKPIID INT,@KpiID INT;
			Declare @KPI NVarchar(MAX)=NULL;
			IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO @MappingOperational 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioOperationalKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(SELECT * FROM Mapping_PortfolioOperationalKPI  WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingOperational ))
					BEGIN
						UPDATE Mapping_PortfolioOperationalKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioOperationalKPI A Inner Join @MappingOperational B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID 
					END							
				INSERT INTO Mapping_PortfolioOperationalKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction FROM  @MappingOperational WHERE  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioOperationalKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Company KPI')
			BEGIN
			 	INSERT INTO @MappingCompany 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_PortfolioCompanyKPI where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_PortfolioCompanyKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and KpiID IN (SELECT KpiID FROM @MappingCompany ))
					BEGIN
						UPDATE Mapping_PortfolioCompanyKPI SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioCompanyKPI A Inner Join @MappingCompany B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_PortfolioCompanyKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCompany where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioCompanyKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
			IF(@KpiType = 'Impact KPI')
			BEGIN
			 	INSERT INTO @MappingImpact 
				SELECT PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_ImpactKPI_Order where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_ImpactKPI_Order where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID  and ImpactKPIID IN (SELECT ImpactKPIID FROM @MappingImpact ))
					BEGIN
						UPDATE Mapping_ImpactKPI_Order SET KPIOrder=B.KPIOrder,ImpactKPIID=B.ImpactKPIID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_ImpactKPI_Order A Inner Join @MappingImpact B On A.ImpactKPIID=B.ImpactKPIID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_ImpactKPI_Order(PortfolioCompanyID,ImpactKPIID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ImpactKPIID,CreatedBy,GETDATE(),0,ParentKPIID,KPIOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingImpact where ImpactKPIID NOT IN (SELECT ImpactKPIID FROM Mapping_ImpactKPI_Order WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
			END
			IF(@KpiType = 'Investment KPI')
			BEGIN	
				SELECT @MaxOrderId = ISNULL(MAX(DisplayOrder), 0) FROM Mapping_PortfolioInvestmentKPI WHERE IsDeleted = 0 AND PortfolioCompanyID = @PortfolioCompanyID;
				;WITH KPIHierarchyCTE AS (
                    SELECT 
                        src.KpiID AS SourceKpiID,
                         CAST(srcKPI.KPI AS VARCHAR(MAX)) AS KPIName,
                        src.ParentKPIID,
                         CAST(NULL AS VARCHAR(MAX)) AS ParentKPIName,
                        0 AS Level,
                        src.DisplayOrder,
						CAST(RIGHT('000000' + CAST(DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) AS HierarchyOrder,
						src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.ParentKPIID IS NULL
                    AND src.IsDeleted = 0
                    UNION ALL
                    SELECT 
                        src.KpiID,
                        CAST(srcKPI.KPI AS VARCHAR(MAX)),
                        src.ParentKPIID,
                        CAST(parentKPI.KPI AS VARCHAR(MAX)),
                        h.Level + 1,
                        src.DisplayOrder,
						 CAST(h.HierarchyOrder + '.' + RIGHT('000000' + CAST(src.DisplayOrder AS VARCHAR(6)), 6) AS VARCHAR(MAX)) as HierarchyOrder,
						 src.KPITypeID,src.Formula,src.FormulaKPIId,src.CreatedBy,src.CreatedOn,src.Synonym,src.Definition,src.IsExtraction
                    FROM Mapping_PortfolioInvestmentKPI src
                    INNER JOIN M_InvestmentKPI srcKPI 
                        ON src.KpiID = srcKPI.InvestmentKPIId
                    INNER JOIN M_InvestmentKPI parentKPI 
                        ON src.ParentKPIID = parentKPI.InvestmentKPIId
                    INNER JOIN KPIHierarchyCTE h 
                        ON src.ParentKPIID = h.SourceKpiID
                    WHERE src.PortfolioCompanyID = @CompanyId
                    AND src.IsDeleted = 0
                )
				 INSERT INTO #KPIHierarchy(KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction)
                SELECT KPIName,ParentKPIID,ParentKPIName,Level,DisplayOrder,SourceKpiID,HierarchyOrder,CASE WHEN @MaxOrderId=0 THEN
				        ROW_NUMBER() OVER (ORDER BY HierarchyOrder) 
						ELSE @MaxOrderId+1 + ROW_NUMBER() OVER (ORDER BY HierarchyOrder) END AS NewDisplayOrder,KPITypeID,Formula,FormulaKPIId,CreatedBy,CreatedOn,Synonym,Definition,IsExtraction FROM KPIHierarchyCTE;

				-- Get existing destination KPIs
                INSERT INTO #ExistingKPIs
                SELECT DISTINCT
                    destKPI.KPI,
                    dest.KpiID,
                    dest.ParentKPIID,
                    parentKPI.KPI,
                    dest.DisplayOrder,
                    CASE WHEN dest.ParentKPIID IS NULL THEN 1 ELSE 0 END,
					dest.Synonym,
					dest.Definition,
					dest.IsExtraction
                FROM Mapping_PortfolioInvestmentKPI dest
                INNER JOIN M_InvestmentKPI destKPI 
                    ON dest.KpiID = destKPI.InvestmentKPIId
                LEFT JOIN M_InvestmentKPI parentKPI 
                    ON dest.ParentKPIID = parentKPI.InvestmentKPIId
                WHERE dest.PortfolioCompanyID = @PortfolioCompanyID
                AND dest.IsDeleted = 0;

				--SELECT * FROM #KPIHierarchy

				INSERT INTO @MappingInvestment(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,HierarchyOrder,Synonym,Definition,IsExtraction) 
				SELECT 
                    kh.KPITypeID,
                    @PortfolioCompanyID,
                    kh.SourceKpiID,
                    @UserId,
                    GETDATE(),
                    0,
                    CASE 
					    WHEN kh.ParentKPIID IS NOT NULL THEN
					        CASE WHEN EXISTS (
					            SELECT 1 
					            FROM #ExistingKPIs e 
					            WHERE e.KPIName = kh.ParentKPIName
					            AND e.IsRootLevel = 1
					        )
					        THEN 
					            (SELECT TOP 1 e.DestKpiID 
					             FROM #ExistingKPIs e 
					             WHERE e.KPIName = kh.ParentKPIName
					             AND e.IsRootLevel = 1)
					        ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					        END
					    ELSE kh.ParentKPIID  -- Use ParentKPIID instead of NULL
					END ParentKPIID,
					kh.NewDisplayOrder,kh.Formula,kh.FormulaKPIId,kh.HierarchyOrder,kh.Synonym,kh.Definition,kh.IsExtraction
                FROM #KPIHierarchy kh
                LEFT JOIN #ExistingKPIs existing
                    ON existing.KPIName = kh.KPIName
                    AND existing.IsRootLevel = CASE WHEN kh.ParentKPIID IS NULL THEN 1 ELSE 0 END
                WHERE NOT EXISTS (
                    SELECT 1 FROM #ExistingKPIs e
                    WHERE e.KPIName = kh.KPIName
                    AND (
                        (kh.ParentKPIID IS NULL AND e.IsRootLevel = 1) OR
                        (kh.ParentKPIID IS NOT NULL AND e.ParentKPIName = kh.ParentKPIName)
                    )
                );
			   IF EXISTS(select *from Mapping_PortfolioInvestmentKPI where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingInvestment))
				BEGIN
					UPDATE Mapping_PortfolioInvestmentKPI SET DisplayOrder=B.DisplayOrder ,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_PortfolioInvestmentKPI A Inner Join @MappingInvestment B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
			   END
			   INSERT INTO Mapping_PortfolioInvestmentKPI(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
			   SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingInvestment where  KpiID NOT IN (SELECT KpiID FROM Mapping_PortfolioInvestmentKPI WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
				
				DROP TABLE IF EXISTS #KPIHierarchy;
                DROP TABLE IF EXISTS #ExistingKPIs;
			
			END
			IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
			 	INSERT INTO @MappingBalance 
				SELECT PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyBalanceSheetLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0

				IF EXISTS(select *from Mapping_CompanyBalanceSheetLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and BalanceSheetLineItemID IN (SELECT BalanceSheetLineItemID FROM @MappingBalance))
					BEGIN
						UPDATE Mapping_CompanyBalanceSheetLineItems SET DisplayOrder=B.DisplayOrder,BalanceSheetLineItemID=B.BalanceSheetLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyBalanceSheetLineItems A Inner Join @MappingBalance B On A.BalanceSheetLineItemID=B.BalanceSheetLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyBalanceSheetLineItems(PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,BalanceSheetLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingBalance where  BalanceSheetLineItemID NOT IN (SELECT BalanceSheetLineItemID FROM Mapping_CompanyBalanceSheetLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO @MappingCashflow 
				SELECT PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyCashFlowLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyCashFlowLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and CashFlowLineItemID IN (SELECT CashFlowLineItemID FROM @MappingCashflow))
					BEGIN
						UPDATE Mapping_CompanyCashFlowLineItems SET DisplayOrder=B.DisplayOrder,CashFlowLineItemID=B.CashFlowLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyCashFlowLineItems A Inner Join @MappingCashflow B On A.CashFlowLineItemID=B.CashFlowLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyCashFlowLineItems(PortfolioCompanyID,CashFlowLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,CashFlowLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCashflow where  CashFlowLineItemID NOT IN (SELECT CashFlowLineItemID FROM Mapping_CompanyCashFlowLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Profit & Loss KPI')
			BEGIN
				INSERT INTO @MappingProfit 
				SELECT PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),IsDeleted,ParentLineItemID,DisplayOrder,Formula,FormulaKPIId,SegmentType,Synonym,Definition,IsExtraction From Mapping_CompanyProfitAndLossLineItems where PortfolioCompanyID = @CompanyId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_CompanyProfitAndLossLineItems where  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ProfitAndLossLineItemID IN (SELECT ProfitAndLossLineItemID FROM @MappingProfit))
					BEGIN
						UPDATE Mapping_CompanyProfitAndLossLineItems SET DisplayOrder=B.DisplayOrder,ProfitAndLossLineItemID=B.ProfitAndLossLineItemID,ParentLineItemID=B.ParentLineItemID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_CompanyProfitAndLossLineItems A Inner Join @MappingProfit B On A.ProfitAndLossLineItemID=B.ProfitAndLossLineItemID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID
					END							
				INSERT INTO Mapping_CompanyProfitAndLossLineItems(PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,CreatedOn,IsDeleted,ParentLineItemID,DisplayOrder,IsActive,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,ProfitAndLossLineItemID,CreatedBy,GETDATE(),0,ParentLineItemID,DisplayOrder,1,SegmentType,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingProfit where  ProfitAndLossLineItemID NOT IN (SELECT ProfitAndLossLineItemID FROM Mapping_CompanyProfitAndLossLineItems WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID )
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
			 	INSERT INTO @MappingTrading 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=1 AND IsDeleted = 0
				
				IF EXISTS(select *from Mapping_Kpis where ModuleID=1 and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingTrading))
					BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingTrading B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=1
					END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingTrading where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=1 )
												
			END
		IF(@KpiType = 'Credit KPI' OR @ModuleId IN (2, 17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
			 	INSERT INTO @MappingCredit 
				SELECT KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From Mapping_Kpis where PortfolioCompanyID = @CompanyId  AND ModuleID=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from Mapping_Kpis where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and KpiID IN (SELECT KpiID FROM @MappingCredit))
				BEGIN
						UPDATE Mapping_Kpis SET DisplayOrder=B.DisplayOrder,KpiID=B.KpiID,ParentKPIID=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM Mapping_Kpis A Inner Join @MappingCredit B On A.KpiID=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyID= @PortfolioCompanyID AND A.ModuleID=@ModuleId
				END							
				INSERT INTO Mapping_Kpis(KPITypeID,PortfolioCompanyID,KpiID,CreatedBy,CreatedOn,IsDeleted,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT KPITypeID,@PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCredit where  KpiID NOT IN (SELECT KpiID FROM Mapping_Kpis WHERE  IsDeleted =0 and PortfolioCompanyID = @PortfolioCompanyID and ModuleID=@ModuleId )												
			END
			IF(@ModuleId = 16)
			BEGIN
			 	INSERT INTO @MappingMonthlyReport 
				SELECT PortfolioCompanyId,KpiId,CreatedBy,GETUTCDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKpiId From MappingMonthlyReport where PortfolioCompanyId = @CompanyId  AND IsDeleted = 0
				IF EXISTS(select *from MappingMonthlyReport where  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingMonthlyReport))
				BEGIN
						UPDATE MappingMonthlyReport SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKpiId,Formula=B.Formula,FormulaKpiId=B.FormulaKpiId FROM MappingMonthlyReport A Inner Join @MappingMonthlyReport B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID
				END							
				INSERT INTO MappingMonthlyReport(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,Formula,FormulaKPIId) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETUTCDATE(),0,ParentKPIID,DisplayOrder,IsHeader,Formula,FormulaKPIId From @MappingMonthlyReport where  KpiID NOT IN (SELECT KpiID FROM MappingMonthlyReport WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID)												
			END
		IF(@ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN
			 	INSERT INTO @MappingCapTable 
				SELECT 1,PortfolioCompanyId,KpiId,CreatedBy,GETDATE(),IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From MappingCapTable where PortfolioCompanyId = @CompanyId  AND ModuleId=@ModuleId AND IsDeleted = 0
				IF EXISTS(select *from MappingCapTable where ModuleID=@ModuleId and IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and KpiId IN (SELECT KpiID FROM @MappingCapTable))
				BEGIN
						UPDATE MappingCapTable SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiID,ParentKpiId=B.ParentKPIID,Formula=B.Formula,FormulaKPIId=B.FormulaKPIId,Synonym =B.Synonym,Definition=B.Definition,IsExtraction=B.IsExtraction FROM MappingCapTable A Inner Join @MappingCapTable B On A.KpiId=B.KpiID WHERE A.IsDeleted=0 and A.PortfolioCompanyId= @PortfolioCompanyID AND A.ModuleId=@ModuleId
				END							
				INSERT INTO MappingCapTable(PortfolioCompanyId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,IsHeader,ModuleId,Formula,FormulaKPIId,Synonym,Definition,IsExtraction) 
				SELECT @PortfolioCompanyID,KpiID,CreatedBy,GETDATE(),0,ParentKPIID,DisplayOrder,IsHeader,ModuleID,Formula,FormulaKPIId,Synonym,Definition,IsExtraction From @MappingCapTable where  KpiID NOT IN (SELECT KpiID FROM MappingCapTable WHERE  IsDeleted =0 and PortfolioCompanyId = @PortfolioCompanyID and ModuleId=@ModuleId)												
			END
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempCopyToCompany		
			END TRY
			BEGIN CATCH
              IF OBJECT_ID('tempdb..#KPIHierarchy') IS NOT NULL DROP TABLE #KPIHierarchy;
			  IF OBJECT_ID('tempdb..#ExistingKPIs') IS NOT NULL DROP TABLE #ExistingKPIs;
			END CATCH
END
GO
IF NOT EXISTS (
    SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID('M_SubPageFields') 
    AND name = 'IsPcLink'
)
BEGIN
    ALTER TABLE M_SubPageFields
    ADD IsPcLink BIT NOT NULL DEFAULT 0;
    
    PRINT 'IsPcLink column added to M_SubPageFields table';
END
ELSE
BEGIN
    PRINT 'IsPcLink column already exists in M_SubPageFields table';
END 
go
UPDATE M_SubPageFields SET IsPcLink =0 WHERE IsPcLink IS NULL
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[M_SubPageDetails] 
				 where Name='Fund Financial Section'    
                ))
BEGIN
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] ON 
INSERT [dbo].[M_SubPageDetails] ([SubPageID], [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported],IsDragDrop,IsDataType) VALUES (49, N'Fund Financial Section', N'Fund Financial Section', 2, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0, 0,1,0)
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] OFF
END
GO

IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials1'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials1', N'FundFinancials1', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials2'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials2', N'FundFinancials2', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 3, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials3'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials3', N'FundFinancials3', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 4, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials4'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials4', N'FundFinancials4', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 5, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials5'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials5', N'FundFinancials5', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 6, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials6'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials6', N'FundFinancials6', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials7'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials7', N'FundFinancials7', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 8, NULL, 0)
END
GO
/* Fund Financial Section */
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials1'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials1', N'FundKeyFinancials1', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials2'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials2', N'FundKeyFinancials2', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 2, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials3'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials3', N'FundKeyFinancials3', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 3, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials4'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials4', N'FundKeyFinancials4', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 4, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials5'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials5', N'FundKeyFinancials5', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 5, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials6'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials6', N'FundKeyFinancials6', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 6, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials7'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials7', N'FundKeyFinancials7', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundKeyFinancials8'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundKeyFinancials8', N'FundKeyFinancials8', 49, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 8, NULL, 0)
END
GO
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials1'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials2'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials3'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials4'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials5'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials6'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials7'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go

IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Actual', N'Actual', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Budget', N'Budget', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Forecast', N'Forecast', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'IC', N'IC', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Actual LTM', N'Actual LTM', 49, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Budget LTM', N'Budget LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Forecast LTM', N'Forecast LTM', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Actual YTD', N'Actual YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Budget YTD', N'Budget YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Forecast YTD', N'Forecast YTD', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8'), N'Since Inception', N'Since Inception', 49, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[MFundKpiModules] where Name='FundFinancials1'
                ))
		BEGIN
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] ON
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1002,'FundFinancials1',3,GETDATE(),0,1,0,2,'Fund Financials1','Fund Financials1','FundFinancials1')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1003,'FundFinancials2',3,GETDATE(),0,1,0,3,'Fund Financials2','Fund Financials2','FundFinancials2')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1004,'FundFinancials3',3,GETDATE(),0,1,0,4,'Fund Financials3','Fund Financials3','FundFinancials3')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1005,'FundFinancials4',3,GETDATE(),0,1,0,5,'Fund Financials4','Fund Financials4','FundFinancials4')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1006,'FundFinancials5',3,GETDATE(),0,1,0,6,'Fund Financials5','Fund Financials5','FundFinancials5')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1007,'FundFinancials6',3,GETDATE(),0,1,0,7,'Fund Financials6','Fund Financials6','FundFinancials6')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1008,'FundFinancials7',3,GETDATE(),0,1,0,8,'Fund Financials7','Fund Financials7','FundFinancials7')
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] OFF
		END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[MFundKpiModules] where Name='FundKeyFinancials1'
                ))
		BEGIN
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] ON
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1009,'FundKeyFinancials1',3,GETDATE(),0,1,0,11,'Fund Key Financials1','Fund Key Financials1','FundKeyFinancials1')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1010,'FundKeyFinancials2',3,GETDATE(),0,1,0,12,'Fund Key Financials2','Fund Key Financials2','FundKeyFinancials2')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1011,'FundKeyFinancials3',3,GETDATE(),0,1,0,13,'Fund Key Financials3','Fund Key Financials3','FundKeyFinancials3')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1012,'FundKeyFinancials4',3,GETDATE(),0,1,0,14,'Fund Key Financials4','Fund Key Financials4','FundKeyFinancials4')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1013,'FundKeyFinancials5',3,GETDATE(),0,1,0,15,'Fund Key Financials5','Fund Key Financials5','FundKeyFinancials5')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1014,'FundKeyFinancials6',3,GETDATE(),0,1,0,16,'Fund Key Financials6','Fund Key Financials6','FundKeyFinancials6')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1015,'FundKeyFinancials7',3,GETDATE(),0,1,0,17,'Fund Key Financials7','Fund Key Financials7','FundKeyFinancials7')
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1016,'FundKeyFinancials8',3,GETDATE(),0,1,0,18,'Fund Key Financials8','Fund Key Financials8','FundKeyFinancials8')
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] OFF
		END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'M_SubPageFields')
    AND NOT EXISTS (
        SELECT * FROM sys.columns 
        WHERE object_id = OBJECT_ID('dbo.M_SubPageFields') 
        AND name = 'IsTrend'
    )
BEGIN
    ALTER TABLE dbo.M_SubPageFields ADD IsTrend BIT NOT NULL DEFAULT 0;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GetCurrencyRates]'))
BEGIN
	 DROP PROCEDURE GetCurrencyRates
END
GO
CREATE PROCEDURE GetCurrencyRates
    @FromDate DATE = NULL,
    @ToDate DATE = NULL,
    @FilterSource NVARCHAR(500) = NULL,
    @FromCurrencyCode NVARCHAR(10) = NULL,
    @ToCurrencyCode NVARCHAR(10) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT  FromCurrencyId,
            FromCurrencyCode,
            ToCurrencyId,
            ToCurrencyCode,
            CONVERT(varchar, Rate) AS Rate,
            FORMAT(CAST(Date AS date), 'MMM yyyy') AS [Date],
            Source
    FROM (
        SELECT 
            cr.FromCurrencyId,
            fc.CurrencyCode AS FromCurrencyCode,
            cr.ToCurrencyId,
            tc.CurrencyCode AS ToCurrencyCode,
            Rate,
            Date,
            'Bulk Upload' AS Source
        FROM 
            CurrencyRates cr 
            LEFT JOIN M_Currency fc ON cr.FromCurrencyId = fc.CurrencyID
            LEFT JOIN M_Currency tc ON cr.ToCurrencyId = tc.CurrencyID
        WHERE 
            cr.IsDeleted = 0

        UNION ALL

        SELECT 
            cr.FromCurrencyId,
            fc.CurrencyCode AS FromCurrencyCode,
            cr.ToCurrencyId,
            tc.CurrencyCode AS ToCurrencyCode,
            CONVERT(varchar, cr.Rate) AS Rate,
            CONVERT(varchar, cr.Date) AS [Date],
            'System API' AS Source
        FROM 
            ApiCurrencyRates cr
            LEFT JOIN M_Currency fc ON cr.FromCurrencyId = fc.CurrencyID
            LEFT JOIN M_Currency tc ON cr.ToCurrencyId = tc.CurrencyID
        WHERE 
            cr.IsDeleted = 0
    ) AS AllRates
    WHERE
        (@FromDate IS NULL OR @FromDate = '' OR  AllRates.Date >= @FromDate)
        AND (@ToDate IS NULL OR @ToDate = '' OR AllRates.Date <= @ToDate)
        AND (@FromCurrencyCode IS NULL OR @FromCurrencyCode = '' OR AllRates.FromCurrencyCode = @FromCurrencyCode)
        AND (@ToCurrencyCode IS NULL OR @ToCurrencyCode = '' OR AllRates.ToCurrencyCode = @ToCurrencyCode)
        AND (
            @FilterSource IS NULL OR @FilterSource = ''
            OR EXISTS (
                SELECT 1
                FROM STRING_SPLIT(REPLACE(@FilterSource, ' ', ''), ',')
                WHERE value = REPLACE(AllRates.Source, ' ', '')
            )
        )
END
GO
ALTER PROCEDURE   [dbo].[spDeleteKpi]
(
@KpiType VARCHAR(500),
@ModuleId INT = NULL,
@KPIId INT
)
AS BEGIN
	SET NOCOUNT ON;
	IF(@KpiType = 'TradingRecords' OR @KpiType = 'CreditKPI' OR @KpiType LIKE '%Custom%' OR @KpiType LIKE '%OtherKpi%')
	BEGIN
		UPDATE M_MasterKpis SET IsDeleted = 1 WHERE MasterKpiID = @KPIId AND ModuleID= @ModuleId
		UPDATE Mapping_Kpis SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_Kpis SET IsDeleted = 1 WHERE KpiID = @KPIId
	END
	ELSE IF(@KpiType = 'Operational')
	BEGIN
		UPDATE M_SectorwiseOperationalKPI SET IsDeleted = 1 WHERE SectorwiseOperationalKPIID = @KPIId
		UPDATE Mapping_PortfolioOperationalKPI SET IsDeleted = 1 WHERE KpiID = @KPIId
		UPDATE Mapping_PortfolioOperationalKPI SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
	END
	ELSE IF(@KpiType = 'Investment')
	BEGIN
		UPDATE M_InvestmentKPI SET IsDeleted = 1 WHERE InvestmentKPIId = @KPIId
		UPDATE Mapping_PortfolioInvestmentKPI SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_PortfolioInvestmentKPI SET IsDeleted = 1 WHERE KpiID = @KPIId
	END
	ELSE IF(@KpiType = 'Company')
	BEGIN
		UPDATE M_CompanyKPI SET IsDeleted = 1 WHERE CompanyKPIID = @KPIId
		UPDATE Mapping_PortfolioCompanyKPI SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_PortfolioCompanyKPI SET IsDeleted = 1 WHERE KpiID = @KPIId
	END
	ELSE IF(@KpiType = 'Impact')
	BEGIN
		UPDATE M_ImpactKPI SET IsDeleted = 1 WHERE ImpactKPIID = @KPIId
		UPDATE Mapping_ImpactKPI_Order SET ParentKPIID = NULL WHERE ParentKPIID = @KPIId
		UPDATE Mapping_ImpactKPI_Order SET IsDeleted = 1 WHERE ImpactKPIID = @KPIId
	END
	ELSE IF(@KpiType = 'ProfitAndLoss')
	BEGIN
		UPDATE M_ProfitAndLoss_LineItems SET IsDeleted = 1 WHERE ProfitAndLossLineItemID = @KPIId
		UPDATE Mapping_CompanyProfitAndLossLineItems SET ParentLineItemID = NULL WHERE ParentLineItemID = @KPIId
		UPDATE Mapping_CompanyProfitAndLossLineItems SET IsDeleted = 1 WHERE ProfitAndLossLineItemID = @KPIId
	END
	ELSE IF(@KpiType = 'BalanceSheet')
	BEGIN
		UPDATE M_BalanceSheet_LineItems SET IsDeleted = 1 WHERE BalanceSheetLineItemID = @KPIId
		UPDATE Mapping_CompanyBalanceSheetLineItems SET ParentLineItemID = NULL WHERE ParentLineItemID = @KPIId
		UPDATE Mapping_CompanyBalanceSheetLineItems SET IsDeleted = 1 WHERE BalanceSheetLineItemID = @KPIId
	END
	ELSE IF(@KpiType = 'CashFlow')
	BEGIN
		UPDATE M_CashFlow_LineItems SET IsDeleted = 1 WHERE CashFlowLineItemID = @KPIId
	    UPDATE Mapping_CompanyCashFlowLineItems SET ParentLineItemID = NULL WHERE ParentLineItemID = @KPIId
		UPDATE Mapping_CompanyCashFlowLineItems SET IsDeleted = 1 WHERE CashFlowLineItemID = @KPIId
	END	
	ELSE IF(@KpiType LIKE '%CapTable%')
	BEGIN
		UPDATE MCapTable SET IsDeleted = 1 WHERE KpiId = @KPIId AND ModuleId = @ModuleId
	    UPDATE MappingCapTable SET ParentKpiId = NULL WHERE ParentKpiId = @KPIId AND ModuleId = @ModuleId
		UPDATE MappingCapTable SET IsDeleted = 1 WHERE KpiId = @KPIId AND ModuleId = @ModuleId
	END	
	ELSE IF(@KpiType = 'MonthlyReport')
	BEGIN
		UPDATE MMonthlyReport SET IsDeleted = 1 WHERE KpiId = @KPIId
	    UPDATE MappingMonthlyReport SET ParentKpiId = NULL WHERE ParentKpiId = @KPIId
		UPDATE MappingMonthlyReport SET IsDeleted = 1 WHERE KpiId = @KPIId
	END	
END
GO
IF (NOT EXISTS (SELECT  [PageID] 

                 FROM [dbo].[M_PageDetails] 

				 where Name IN('Data Ingestion')				          

                ))

BEGIN

INSERT [dbo].[M_PageDetails] ( [Name], [AliasName], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES 

(N'Data Ingestion', N'Data Ingestion', NULL, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, NULL, NUll, 0)

END

GO

IF NOT EXISTS(SELECT * FROM M_SubPageDetails WHERE PageID in(

SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion'))

BEGIN

  INSERT INTO M_SubPageDetails(Name,AliasName,PageID,isActive,isDeleted,CreatedOn,CreatedBy,SequenceNo,IsCustom,IsDynamicFieldSupported)

  SELECT 'Data Ingestion','Data Ingestion',(SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion'),1,0,Getdate(),1,1,0,0
 
END

GO

IF NOT EXISTS(Select * from M_SubPageFields Where SubPageID in(

SELECT SubPageID FROM M_SubPageDetails WHERE PageID in(

SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion') AND Name='Data Ingestion'))

BEGIN
 
INSERT INTO M_SubPageFields(Name,AliasName,SubPageID,isActive,isDeleted,CreatedBy,CreatedOn,IsCustom,SequenceNo,IsListData,ShowOnList)

SELECT 'Specific Kpi Extraction','Specific Kpi',(SELECT SubPageID FROM M_SubPageDetails WHERE PageID 

in(SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion') AND Name='Data Ingestion'),1,0,1,getdate(),0,1,0,0
 
 
INSERT INTO M_SubPageFields(Name,AliasName,SubPageID,isActive,isDeleted,CreatedBy,CreatedOn,IsCustom,SequenceNo,IsListData,ShowOnList)

SELECT 'AsIs Extraction','AsIs Extraction',(SELECT SubPageID FROM M_SubPageDetails WHERE PageID 

in(SELECT PageID FROM M_PageDetails  WHERE Name='Data Ingestion') AND Name='Data Ingestion'),1,0,1,getdate(),0,2,0,0

END
GO
ALTER PROCEDURE  [dbo].[ProcCreateDuplicateKPI]
(
@KpiType VARCHAR(100),
@KpiId INT,
@UserId INT,
@ModuleId INT=NULL,
@Id INT OUTPUT
)
AS
BEGIN
SET NOCOUNT ON
		 BEGIN TRY
		 IF(@KpiType = 'Operational KPI')
			BEGIN
				INSERT INTO M_SectorwiseOperationalKPI(SectorID,KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT SectorID,KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Company KPI')
			BEGIN
				INSERT INTO M_CompanyKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,Description,Formula,FormulaKPIId,IsBoldKPI,IsHeader,Synonym FROM M_CompanyKPI WHERE CompanyKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		  IF(@KpiType = 'Impact KPI')
			BEGIN
				INSERT INTO M_ImpactKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,Description,ParentID,IsParent,Formula,FormulaKPIId,Synonym FROM M_ImpactKPI WHERE ImpactKPIID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Investment KPI')
			BEGIN
				INSERT INTO M_InvestmentKPI(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsNumeric,OrderBy,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,0,0,Description,Formula,FormulaKPIId,MethodologyId,IsBoldKPI,IsHeader,Synonym FROM M_InvestmentKPI WHERE InvestmentKPIId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		 IF(@KpiType = 'Balance Sheet KPI')
			BEGIN
				INSERT INTO M_BalanceSheet_LineItems(BalanceSheetLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT BalanceSheetLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,DisplayOrder,Description,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Cashflow KPI')
			BEGIN
				INSERT INTO M_CashFlow_LineItems(CashFlowLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym) SELECT CashFlowLineItem,KpiInfo,@UserId,GETDATE(),0,1,IsHeader,IsBoldKPI,MethodologyID,Description,DisplayOrder,IsCalculatedValue,Formula,FormulaKPIId,Synonym FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Profit And Loss KPI')
			BEGIN
				INSERT INTO M_ProfitAndLoss_LineItems(ProfitAndLossLineItem,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsActive,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym) SELECT ProfitAndLossLineItem,KpiInfo,@UserId,GETDATE(),0,1,MethodologyID,IsHeader,IsBoldKPI,Description,DisplayOrder,IsCalculatedValue,IsHighlighted,Formula,FormulaKPIId,Synonym FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Trading Records')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,1,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@KpiType = 'Credit KPI')
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,2,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId  = 16)
			BEGIN
				INSERT INTO MMonthlyReport(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId) SELECT Kpi,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKpi,Description,Formula,FormulaKpiId FROM MMonthlyReport WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (11,12,13,14,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45))
			BEGIN
				INSERT INTO MCapTable(Kpi,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKpi,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym) SELECT KPI,KpiInfo,@UserId,GETUTCDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,KpiTypeId,Synonym FROM MCapTable WHERE KpiId = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
		IF(@ModuleId IN (17,18,19,20,21,22,23,24,25,26,27,28,29,30))
			BEGIN
				INSERT INTO M_MasterKpis(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,IsHeader,IsBoldKPI,ModuleID,MethodologyID,Description,Formula,FormulaKPIId,Synonym) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,IsHeader,IsBoldKPI,@ModuleId,MethodologyID,Description,Formula,FormulaKPIId,Synonym FROM M_MasterKpis WHERE MasterKpiID = @KpiId
				SET @Id = SCOPE_IDENTITY()
			END
         END TRY
         BEGIN CATCH
             SET @Id = 0
         END CATCH
END
GO
CREATE OR ALTER PROCEDURE [dbo].[spBulkUploadFundFinancials]  
	 @TableName VARCHAR(100)
	,@UserID INT
	,@DocumentId INT = NULL
    ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
    ,@CommentId INT = NULL
	,@FundId INT
	,@IsIngestion BIT = 0
	,@ProcessId NVARCHAR(1000) = NULL
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @SQLString NVARCHAR(MAX);
	DECLARE @uploadType NVARCHAR(MAX)
	IF @IsIngestion = 1
		SET @uploadType = 'Ingestion'
	ELSE
		SET @uploadType = 'File Upload'
	DECLARE @KpiTable TABLE (
    [Id] [INT],
	[KpiId][int], 
	[Quarter] [nvarchar](10) NULL,
	[Year] [int] NOT NULL,
	[Month] [int] NULL,
	[KpiValue] NVARCHAR(MAX) NULL,
	[ValueTypeId] INT NULL,
	[ModuleId] [INT])
	SET @SQLString = 'SELECT Id,KpiId,Quarter,Year,Month,KpiValue,ValueTypeId,ModuleId FROM ' + @TableName + '';
	INSERT INTO @KpiTable
	EXEC sys.sp_executesql @SQLString;
	BEGIN TRY
	BEGIN TRANSACTION
	--Audit Log Entry for Update--
	---First entry for audit log OLD values for prod data
	INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,V.DocumentId,V.SupportingDocumentsId,V.CommentId)
	SELECT DISTINCT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,NULL,V.KPIValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,V.DocumentId,V.SupportingDocumentsId,V.CommentId 
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND MAP.KpiID = KPI.KpiId AND MAP.IsDeleted = 0  
	INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
	INNER JOIN FundMasterKpiValues V ON V.FundId = @FundId AND V.ModuleID = KPI.ModuleId  AND V.MappingId =MAP.MappingFundSectionKpiID 
		AND V.MappingId = Map.MappingFundSectionKpiID AND V.FundId = @FundId AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	LEFT JOIN FundKpiAuditLog A ON A.AttributeId = V.FundMasterKpiValueId AND A.ModuleId =KPI.ModuleId AND A.FundId = @FundId
	WHERE Map.FundId = @FundId  
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
	AND KPI.KpiValue IS NOT NULL AND A.AuditId IS NULL AND A.AttributeId IS NULL AND V.FundMasterKpiValueId IS NOT NULL 

	---insert audit log data before update for old values
	INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,DocumentId,SupportingDocumentsId,CommentId)
	SELECT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,V.KPIValue,KPI.KpiValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,@DocumentId,@SupportingDocumentsId,@CommentId 
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	WHERE 
	Map.FundId = @FundId 
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
	AND KPI.KpiValue IS NOT NULL

	--Update existing data--
	UPDATE V
	SET V.KPIValue = KPI.KpiValue,V.DocumentId = @DocumentId,V.SupportingDocumentsId = @SupportingDocumentsId,V.CommentId =@CommentId, V.ProcessId = @ProcessId
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND Map.IsDeleted = 0
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
INNER JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	WHERE 
	 Map.FundId = @FundId 
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
    AND KPI.KpiValue IS NOT NULL

	--Insert New Data---
	INSERT INTO FundMasterKpiValues(FundId, MappingId, KPIValue, KPIInfo, Month, Year, Quarter, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, ValueTypeID,[ModuleID],DocumentId,SupportingDocumentsId,CommentId,ProcessId)
	SELECT @FundId, Map.MappingFundSectionKpiID, KPI.KpiValue, M.KpiInfo, KPI.Month, KPI.Year, KPI.Quarter, GETDATE(), @UserID, NULL, NULL, 0, T.ValueTypeID,KPI.ModuleId,@DocumentId,@SupportingDocumentsId,@CommentId,@ProcessId
	from @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND Map.IsDeleted = 0
INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
LEFT JOIN FundMasterKpiValues V ON V.FundId = Map.FundId AND V.MappingId = Map.MappingFundSectionKpiID AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	WHERE Map.FundId = @FundId 
	AND V.MappingId IS NULL 
	AND KPI.KpiValue IS NOT NULL;

	---insert audit log data after insert
	INSERT INTO FundKpiAuditLog (ModuleId,FundId,AttributeId,OldValue,NewValue,OldCurrency,NewCurrency,AuditType,IsDeleted,CreatedOn,CreatedBy,V.DocumentId,V.SupportingDocumentsId,V.CommentId)
	SELECT DISTINCT KPI.ModuleId,@FundId,V.FundMasterKpiValueId,NULL,V.KPIValue,V.KPIInfo,M.KpiInfo,@uploadType,0,GETDATE(),@UserID,V.DocumentId,V.SupportingDocumentsId,V.CommentId 
	FROM @KpiTable KPI
	INNER JOIN MFundSectionKpi M on M.FundSectionKpiId = KPI.KpiId AND M.IsDeleted = 0
	INNER JOIN MappingFundSectionKpi Map on Map.KpiID = M.FundSectionKpiId and Map.ModuleID = KPI.ModuleId AND MAP.KpiID = KPI.KpiId AND MAP.IsDeleted = 0  
	INNER JOIN M_ValueTypes T ON  T.ValueTypeID = KPI.ValueTypeId
	INNER JOIN FundMasterKpiValues V ON V.FundId = @FundId AND V.ModuleID = KPI.ModuleId  AND V.MappingId =MAP.MappingFundSectionKpiID 
		AND V.MappingId = Map.MappingFundSectionKpiID AND V.FundId = @FundId AND V.IsDeleted = 0
		AND ISNULL(V.Month, - 1) = ISNULL(KPI.Month, - 1)
		AND ISNULL(V.Quarter, - 1) = ISNULL(KPI.Quarter, - 1)
		AND ISNULL(V.Year, - 1) = ISNULL(KPI.Year, - 1)
		AND V.ValueTypeID = T.ValueTypeID
	LEFT JOIN FundKpiAuditLog A ON A.AttributeId = V.FundMasterKpiValueId AND A.ModuleId =KPI.ModuleId AND A.FundId = @FundId
	WHERE Map.FundId = @FundId  
	AND V.ModuleID = KPI.ModuleId
	AND V.MappingId IS NOT NULL 
	AND KPI.KpiValue IS NOT NULL AND A.AuditId IS NULL AND A.AttributeId IS NULL AND V.FundMasterKpiValueId IS NOT NULL 

	COMMIT TRANSACTION
	END TRY
	BEGIN CATCH

		DECLARE @Message varchar(MAX) = ERROR_MESSAGE(),
        @Number int = ERROR_NUMBER(),
        @State smallint = ERROR_STATE();
		
		THROW 50000, @Message, @State;


	ROLLBACK TRANSACTION
	END CATCH
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 6')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (94, N'Cap Table 6', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'CapTable6')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 7')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (95, N'Cap Table 7', 14, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'CapTable7')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 8')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], 
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (96, N'Cap Table 8', 14, NULL,1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'CapTable8')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 9')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (97, N'Cap Table 9', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'CapTable9')
END
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Cap Table 10')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (98, N'Cap Table 10', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'CapTable10')
END
GO
-- Cap Table 6
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 6'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 7
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 7'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 8
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 8'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 9
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 9'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Cap Table 10
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Cap Table 10'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- othercap table

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 1')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (99, N'Other Cap Table 1', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable1')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 2')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (100, N'Other Cap Table 2', 14, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'OtherCapTable2')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 3')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], 
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (101, N'Other Cap Table 3', 14, NULL,1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'OtherCapTable3')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 4')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (102, N'Other Cap Table 4', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'OtherCapTable4')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 5')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (103, N'Other Cap Table 5', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable5')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 6')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (104, N'Other Cap Table 6', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable6')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 7')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (105, N'Other Cap Table 7', 14, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'OtherCapTable7')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 8')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (106, N'Other Cap Table 8', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'OtherCapTable8')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 9')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (107, N'Other Cap Table 9', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable9')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Other Cap Table 10')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (108, N'Other Cap Table 10', 14, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'OtherCapTable10')
END
GO

-- Other Cap Table 1
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 1'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 2
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 2'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 3
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 3'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 4
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 4'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 5
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 5'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO


-- Other Cap Table 6
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 6'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO


-- Other Cap Table 7
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 7'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 8
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 8'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

-- Other Cap Table 9
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 9'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO


-- Other Cap Table 10
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 2, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 3, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 4, 0, getdate(), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = (SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10') AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureActionMappingID])
	VALUES ((SELECT SubFeatureID FROM M_SubFeature WHERE SubFeature = 'Other Cap Table 10'), 5, 0,getdate(), 3, NULL, NULL, NULL)
END
GO

IF OBJECT_ID('[dbo].[SpGetSubFeatureListByPageconfig]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
END
GO
CREATE PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig] ( @FeatureId INT ) AS
BEGIN   IF (@FeatureId = 14)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
f.isDeleted AS IsDeleted, f.ParentFeatureID AS ParentFeatureId FROM M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4,(Select SubPageID from M_SubPageDetails WHERE Name='Documents' AND PageID=1)) AND s.isDeleted = 0
LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38, 41,47) AND sf.isDeleted = 0
WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and ParentFeatureID = 14
END
 
ELSE IF(@FeatureId = 13)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName,
   COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted, ParentFeatureId
   FROM       M_SubFeature f     LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12,(Select SubPageID from M_SubPageDetails WHERE Name='Documents' AND PageID=2)) AND s.isDeleted = 0
   WHERE s.SubPageID IS NOT NULL and ParentFeatureID = 13
   END
ELSE IF(@FeatureId = 15)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL
END
ELSE IF(@FeatureId = 50)
BEGIN
SELECT  f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId     FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0
LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0
WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
END
ELSE IF(@FeatureId = 42)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM   M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42
 
UNION ALL
 
SELECT    f.SubFeatureID,      f.SubFeature As SubFeatureName,COALESCE(f.SubFeature, f.SubFeature) AS SubFeatureAliasName,f.isDeleted AS IsDeleted,f.ParentFeatureID AS ParentFeatureId     FROM M_SubFeature f
WHERE ParentFeatureID = 42 AND F.isDeleted=0 AND PageConfigName IS NULL
END
ELSE IF(@FeatureId = 19)
BEGIN
SELECT       SubFeatureID,      SubFeature As SubFeatureName, COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,isDeleted AS IsDeleted,
ParentFeatureID AS ParentFeatureId  FROM  M_SubFeature
WHERE  SubFeatureID  BETWEEN 33 AND 48
END
ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 82 AND 92
		END
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MasterAllKPIsByModuleIds]'))
BEGIN
	 DROP PROCEDURE sp_MasterAllKPIsByModuleIds
END
GO
CREATE PROCEDURE [dbo].[sp_MasterAllKPIsByModuleIds](
    @ModuleIds  varchar(max)=NULL)
AS
BEGIN

    DECLARE @ModuleIdsCTE TABLE (ModuleId INT);
    IF @ModuleIds IS NOT NULL
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT Item FROM dbo.SplitString(@ModuleIds, ',');
    END
    ELSE
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT ModuleID FROM M_KpiModules WHERE IsDeleted=0 AND Name NOT IN('StaticInformation','MonthlyReport');
		INSERT INTO @ModuleIdsCTE(ModuleId)
		SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN(SELECT PageID FROM M_PageDetails WHERE Name='ESG')
    END

    -- Investment KPI
    SELECT 
        Mst.KPI,
        Mst.InvestmentKPIId KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        4 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'InvestmentKPIs') AliasName
    FROM M_InvestmentKPI Mst
     LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 4)

    UNION ALL

    -- Master KPI
    SELECT 
        Mst.KPI,
        Mst.MasterKpiID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
		NULL ParentKpi,
        Mst.ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
	SPF.AliasName
    FROM M_MasterKpis Mst
	INNER JOIN M_KpiModules kpi on kpi.ModuleID=Mst.ModuleID
	LEFT JOIN M_SubPageFields SPF ON SPF.name = kpi.PageConfigFieldName
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID	
    WHERE Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text') AND Mst.ModuleID IN(SELECT ModuleID FROM @ModuleIdsCTE)
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT Distinct ModuleID FROM M_KpiModules WHERE IsDeleted=0))

    UNION ALL

    -- Impact KPI
    SELECT 
        Mst.KPI,
        Mst.ImpactKPIID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        6 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'ImpactKPIs') AliasName
    FROM M_ImpactKPI Mst
     LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 6)

    UNION ALL

    -- Operational KPI
    SELECT 
        Mst.KPI,
        Mst.SectorwiseOperationalKPIID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        3 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'OperationalKPIs') AliasName
    FROM M_SectorwiseOperationalKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 3)

    UNION ALL

    -- Company KPI
    SELECT 
        Mst.KPI,
        Mst.CompanyKPIID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        5 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'CompanyKPIs') AliasName
    FROM M_CompanyKPI Mst
     LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 5)

    UNION ALL

    -- Profit and Loss
    SELECT 
        Mst.ProfitAndLossLineItem KPI,
        Mst.ProfitAndLossLineItemID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        7 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'ProfitLoss') AliasName
    FROM M_ProfitAndLoss_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 7)

    UNION ALL

    -- Balance Sheet
    SELECT 
        Mst.BalanceSheetLineItem KPI,
        Mst.BalanceSheetLineItemID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        8 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'BalanceSheet') AliasName
    FROM M_BalanceSheet_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 8)

    UNION ALL

    -- Cash Flow
    SELECT 
        Mst.CashFlowLineItem KPI,
        Mst.CashFlowLineItemID KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
        NULL ParentKpi,
        9 ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		(SELECT Top 1 AliasName FROM M_SubPageFields where name = 'CashFlow') AliasName
    FROM M_CashFlow_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mlg.MethodologyId = Mst.MethodologyID
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text')
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 9)

		  UNION ALL
		-- cap table
		SELECT 
        Mst.KPI,
        Mst.KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
		NULL ParentKpi,
        Mst.ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		Msf.AliasName
    FROM MCapTable Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
	INNER JOIN M_KpiModules Mk on Mk.ModuleID =Mst.ModuleId
	INNER JOIN M_SubPageFields Msf on Msf.Name=Mk.PageConfigFieldName
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text') AND Mst.ModuleID IN(SELECT ModuleID FROM @ModuleIdsCTE)
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT ModuleID FROM M_KpiModules WHERE Name IN ('CapTable1', 'CapTable2', 'CapTable3', 'CapTable4', 'CapTable5') AND IsDeleted=0))

		  UNION ALL
		  -- ESG
		  SELECT 
        Mst.KPI,
        Mst.ESGKpiId as KpiId,
        Mst.KpiInfo,
        Mst.IsBoldKPI IsBoldKpi,
        Mst.IsHeader,
        NULL ParentId,
        Mst.MethodologyId,
		NULL ParentKpi,
        Mst.SubPageId ModuleId,
		 CASE 
        WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
        ELSE NULL
    END AS MethodologyName,
		Msb.AliasName
    FROM M_ESGKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
	INNER JOIN M_SubPageDetails Msb on Msb.SubPageID=Mst.SubPageId
    WHERE  Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0)
    OR Mst.KpiInfo = 'Text') AND Mst.SubPageId IN(SELECT ModuleId FROM @ModuleIdsCTE)
          AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN(
SELECT PageID FROM M_PageDetails WHERE Name='ESG')))
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MappingAllKPIsByModuleIds]'))
BEGIN
	 DROP PROCEDURE sp_MappingAllKPIsByModuleIds
END
GO
CREATE PROCEDURE [dbo].[sp_MappingAllKPIsByModuleIds](
    @ModuleIds  varchar(max)=NULL,
	@CompanyIds varchar(max)=NULL )
AS
BEGIN
	DECLARE @CompanyDetails TABLE (PortfolioCompanyId INT,CompanyName varchar(max))	
	IF(@CompanyIds IS NULL OR @CompanyIds='')
	BEGIN
		INSERT INTO @CompanyDetails(CompanyName,PortfolioCompanyId)
		SELECT pcd.CompanyName, pcd.PortfolioCompanyID 
		FROM PortfolioCompanyDetails pcd
		WHERE    pcd.IsDeleted = 0;
	END
	ELSE 
	 BEGIN	
		INSERT INTO @CompanyDetails(CompanyName,PortfolioCompanyId)
		SELECT pcd.CompanyName, pcd.PortfolioCompanyID 
			FROM PortfolioCompanyDetails pcd INNER JOIN  dbo.SplitString(@CompanyIds, ',') ss 
			ON pcd.PortfolioCompanyID = ss.Item WHERE    pcd.IsDeleted = 0;
	 END

    DECLARE @ModuleIdsCTE TABLE (ModuleId INT);
    IF @ModuleIds IS NOT NULL
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT Item FROM dbo.SplitString(@ModuleIds, ',');
    END
    ELSE
    BEGIN
        INSERT INTO @ModuleIdsCTE(ModuleId)
        SELECT ModuleID FROM M_KpiModules WHERE IsDeleted=0 AND Name NOT IN('StaticInformation','MonthlyReport');
		INSERT INTO @ModuleIdsCTE(ModuleId)
		SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN(SELECT PageID FROM M_PageDetails WHERE Name='ESG')
    END
	
	-- Investment KPI
SELECT 
    Mst.KPI,
    Mst.InvestmentKPIId AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mst.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    4 AS ModuleId,
    CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_InvestmentKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_PortfolioInvestmentKPI Map ON Map.KpiID = Mst.InvestmentKPIId
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_InvestmentKPI ParentKpi ON ParentKpi.InvestmentKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'InvestmentKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 4)

UNION ALL

-- Master KPI
SELECT 
    Mst.KPI,
    Mst.MasterKpiID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    Mst.ModuleId,
    CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    SPF.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_MasterKpis Mst
	INNER JOIN M_KpiModules Kpi on Kpi.ModuleID=Mst.ModuleID
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_Kpis Map ON Map.KpiID = Mst.MasterKpiID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_MasterKpis ParentKpi ON ParentKpi.MasterKpiID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0 AND ParentKpi.ModuleID = Mst.ModuleID
    LEFT JOIN M_SubPageFields SPF ON SPF.name = kpi.PageConfigFieldName
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0 
    AND Mst.ModuleID IN (SELECT ModuleID FROM @ModuleIdsCTE)
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT Distinct ModuleID FROM M_KpiModules WHERE IsDeleted=0))

UNION ALL

-- Impact KPI
SELECT 
    Mst.KPI,
    Mst.ImpactKPIID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    6 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.KPIOrder AS DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_ImpactKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_ImpactKPI_Order Map ON Map.ImpactKPIID = Mst.ImpactKPIID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_ImpactKPI ParentKpi ON ParentKpi.ImpactKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'ImpactKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 6)

UNION ALL

-- Operational KPI
SELECT 
    Mst.KPI,
    Mst.SectorwiseOperationalKPIID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    3 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_SectorwiseOperationalKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_PortfolioOperationalKPI Map ON Map.KpiID = Mst.SectorwiseOperationalKPIID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_SectorwiseOperationalKPI ParentKpi ON ParentKpi.SectorwiseOperationalKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'OperationalKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 3)

UNION ALL

-- Company KPI
SELECT 
    Mst.KPI,
    Mst.CompanyKPIID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKPIID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    5 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_CompanyKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_PortfolioCompanyKPI Map ON Map.KpiID = Mst.CompanyKPIID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_CompanyKPI ParentKpi ON ParentKpi.CompanyKPIID = Map.ParentKPIID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'CompanyKPIs') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 5)

UNION ALL

-- Profit and Loss
SELECT 
    Mst.ProfitAndLossLineItem AS KPI,
    Mst.ProfitAndLossLineItemID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentLineItemID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.ProfitAndLossLineItem AS ParentKpi,
    7 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_ProfitAndLoss_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.ProfitAndLossLineItemID = Mst.ProfitAndLossLineItemID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_ProfitAndLoss_LineItems ParentKpi ON ParentKpi.ProfitAndLossLineItemID = Map.ParentLineItemID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'ProfitLoss') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 7)

UNION ALL

-- Balance Sheet
SELECT 
    Mst.BalanceSheetLineItem AS KPI,
    Mst.BalanceSheetLineItemID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentLineItemID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.BalanceSheetLineItem AS ParentKpi,
    8 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_BalanceSheet_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.BalanceSheetLineItemID = Mst.BalanceSheetLineItemID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_BalanceSheet_LineItems ParentKpi ON ParentKpi.BalanceSheetLineItemID = Map.ParentLineItemID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'BalanceSheet') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 8)

UNION ALL

-- Cash Flow
SELECT 
    Mst.CashFlowLineItem AS KPI,
    Mst.CashFlowLineItemID AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentLineItemID AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.CashFlowLineItem AS ParentKpi,
    9 AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    AliasName.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_CashFlow_LineItems Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CashFlowLineItemID = Mst.CashFlowLineItemID
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_CashFlow_LineItems ParentKpi ON ParentKpi.CashFlowLineItemID = Map.ParentLineItemID AND ParentKpi.IsDeleted = 0
    LEFT JOIN (SELECT TOP 1 AliasName FROM M_SubPageFields WHERE name = 'CashFlow') AliasName ON 1=1
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId = 9)

UNION ALL

-- Cap Table
SELECT 
    Mst.KPI,
    Mst.KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentKpiId AS ParentId,
    Mlg.MethodologyId,
    ParentKpi.Kpi AS ParentKpi,
    Mst.ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    Msf.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    MCapTable Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN MappingCapTable Map ON Map.KpiId = Mst.KpiId
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    INNER JOIN M_KpiModules Mk ON Mk.ModuleID = Mst.ModuleId
    INNER JOIN M_SubPageFields Msf ON Msf.Name = Mk.PageConfigFieldName
    LEFT JOIN MCapTable ParentKpi ON ParentKpi.KpiId = Map.ParentKpiId AND ParentKpi.IsDeleted = 0 AND ParentKpi.ModuleId = Map.ModuleId
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0 
    AND Mst.ModuleId IN (SELECT ModuleID FROM @ModuleIdsCTE)
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT ModuleID FROM M_KpiModules WHERE Name IN ('CapTable1', 'CapTable2', 'CapTable3', 'CapTable4', 'CapTable5') AND IsDeleted = 0))

UNION ALL

-- ESG
SELECT 
    Mst.KPI,
    Mst.ESGKpiId AS KpiId,
    Mst.KpiInfo,
    Mst.IsBoldKPI AS IsBoldKpi,
    Mst.IsHeader,
    Map.ParentId,
    Mlg.MethodologyId,
    ParentKpi.KPI AS ParentKpi,
    Mst.SubPageId AS ModuleId,
     CASE 
            WHEN Mlg.MethodologyName IS NOT NULL THEN Mlg.MethodologyName
            ELSE NULL
        END AS MethodologyName,
    Msb.AliasName,
    Map.DisplayOrder,
    CD.PortfolioCompanyId,
    CD.CompanyName
FROM 
    M_ESGKPI Mst
    LEFT JOIN M_Methodology Mlg ON Mst.MethodologyId = Mlg.MethodologyID
    INNER JOIN M_SubPageDetails Msb ON Msb.SubPageID = Mst.SubPageId
    INNER JOIN Mapping_ESGKpi Map ON Map.EsgKpiID = Mst.ESGKpiId
    INNER JOIN @CompanyDetails CD ON CD.PortfolioCompanyId = Map.PortfolioCompanyID
    LEFT JOIN M_ESGKPI ParentKpi ON ParentKpi.ESGKpiID = Map.ParentId AND ParentKpi.IsDeleted = 0 AND ParentKpi.SubPageId = Map.SubPageId
WHERE 
    Mst.IsDeleted = 0 
    AND ((Mlg.IsDeleted = 0 OR Mst.MethodologyId = 0) OR Mst.KpiInfo = 'Text')
    AND Map.IsDeleted = 0 
    AND Mst.SubPageId IN (SELECT ModuleId FROM @ModuleIdsCTE)
    AND EXISTS (SELECT 1 FROM @ModuleIdsCTE WHERE ModuleId IN (SELECT SubPageID FROM M_SubPageDetails WHERE PageID IN (SELECT PageID FROM M_PageDetails WHERE Name = 'ESG')));

END
GO

IF EXISTS(select *from MSubSectionFields where SubPageID in (46,49) AND Options='Monthly,Quarterly,Annual')
	BEGIN
		UPDATE  MSubSectionFields SET Options='Monthly,Quarterly,Annual,Half-Annual',ChartValue='Monthly,Quarterly,Annual,Half-Annual' where SubPageID in (46,49)
	END
GO
-- Update SubPage names if they exist
IF EXISTS (SELECT 1 FROM M_SubPageDetails WHERE SubPageID=49 and Name!='Fund Key Performance Indicator')
BEGIN
    UPDATE M_SubPageDetails SET Name='Fund Key Performance Indicator' WHERE SubPageID=49
END

IF EXISTS (SELECT 1 FROM M_SubPageDetails WHERE SubPageID=46 and Name!='Fund Financials')
BEGIN
    UPDATE M_SubPageDetails SET Name='Fund Financials' WHERE SubPageID=46
END

-- Update SubPage fields if they exist
IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials')
BEGIN
    UPDATE M_SubPageFields SET Name='FundFinancials8' WHERE SubPageID=46 AND Name='FundFinancials'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials1')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI1' WHERE SubPageID=49 AND Name='FundKeyFinancials1'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials2')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI2' WHERE SubPageID=49 AND Name='FundKeyFinancials2'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials3')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI3' WHERE SubPageID=49 AND Name='FundKeyFinancials3'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials4')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI4' WHERE SubPageID=49 AND Name='FundKeyFinancials4'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials5')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI5' WHERE SubPageID=49 AND Name='FundKeyFinancials5'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials6')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI6' WHERE SubPageID=49 AND Name='FundKeyFinancials6'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials7')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI7' WHERE SubPageID=49 AND Name='FundKeyFinancials7'
END

IF EXISTS (SELECT 1 FROM M_SubPageFields WHERE SubPageID=49 AND Name='FundKeyFinancials8')
BEGIN
    UPDATE M_SubPageFields SET Name='FundKPI8' WHERE SubPageID=49 AND Name='FundKeyFinancials8'
END

-- Update MFundKpiModules if they exist
IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundFinancials')
BEGIN
    UPDATE MFundKpiModules SET Name='FundFinancials8', PageConfigFieldName='FundFinancials8' WHERE Name='FundFinancials'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials1')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI1', PageConfigFieldName='FundKPI1' WHERE Name='FundKeyFinancials1'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials2')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI2', PageConfigFieldName='FundKPI2' WHERE Name='FundKeyFinancials2'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials3')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI3', PageConfigFieldName='FundKPI3' WHERE Name='FundKeyFinancials3'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials4')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI4', PageConfigFieldName='FundKPI4' WHERE Name='FundKeyFinancials4'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials5')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI5', PageConfigFieldName='FundKPI5' WHERE Name='FundKeyFinancials5'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials6')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI6', PageConfigFieldName='FundKPI6' WHERE Name='FundKeyFinancials6'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials7')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI7', PageConfigFieldName='FundKPI7' WHERE Name='FundKeyFinancials7'
END

IF EXISTS (SELECT 1 FROM MFundKpiModules WHERE Name='FundKeyFinancials8')
BEGIN
    UPDATE MFundKpiModules SET Name='FundKPI8', PageConfigFieldName='FundKPI8' WHERE Name='FundKeyFinancials8'
END