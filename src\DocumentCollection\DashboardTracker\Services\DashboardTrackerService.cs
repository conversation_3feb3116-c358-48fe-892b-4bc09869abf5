using Contract.PortfolioCompany;
using Contract.Repository;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.DashboardTracker;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using PortfolioCompany.Helper;
using Workflow;

namespace DocumentCollection.DashboardTracker.Services
{
    public class DashboardTrackerService : IDashboardTrackerService
    {
        private readonly IWorkflowPCService _workflowPCService;
        private readonly IFileService _fileService;
        private readonly IUnitOfWork _unitOfWork;

        public DashboardTrackerService(IWorkflowPCService workflowPCService, IFileService fileService, IUnitOfWork unitOfWork)
        {
            _workflowPCService = workflowPCService;
            _fileService = fileService;
            _unitOfWork = unitOfWork;
        }

        public async Task<List<PortfolioCompanyViewModel>> GetPortfolioCompanies(PortfolioCompanyFilter portfolioCompanyFilter) 
        {
            var workflowResult = await _workflowPCService.GetPortfolioCompanies(portfolioCompanyFilter);

            if (workflowResult == null || workflowResult.PortfolioCompanyQueryListModel == null || workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList.Count == 0)
            {
                return new List<PortfolioCompanyViewModel>(); ;
            }

            // Map to PortfolioCompanyViewModel
            var viewModelList = workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList.Select(pc => new PortfolioCompanyViewModel
            {
                CompanyId = pc.PortfolioCompanyID,
                PortfolioCompanyName = pc.CompanyName,
                FundName = pc.FundName,
            }).ToList();


            // Only fetch image paths for the first 50 companies (simplified loop)
            var companiesToFetchLogo = viewModelList.Take(50).ToList();
            foreach (var viewModel in companiesToFetchLogo)
            {
                var res = await _unitOfWork.PortfolioCompanyDetailRepository.FindFirstAsync(x => x.PortfolioCompanyId == viewModel.CompanyId);
                if (res != null)
                {
                    var imgPath = await PortfolioCompanyHelper.GetCompanyLogo(_fileService, viewModel.CompanyId, res.ImagePath);
                    viewModel.CompanyLogo = imgPath;
                }
            }

            return viewModelList;
        }

        public async Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto)
        {
            var entity = new DashboardTrackerConfig
            {
                FieldType = dto.FieldType,
                DataType = dto.DataType,
                Name = dto.Name,
                FrequencyType = dto.FrequencyType,
                StartPeriod = dto.StartPeriod,
                EndPeriod = dto.EndPeriod,
                IsPrefix = dto.IsPrefix,
                TimeSeriesDateFormat = dto.TimeSeriesDateFormat,
                MapTo = dto.MapTo,
                IsActive = dto.IsActive,
                IsDeleted = dto.IsDeleted,
            };

            if (dto.ID.HasValue && dto.ID.Value > 0)
            {
                // Update existing record
                entity.ModifiedOn = dto.ModifiedOn;
                entity.ModifiedBy = dto.ModifiedBy;
                entity.ID = dto.ID.Value;
                _unitOfWork.DashboardTrackerConfigRepository.Update(entity);
            }
            else
            {
                // Insert new record
                entity.CreatedBy = dto.CreatedBy ?? 0;
                entity.CreatedOn = dto.CreatedOn ?? DateTime.UtcNow;
                _unitOfWork.DashboardTrackerConfigRepository.Insert(entity);
            }

            await _unitOfWork.SaveAsync();
            return entity.ID;
        }
    }
}
