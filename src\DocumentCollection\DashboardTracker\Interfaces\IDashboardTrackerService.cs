﻿using Contract.PortfolioCompany;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Models;

namespace DocumentCollection.DashboardTracker.Interfaces
{
    public interface IDashboardTrackerService
    {
        Task<List<PortfolioCompanyViewModel>> GetPortfolioCompanies(PortfolioCompanyFilter portfolioCompanyFilter);
        Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto);
    }
}
