using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.DashboardTracker
{
    [Table("DashboardTrackerConfig")]
    public class DashboardTrackerConfig : BaseCommonModel
    {
        [Key]
        public int ID { get; set; }
        
        [Required]
        public int FieldType { get; set; }
        
        [Required]
        public int DataType { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; }
        
        public int? FrequencyType { get; set; }
        
        [MaxLength(50)]
        public string StartPeriod { get; set; }
        
        [MaxLength(50)]
        public string EndPeriod { get; set; }

        public bool? IsPrefix { get; set; }
        public string? TimeSeriesDateFormat { get; set; }

        public int? MapTo { get; set; }
        
        [Required]
        public bool IsActive { get; set; } = true;        

    }
}
