﻿using DataAccessLayer.Fluent.Account;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DataCollection;
using DataAccessLayer.Models.Documents;
using DataAccessLayer.Models.ESG;
using DataAccessLayer.Models.MonthlyReport;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.Models.Valuation;
using DataAccessLayer.Models.Workflow;
using DataAccessLayer.Models.LpReport;
using Microsoft.EntityFrameworkCore;
using DataAccessLayer.Models.GrowthReport;
using DataAccessLayer.Models.SDG;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.CLO.CLOPageConfig;
using DataAccessLayer.Models.CLO.InvestmentCompany;
using DataAccessLayer.Models.CLO.Clo_Details;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.Models.Fund;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.Models.DashboardTracker;


namespace DataAccessLayer.DBModel {
    public partial class DBEntities : DbContext {

        public DBEntities(DbContextOptions<DBEntities> options) : base(options) {
        }

        public virtual DbSet<UserReport> UserReports { get; set; }
        public virtual DbSet<UserReportFilter> UserReportFilters { get; set; }
        public virtual DbSet<CashFlowCompanyWiseData> CashFlowCompanyWiseData { get; set; }
        public virtual DbSet<CashFlowDateWiseSummary> CashFlowDateWiseSummary { get; set; }
        public virtual DbSet<CashFlowFileLogs> CashFlowFileLogs { get; set; }
        public virtual DbSet<KPIValuesFileLogs> KPIValuesFileLogs { get; set; }
        public virtual DbSet<CashflowCalculationDetails> CashflowCalculationDetails { get; set; }
        public virtual DbSet<CashflowTotalCalculationDetails> CashflowTotalCalculationDetails { get; set; }
        public virtual DbSet<CompanyFundingDetails> CompanyFundingDetails { get; set; }
        public virtual DbSet<CompanySummaryDetails> CompanySummaryDetails { get; set; }
        public virtual DbSet<CurrencyRates> CurrencyRates { get; set; }
        public virtual DbSet<DealDetails> DealDetails { get; set; }
        public virtual DbSet<DynamicQueries> DynamicQueries { get; set; }
        public virtual DbSet<DynamicQueriesCopy> DynamicQueriesCopy { get; set; }
        public virtual DbSet<ElmahError> ElmahError { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplate { get; set; }
        public virtual DbSet<EmployeeDetails> EmployeeDetails { get; set; }
        public virtual DbSet<FirmDetails> FirmDetails { get; set; }
        public virtual DbSet<FundDetails> FundDetails { get; set; }
        public virtual DbSet<FundTrackRecord> FundTrackRecord { get; set; }
        public virtual DbSet<FundIngestion> FundIngestion { get; set; }
        public virtual DbSet<GlobalVariables> GlobalVariables { get; set; }
        public virtual DbSet<M_AccountType> MAccountType { get; set; }
        public virtual DbSet<M_Action> MAction { get; set; }
        public virtual DbSet<M_City> MCity { get; set; }
        public virtual DbSet<M_CompanyKpi> MCompanyKpi { get; set; }
        public virtual DbSet<MImpactKpi> MImpactKpi { get; set; }
        public virtual DbSet<M_InvestmentKpi> MInvestmentKpi { get; set; }
        public virtual DbSet<M_Country> MCountry { get; set; }
        public virtual DbSet<M_Currency> MCurrency { get; set; }
        public virtual DbSet<M_DealBoardSeat> MDealBoardSeat { get; set; }
        public virtual DbSet<M_DealExitMethod> MDealExitMethod { get; set; }
        public virtual DbSet<M_DealInvestmentStage> MDealInvestmentStage { get; set; }
        public virtual DbSet<M_DealSecurtyType> MDealSecurtyType { get; set; }
        public virtual DbSet<M_DealSourcing> MDealSourcing { get; set; }
        public virtual DbSet<M_DealTransactionRole> MDealTransactionRole { get; set; }
        public virtual DbSet<M_DealValuationMethodology> MDealValuationMethodology { get; set; }
        public virtual DbSet<M_Designation> MDesignation { get; set; }
        public virtual DbSet<M_Features> MFeatures { get; set; }
        public virtual DbSet<MFinancialKpi> MFinancialKpi { get; set; }
        public virtual DbSet<M_FinancialStatus> MFinancialStatus { get; set; }
        public virtual DbSet<M_FirmType> MFirmType { get; set; }
        public virtual DbSet<M_FundHoldingStatus> MFundHoldingStatus { get; set; }
        public virtual DbSet<M_FundingType> MFundingType { get; set; }
        public virtual DbSet<M_Geography> MGeography { get; set; }
        public virtual DbSet<M_Groups> MGroups { get; set; }
        public virtual DbSet<M_Headquarter> MHeadquarter { get; set; }
        public virtual DbSet<M_InvestmentStage> MInvestmentStage { get; set; }
        public virtual DbSet<M_OperationalKPI> MOperationalKpi { get; set; }
        public virtual DbSet<M_OwnershipStatus> MOwnershipStatus { get; set; }
        public virtual DbSet<M_PipelineStatus> MPipelineStatus { get; set; }
        public virtual DbSet<M_Region> MRegion { get; set; }
        public virtual DbSet<M_Sector> MSector { get; set; }
        public virtual DbSet<M_SectorwiseKPI> MSectorwiseKpi { get; set; }
        public virtual DbSet<M_State> MState { get; set; }
        public virtual DbSet<M_StockExchange> MStockExchange { get; set; }
        public virtual DbSet<M_Strategy> MStrategy { get; set; }
        public virtual DbSet<M_SubSector> MSubSector { get; set; }
        public virtual DbSet<Mapping_CountryCurrency> MappingCountryCurrency { get; set; }
        public virtual DbSet<Mapping_FeatureAction> MappingFeatureAction { get; set; }
        public virtual DbSet<Mapping_SubFeatureAction> MappingSubFeatureAction { get; set; }
        public virtual DbSet<Mapping_FirmEmployee> MappingFirmEmployee { get; set; }
        public virtual DbSet<Mapping_FirmFund> MappingFirmFund { get; set; }
        public virtual DbSet<Mapping_FirmGeographicLocation> MappingFirmGeographicLocation { get; set; }
        public virtual DbSet<Mapping_FirmHeadquarterLocation> MappingFirmHeadquarterLocation { get; set; }
        public virtual DbSet<Mapping_GroupFeature> MappingGroupFeature { get; set; }
        public virtual DbSet<Mapping_Locations> MappingLocations { get; set; }
        public virtual DbSet<Mapping_PCEmployee> MappingPcemployee { get; set; }
        public virtual DbSet<Mapping_PCGeographicLocation> MappingPcgeographicLocation { get; set; }
        public virtual DbSet<Mapping_UserGroup> MappingUserGroup { get; set; }
        public virtual DbSet<PcCompanywiseKpiMonthlyValue> PccompanywiseKpimonthlyValue { get; set; }
        public virtual DbSet<PCFinancialKPIMonthlyValue> PcfinancialKpimonthlyValue { get; set; }
        public virtual DbSet<PCOperationalKPIMonthlyValue> PcoperationalKpimonthlyValue { get; set; }
        public virtual DbSet<PCSectorwiseKPIMonthlyValue> PcsectorwiseKpimonthlyValue { get; set; }
        public virtual DbSet<PipelineDetails> PipelineDetails { get; set; }
        public virtual DbSet<PortfolioCompanyDetails> PortfolioCompanyDetails { get; set; }
        public virtual DbSet<PortfolioCompanyEmployeeDetails> PortfolioCompanyEmployeeDetails { get; set; }
        public virtual DbSet<PortfolioCompanyFundHoldingDetails> PortfolioCompanyFundHoldingDetails { get; set; }
        public virtual DbSet<PortfolioCompanyProfitabilityDetails> PortfolioCompanyProfitabilityDetails { get; set; }
        public virtual DbSet<PortfolioCompanySectorwiseKPIMonthly> PortfolioCompanySectorwiseKpimonthly { get; set; }
        public virtual DbSet<PortfolioCompanySectorwiseKPIValues> PortfolioCompanySectorwiseKpivalues { get; set; }
        public virtual DbSet<UserDetails> UserDetails { get; set; }
        public virtual DbSet<View_LocationMapping> ViewLocationMapping { get; set; }
        public virtual DbSet<View_RegionCountryMapping> ViewRegionCountryMapping { get; set; }
        public virtual DbSet<MappingPortfolioCompanyKpi> MappingPortfolioCompanyKpi { get; set; }
        public virtual DbSet<MappingPortfolioInvestmentKpi> MappingPortfolioInvestmentKpi { get; set; }
        public virtual DbSet<M_Kpitypes> MKpitypes { get; set; }
        public virtual DbSet<PortfolioCompanyOperationalKPIQuarter> PortfolioCompanyOperationalKPIQuarter { get; set; }
        public virtual DbSet<PortfolioCompanyOperationalKPIValue> PortfolioCompanyOperationalKPIValue { get; set; }
        public virtual DbSet<BalanceSheetValues> BalanceSheetValues { get; set; }
        public virtual DbSet<M_BalanceSheet_LineItems> M_BalanceSheet_LineItems { get; set; }
        public virtual DbSet<Mapping_CompanyBalanceSheetLineItems> Mapping_CompanyBalanceSheetLineItems { get; set; }
        public virtual DbSet<ProfitAndLossIccaseValues> ProfitAndLossIccaseValues { get; set; }
        public virtual DbSet<BalanceSheetICCaseValues> BalanceSheetICCaseValues { get; set; }
        public virtual DbSet<ProfitAndLossAnnualHistoricalData> ProfitAndLossAnnualHistoricalData { get; set; }
        public virtual DbSet<BalanceSheet_AnnualHistoricalData> BalanceSheet_AnnualHistoricalData { get; set; }
        public virtual DbSet<ProfitAndLossForecastData> ProfitAndLossForecastData { get; set; }
        public virtual DbSet<BalanceSheetForecastData> BalanceSheetForecastData { get; set; }
        public virtual DbSet<M_CashFlow_LineItems> M_CashFlow_LineItems { get; set; }
        public virtual DbSet<Mapping_CompanyCashFlowLineItems> Mapping_CompanyCashFlowLineItems { get; set; }
        public virtual DbSet<CashFlowValues> CashFlowValues { get; set; }
        public virtual DbSet<CashFlow_AnnualHistoricalData> CashFlow_AnnualHistoricalData { get; set; }
        public virtual DbSet<CashFlowICCaseValues> CashFlowICCaseValues { get; set; }
        public virtual DbSet<CashFlow_ForecastData> CashFlow_ForecastData { get; set; }
        public virtual DbSet<PCInvestmentKpiQuarterlyValue> PCInvestmentKpiQuarterlyValue { get; set; }
        public virtual DbSet<Mapping_ImpactKPI_Order> Mapping_ImpactKPI_Order { get; set; }
        public virtual DbSet<M_SubFeature> MSubFeature { get; set; }
        public virtual DbSet<Mapping_UserSubFeature> Mapping_UserSubFeature { get; set; }
        public virtual DbSet<ImpactKPI_AnnualHistoricalData> ImpactKPI_AnnualHistoricalData { get; set; }
        public virtual DbSet<DocumentType> DocumentTypes { get; set; }
        public virtual DbSet<DocumentStatus> DocumentStatus { get; set; }
        public virtual DbSet<DataAuditLog> DataAuditLog { get; set; }
        public virtual DbSet<M_LPReportConfiguration> M_LPReportConfiguration { get; set; }
        public virtual DbSet<Mapping_LPReportConfiguration> Mapping_LPReportConfiguration { get; set; }
        public virtual DbSet<M_ValueTypes> M_ValueTypes { get; set; }
        public virtual DbSet<M_KpiModules> M_KpiModules { get; set; }
        public virtual DbSet<M_MasterKpis> M_MasterKpis { get; set; }
        public virtual DbSet<Mapping_Kpis> Mapping_Kpis { get; set; }
        public virtual DbSet<MasterKpiAuditLog> MasterKpiAuditLog { get; set; }
        public virtual DbSet<ImpactKpiAuditLog> ImpactKpiAuditLog { get; set; }
        public virtual DbSet<PCMasterKpiValues> PCMasterKpiValues { get; set; }
        public virtual DbSet<M_Methodology> M_Methodology { get; set; }
        public virtual DbSet<M_Notification> M_Notification { get; set; }
        public virtual DbSet<M_NotificationModules> M_NotificationModules { get; set; }
        public virtual DbSet<FinancialValueTypes> FinancialValueTypes { get; set; }
        public virtual DbSet<MappingPortfolioOperationalKpi> MappingPortfolioOperationalKpi { get; set; }
        public virtual DbSet<PageConfiguration> PageConfiguration { get; set; }
        public virtual DbSet<ReportTemplateConfiguration> ReportTemplateConfiguration { get; set; }
        public virtual DbSet<MWorkflowStatus> MWorkflowStatus { get; set; }
        public virtual DbSet<MappingWorkflowStatus> MappingWorkflowStatus { get; set; }
        public virtual DbSet<WorkflowRequest> WorkflowRequest { get; set; }
        public virtual DbSet<MappingWorkflowRequest> MappingWorkflowRequest { get; set; }
        public virtual DbSet<MappingWorkflowRequestComments> MappingWorkflowRequestComments { get; set; }
        public virtual DbSet<PortfolioCompanyDetailsDraft> PortfolioCompanyDetailsDraft { get; set; }
        public virtual DbSet<ReportTemplateConfigurationMapping> ReportTemplateConfigurationMapping { get; set; }
        public virtual DbSet<MasterGroups> MasterGroups { get; set; }
        public virtual DbSet<PeriodTypes> PeriodTypes { get; set; }
        public virtual DbSet<MappingPCGeographicLocationDraft> MappingPCGeographicLocationDraft { get; set; }
        public virtual DbSet<MappingPCEmployeeDraft> MappingPCEmployeeDraft { get; set; }
        public virtual DbSet<WorkflowHistory> WorkflowHistory { get; set; }
        public virtual DbSet<EmployeeDetailsDraft> EmployeeDetailsDraft { get; set; }
        public virtual DbSet<MappingWorkflowStatusGroup> MappingWorkflowStatusGroup { get; set; }
        public virtual DbSet<PCCompanyKpiMonthlyValueDraft> PCCompanyKpiMonthlyValueDraft { get; set; }
        public virtual DbSet<PCInvestmentKpiQuarterlyValueDraft> PCInvestmentKpiQuarterlyValueDraft { get; set; }
        public virtual DbSet<DraftAuditLog> DraftAuditLog { get; set; }
        public virtual DbSet<ReportTypes> ReportTypes { get; set; }
        public virtual DbSet<PortfolioCompanyOperationalKPIQuartersDraft> PortfolioCompanyOperationalKPIQuartersDraft { get; set; }
        public virtual DbSet<PortfolioCompanyOperationalKPIValuesDraft> PortfolioCompanyOperationalKPIValuesDraft { get; set; }

        public virtual DbSet<M_FundReportConfiguration> M_FundReportConfiguration { get; set; }
        public virtual DbSet<FundReportTemplateConfiguration> FundReportTemplateConfiguration { get; set; }
        public virtual DbSet<FundReportTemplateConfigurationMapping> FundReportTemplateConfigurationMapping { get; set; }
        public virtual DbSet<WorkflowMappingHistory> WorkflowMappingHistory { get; set; }
        public virtual DbSet<M_PageDetails> M_PageDetails { get; set; }
        public virtual DbSet<M_SubPageDetails> M_SubPageDetails { get; set; }
        public virtual DbSet<M_SubPageFields> M_SubPageFields { get; set; }
        public virtual DbSet<PageConfigurationFieldValue> PageConfigurationFieldValue { get; set; }
        public virtual DbSet<PageConfigurationTrackRecordFieldValue> PageConfigurationTrackRecordFieldValue { get; set; }
        public virtual DbSet<M_TrackRecordDataTypes> M_TrackRecordDataTypes { get; set; }

        public virtual DbSet<MInvestorType> MInvestorType { get; set; }
        public virtual DbSet<InvestorType> InvestorType { get; set; }
        public virtual DbSet<MappingInvestorGeographicLocation> MappingInvestorGeographicLocation { get; set; }
        public virtual DbSet<FundInvestors> FundInvestors { get; set; }
        public virtual DbSet<InvestorValuation> InvestorValuation { get; set; }
        public virtual DbSet<CompanyKPIForecastValues> CompanyKPIForecastValues { get; set; }
        public virtual DbSet<UnstructuredHistory> UnstructuredHistory { get; set; }
        public virtual DbSet<PageConfigurationFieldValueDraft> PageConfigurationFieldValueDraft { get; set; }
        public virtual DbSet<FootNotes> FootNotes { get; set; }
        public virtual DbSet<InternalReportConfiguration> InternalReportConfiguration { get; set; }
        public virtual DbSet<MappingInternalReportConfiguration> MappingInternalReportConfiguration { get; set; }
        public virtual DbSet<InternalReportFundPreference> InternalReportFundPreference { get; set; }
        public virtual DbSet<InternalReportSectionPreference> InternalReportSectionPreference { get; set; }
        public virtual DbSet<InternalReportValueTypePreference> InternalReportValueTypePreference { get; set; }
        public virtual DbSet<InternalReportPeriodTypePreference> InternalReportPeriodTypePreference { get; set; }
        public virtual DbSet<InternalReportCalculationPreference> InternalReportCalculationPreference { get; set; }
        public virtual DbSet<MPeriod> MPeriod { get; set; }
        public virtual DbSet<MCalculation> MCalculation { get; set; }
        public virtual DbSet<MInternalReportExcelTemplate> MInternalReportExcelTemplate { get; set; }
        public virtual DbSet<InternalReportExcelTemplatePreference> InternalReportExcelTemplatePreference { get; set; }
        public virtual DbSet<InternalReportPeriodConfigPreference> InternalReportPeriodConfigPreference { get; set; }
        public virtual DbSet<ReportDownloadType> ReportDownloadType { get; set; }
        public virtual DbSet<ConsolidatedReportConfiguration> ConsolidatedReportConfiguration { get; set; }
        public virtual DbSet<MappingConsolidatedReportConfiguration> MappingConsolidatedReportConfiguration { get; set; }
        public virtual DbSet<ConsolidatedReportPreference> ConsolidatedReportPreference { get; set; }
        public virtual DbSet<MConsolidatedReportExcelTemplate> MConsolidatedReportExcelTemplate { get; set; }
        public virtual DbSet<PcMasterKpiValueDraft> PcMasterKpiValueDraft { get; set; }
        public virtual DbSet<MGroupingList> MGroupingList { get; set; }
        public virtual DbSet<PortfolioCompanyCustomListDetails> PortfolioCompanyCustomListDetails { get; set; }
        public virtual DbSet<ConsolidatedExcelTemplatePreference> ConsolidatedExcelTemplatePreference{ get; set; }
        public virtual DbSet<PageConfigurationCommentaryCustomFieldValue> PageConfigurationCommentaryCustomFieldValue { get; set; }
        public virtual DbSet<ValuationModelDetail> ValuationModelDetail { get; set; }
        public virtual DbSet<MValuationFinancialKpi> MValuationFinancialKpi { get; set; }
        public virtual DbSet<BackgroundJobReportHistory> BackgroundJobReportHistory { get; set; }
        public virtual DbSet<MSubSectionFields> MSubSectionFields { get; set; }
        public virtual DbSet<DataAnalytics> DataAnalytics { get; set; }
        public virtual DbSet<Valuation_ImpliedEVRecord> Valuation_ImpliedEVRecord { get; set; }
        public virtual DbSet<Valuation_AdjustmentDetail> Valuation_AdjustmentDetail { get; set; }
        public virtual DbSet<Valuation_TargetCompanyKPIRecord> Valuation_TargetCompanyKPIRecord { get; set; }
        public virtual DbSet<Valuation_UnselectedRecords> Valuation_UnselectedRecords { get; set; }
        public virtual DbSet<FinancialAuditLog> FinancialAuditLog { get; set; }
        public virtual DbSet<File_Upload_Status> File_Upload_Status { get; set; }

        public virtual DbSet<M_ESGKPI> M_ESGKPI { get; set; }
        public virtual DbSet<Mapping_EsgKpi> Mapping_EsgKpi { get; set; }
        public virtual DbSet<ValuationCompanyEquityCalculation> ValuationCompanyEquityCalculation { get; set; }
        public virtual DbSet<ValuationEquityValue> ValuationEquityValue { get; set; }
        public virtual DbSet<Esg_Kpi_DataRecords> Esg_Kpi_DataRecords { get; set; }
        public virtual DbSet<M_ValuationHeaderType> M_ValuationHeaderType { get; set; }
        public virtual DbSet<MappingCompanyValuationHeaderType> MappingCompanyValuationHeaderType { get; set; }
        public virtual DbSet<DocumentsInformation> DocumentsInformation { get; set; }
        public virtual DbSet<DocumentComments> DocumentComments { get; set; }
        public virtual DbSet<EsgKpiStaticValues> EsgKpiStaticValues { get; set; }
        public virtual DbSet<PortfolioCompanyCommentaryDraft> PortfolioCompanyCommentaryDraft { get; set; }
        public virtual DbSet<PcCustomCommentaryDraft> PcCustomCommentaryDraft { get; set; }
        public virtual DbSet<EsgKpiAuditLog> EsgKpiAuditLog { get; set; }
        public virtual DbSet<MCapTable> MCapTable { get; set; }
        public virtual DbSet<MappingCapTable> MappingCapTable { get; set; }
        public virtual DbSet<MKpiType> MKpiType { get; set; }
        public virtual DbSet<PcCapTableValues> PcCapTableValues { get; set; }
        public virtual DbSet<CapTablePeriod> CapTablePeriod { get; set; }
        public virtual DbSet<CapTableAuditLog> CapTableAuditLog { get; set; }
        public virtual DbSet<MappingUserGroupCompany> MappingUserGroupCompany { get; set; }
        public virtual DbSet<PCFinancialsValues> PCFinancialsValues { get; set; }
         public virtual DbSet<ValuationConsolidatedAttributeDetail> ValuationConsolidatedAttributeDetail { get; set; }
        public virtual DbSet<DataRequest> DataRequest { get; set; }
        public virtual DbSet<ExternalUser> ExternalUser { get; set; }
        public virtual DbSet<DataRequestGroup> DataRequestGroup { get; set; }
        public virtual DbSet<DataRequestReminders> DataRequestReminders { get; set; }
        public virtual DbSet<DataRequestAttachments> DataRequestAttachments { get; set; }
        public virtual DbSet<SDGImages> SDGImages { get; set; }
        public virtual DbSet<UserBrowserDetails> UserBrowserDetails { get; set; }
        public virtual DbSet<MMonthlyReport> MMonthlyReport { get; set; }
        public virtual DbSet<MappingMonthlyReport> MappingMonthlyReport { get; set; }
        public virtual DbSet<MLpReportTemplate> MLpReportTemplate { get; set; }
        public virtual DbSet<MappingLpReportKpiSection> MappingLpReportKpiSection { get; set; }
        public virtual DbSet<MappingLpReportPeriodSection> MappingLpReportPeriodSection { get; set; }
        public virtual DbSet<MappingLpReportSection> MappingLpReportSection { get; set; }
        public virtual DbSet<MappingLpReportTemplate> MappingLpReportTemplate { get; set; }
        public virtual DbSet<MLpReportSection> MLpReportSection { get; set; }
        public virtual DbSet<LPReportHeader> LPReportHeader { get; set; }
        public virtual DbSet<MappingLpReportCommentarySection> MappingLpReportCommentarySection { get; set; }
        public virtual DbSet<DashboardTrackerConfig> DashboardTrackerConfig { get; set; }
        public virtual DbSet<GrowthReportHeader> GrowthReportHeader { get; set; }
        public virtual DbSet<MappingGrowthReport> MappingGrowthReport { get; set; }
        public virtual DbSet<GrowthReportHeaderModule> GrowthReportHeaderModule { get; set; }
        public virtual DbSet<GrowthReportHeaderKpi> GrowthReportHeaderKpi { get; set; }
        public virtual DbSet<GrowthReportColumnKpi> GrowthReportColumnKpi { get; set; }
        public virtual DbSet<DataExtractionTypes> DataExtractionTypes { get; set; }
        public virtual DbSet<MSourceTypes> MSourceTypes { get; set; }

        public virtual DbSet<CLO_InvestmentCompanyDetails> InvestmentCompanyDetails { get; set; }
        public virtual DbSet<M_clopagedetails> CloPageDetails { get; set; }
        public virtual DbSet<CLO_M_Table> CloTableDetails { get; set; }
        public virtual DbSet<DocCollectionFrequencyConfig> DocumentCollectionConfiguration { get; set; }
        public virtual DbSet<DocumentCollectionStore> DocumentCollectionStore { get; set; }
        public virtual DbSet<RepositoryDocumentMappingDetail> DocumentFolderMappings { get; set; }
        public virtual DbSet<MFundSectionKpi> MFundSectionKpi { get; set; }
        public virtual DbSet<MappingFundSectionKpi> MappingFundSectionKpi { get; set; }
        public virtual DbSet<FundMasterKpiValues> FundMasterKpiValues { get; set; }
        public virtual DbSet<FundKpiAuditLog> FundKpiAuditLog { get; set; }
        public virtual DbSet<MFundKpiModules> MFundKpiModules { get; set; }
        public virtual DbSet<M_UserCategory> M_UserCategory { get; set; }
        public virtual DbSet<UserInformation> UserInformation { get; set; }
        public virtual DbSet<User_Documents> User_Documents { get; set; }
        public DbSet<EmailNotificationGroup> EmailNotificationGroups { get; set; }
        public DbSet<EmailListMember> EmailListMembers { get; set; }

        public DbSet<CompanyEmailGroup> CompanyEmailGroups { get; set; }
        public virtual DbSet<EmailReminder> EmailReminder { get; set; }
        public virtual DbSet<EmailReminderRecipients> EmailReminderRecipients { get; set; }
        public virtual DbSet<EmailReminderConfig> EmailReminderConfig { get; set; }
        public virtual DbSet<EmailReminderSchedule> EmailReminderSchedule { get; set; }

        protected override void OnConfiguring (DbContextOptionsBuilder optionsBuilder) {
            optionsBuilder.UseLazyLoadingProxies ();
        }

        protected override void OnModelCreating (ModelBuilder modelBuilder) {
            modelBuilder.HasAnnotation ("ProductVersion", "2.2.4-servicing-10062");
            modelBuilder.ApplyConfiguration (new CommentaryDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_ImpactKPI_OrderConfiguration ());
            modelBuilder.ApplyConfiguration (new DataAuditLogConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlowCompanyWiseDataConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlowDateWiseSummaryConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlowFileLogsConfiguration ());
            modelBuilder.ApplyConfiguration (new CashflowCalculationDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new CashflowTotalCalculationDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new CompanyFundingDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new CompanySummaryDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new DealDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new DynamicQueriesConfiguration ());
            modelBuilder.ApplyConfiguration (new DynamicQueriesCopyConfiguration ());
            modelBuilder.ApplyConfiguration (new ElmahErrorConfiguration ());
            modelBuilder.ApplyConfiguration (new EmailTemplateConfiguration ());
            modelBuilder.ApplyConfiguration (new EmployeeDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new FirmDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new FundDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new FundTrackRecordConfiguration ());
            modelBuilder.ApplyConfiguration (new GlobalVariablesConfiguration ());
            modelBuilder.ApplyConfiguration (new M_AccountTypeConfiguration ());
            modelBuilder.ApplyConfiguration (new ActionsConfiguration ());
            modelBuilder.ApplyConfiguration (new M_CityConfiguration ());
            modelBuilder.ApplyConfiguration (new CompanyKPIConfiguration ());
            modelBuilder.ApplyConfiguration (new M_ImpactKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_ImpactKPI_OrderConfiguration ());
            modelBuilder.ApplyConfiguration (new M_InvestmentKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new M_CountryConfiguration ());
            modelBuilder.ApplyConfiguration (new CurrrencyConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealBoardSeatConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealExitMethodConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealInvestmentStageConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealSecurtyTypeConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealSourcingConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealTransactionRoleConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DealValuationMethodologyConfiguration ());
            modelBuilder.ApplyConfiguration (new M_DesignationConfiguration ());
            modelBuilder.ApplyConfiguration (new FeaturesConfiguration ());
            modelBuilder.ApplyConfiguration (new M_SubFeatureConfiguration());
            modelBuilder.ApplyConfiguration (new MFinancialKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new M_FinancialStatusConfiguration ());
            modelBuilder.ApplyConfiguration (new M_FirmTypeConfiguration ());
            modelBuilder.ApplyConfiguration (new M_FundHoldingStatusConfiguration ());
            modelBuilder.ApplyConfiguration (new M_FundingTypeConfiguration ());
            modelBuilder.ApplyConfiguration (new M_GeographyConfiguration ());
            modelBuilder.ApplyConfiguration (new M_GroupsConfiguration ());
            modelBuilder.ApplyConfiguration (new M_HeadquarterConfiguration ());
            modelBuilder.ApplyConfiguration (new M_InvestmentStageConfiguration ());
            modelBuilder.ApplyConfiguration (new M_OperationalKPIConfiguration ());
            modelBuilder.ApplyConfiguration (new M_OwnershipStatusConfiguration ());
            modelBuilder.ApplyConfiguration (new M_PipelineStatusConfiguration ());
            modelBuilder.ApplyConfiguration (new M_RegionConfiguration ());
            modelBuilder.ApplyConfiguration (new M_SectorConfiguration ());
            modelBuilder.ApplyConfiguration (new M_SubSectorConfiguration ());
            modelBuilder.ApplyConfiguration (new M_SectorwiseKPIConfiguration ());
            modelBuilder.ApplyConfiguration (new M_StateConfiguration ());
            modelBuilder.ApplyConfiguration (new M_StockExchangeConfiguration ());
            modelBuilder.ApplyConfiguration (new M_StrategyConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_CountryCurrencyConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_FeatureActionConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_SubFeatureActionConfiguration());
            modelBuilder.ApplyConfiguration (new Mapping_FirmEmployeeConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_FirmFundConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_FirmGeographicLocationConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_FirmHeadquarterLocationConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_GroupFeatureConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_LocationsConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_PCEmployeeConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_PCGeographicLocationConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_UserGroupConfiguration ());
            modelBuilder.ApplyConfiguration (new PcCompanywiseKpiMonthlyValueConfiguration ());
            modelBuilder.ApplyConfiguration (new PCFinancialKPIMonthlyValueConfiguration ());
            modelBuilder.ApplyConfiguration (new PCOperationalKPIMonthlyValueConfiguration ());
            modelBuilder.ApplyConfiguration (new PCSectorwiseKPIMonthlyValueConfiguration ());
            modelBuilder.ApplyConfiguration (new PipelineDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanyDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanyEmployeeDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanyFundHoldingDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanyProfitabilityDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanySectorwiseKPIMonthlyConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanySectorwiseKPIValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new UserDetailsConfiguration ());
            modelBuilder.ApplyConfiguration (new View_LocationMappingConfiguration ());
            modelBuilder.ApplyConfiguration (new View_RegionCountryMappingConfiguration ());
            modelBuilder.ApplyConfiguration (new KPIValuesFileLogsConfiguration ());
            modelBuilder.ApplyConfiguration (new MappingPortfolioCompanyKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new MappingPortfolioInvestmentKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new M_KpitypesConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanyOperationalKPIQuarterConfiguration ());
            modelBuilder.ApplyConfiguration (new PortfolioCompanyOperationalKPIValueConfiguration ());
            modelBuilder.ApplyConfiguration (new M_BalanceSheet_LineItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_CompanyBalanceSheetLineItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new BalanceSheetValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new ProfitAndLossAnnualHistoricalDataConfiguration ());
            modelBuilder.ApplyConfiguration (new ProfitAndLossIccaseValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new M_ProfitAndLoss_LineItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_CompanyProfitAndLossLineItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new ProfitAndLossValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new ProfitAndLossForecastDataConfiguration ());
            modelBuilder.ApplyConfiguration (new BalanceSheetForecastDataConfiguration ());
            modelBuilder.ApplyConfiguration (new BalanceSheetICCaseValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new M_CashFlow_LineItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_CompanyCashFlowLineItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlowValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlowICCaseValuesConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlow_AnnualHistoricalDataConfiguration ());
            modelBuilder.ApplyConfiguration (new CashFlow_ForecastDataConfiguration ());
            modelBuilder.ApplyConfiguration (new BalanceSheet_AnnualHistoricalDataConfiguration ());
            modelBuilder.ApplyConfiguration (new MappingPortfolioInvestmentKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new MappingPortfolioImpactKpiConfiguration ());
            modelBuilder.ApplyConfiguration (new M_StandingDataItemsConfiguration ());
            modelBuilder.ApplyConfiguration (new DocumentTypeConfiguration ());
            modelBuilder.ApplyConfiguration (new DocumentConfiguration ());
            modelBuilder.ApplyConfiguration (new UserReportConfiguration ());
            modelBuilder.ApplyConfiguration (new UserReportFilterConfiguration ());
            modelBuilder.ApplyConfiguration (new Mapping_UserSubFeatureConfiguration());
            modelBuilder.ApplyConfiguration (new MappingPortfolioOperationalKpiConfiguration());
            modelBuilder.ApplyConfiguration (new Mapping_LPReportConfig());
            modelBuilder.ApplyConfiguration (new M_LPReportConfig());
            modelBuilder.ApplyConfiguration (new CurrencyRateConfiguration());
            modelBuilder.ApplyConfiguration (new ApiCurrencyRateConfiguration());
            modelBuilder.ApplyConfiguration (new MappingPCGeographicLocationConfigurationDraft());
            modelBuilder.ApplyConfiguration (new MappingPCEmployeeConfigurationDraft());
            modelBuilder.ApplyConfiguration(new PortfolioCompanyOperationalKPIQuarterDraftConfiguration());
            modelBuilder.ApplyConfiguration(new PortfolioCompanyOperationalKPIValueDraftConfiguration());
            modelBuilder.ApplyConfiguration(new InvestmentCompanyDetailsConfiguration());
            modelBuilder.ApplyConfiguration(new CloDetailsConfigurations());
            modelBuilder.ApplyConfiguration(new CloCommentriesConfiguration());
            modelBuilder.ApplyConfiguration(new TableFootnoteConfiguration());
            modelBuilder.ApplyConfiguration(new CloPageDetailsConfigurations());

            modelBuilder.ApplyConfiguration(new DocumentCollectionConfiguration());


        }
    }
}