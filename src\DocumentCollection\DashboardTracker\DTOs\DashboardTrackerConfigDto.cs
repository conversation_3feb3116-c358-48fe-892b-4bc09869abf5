using System;

namespace DocumentCollection.DashboardTracker.DTOs
{
    public class DashboardTrackerConfigDto
    {
        public int? ID { get; set; } // Nullable for insert
        public int FieldType { get; set; }
        public int DataType { get; set; }
        public string Name { get; set; } = "Custom Column";
        public int? FrequencyType { get; set; }
        public string? StartPeriod { get; set; }
        public string? EndPeriod { get; set; }
        public bool? IsPrefix { get; set; }
        public string? TimeSeriesDateFormat { get; set; }
        public int? MapTo { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;
        public DateTime? CreatedOn { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public int? ModifiedBy { get; set; }
    }
}