using Audit.Enums;
using AutoMapper;
using Contract.Configuration;
using Contract.ConsolidatedReport;
using Contract.Deals;
using Contract.Employee;
using Contract.FootNotes;
using Contract.Funds;
using Contract.FxRates;
using Contract.KPI;
using Contract.PortfolioCompany;
using CurrencyRates;
using Dapper;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using DataAnalytic.Helpers;
using DataAnalytic.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared;
using Shared.Enums;
using System.Dynamic;
using System.Threading.Tasks;

namespace DataAnalytic.Services
{
    public class PcAnalyticsService(IUnitOfWork unitOfWork, IDapperGenericRepository dapperGenericRepository, IMapper mapper, ICurrencyRateConversionService currencyRateConversionService, IStaticDetailsServices staticDetailsServices, ILogger<PcAnalyticsService> logger) : IPcAnalyticsService
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private readonly IDapperGenericRepository _dapperGenericRepository = dapperGenericRepository;
        private readonly IMapper _mapper = mapper;
        private readonly ICurrencyRateConversionService _currencyRateConversionService = currencyRateConversionService;
        private readonly IStaticDetailsServices _staticDetailsServices = staticDetailsServices;
        private readonly ILogger<PcAnalyticsService> _logger = logger;
        public List<DataAnalyticsPortfolioCompanyDetails> PortfolioStaticDetails() => _dapperGenericRepository.QueryNonAsync<DataAnalyticsPortfolioCompanyDetails>(SqlConstants.QueryGetPortfolioCompanyDetails);

        public List<MappingEmployeeModel> PortfolioInvestmentProfessionals() => _dapperGenericRepository.QueryNonAsync<MappingEmployeeModel>(SqlConstants.QueryGetPortfolioCompanyInvestmentProfessionals);

        public List<DataAnalyticsGeographicLocation> PortfolioGeographicLocation() => _dapperGenericRepository.QueryNonAsync<DataAnalyticsGeographicLocation>(SqlConstants.QueryGetPortfolioCompanyLocationDetails);
        /// <summary>
        /// Retrieves a list of KPI modules that match the given sub-page IDs and are not deleted.
        /// Also adds commentary footnotes if applicable.
        /// </summary>
        /// <param name="subPageIds">The list of sub-page IDs to filter the KPI modules.</param>
        /// <returns>A list of KPI modules with their associated sub-page fields and commentary footnotes.</returns>
        public async Task<List<KpiModules>> GetPageConfigByKpiModules(List<int> subPageIds)
        {
            // Fetch non-deleted sub-page fields that match the given subPageIds
            var activeSubPageFields = _unitOfWork.SubPageFieldsRepository
                .GetManyQueryable(x => !x.IsDeleted && subPageIds.Contains(x.SubPageID)).ToList();
            var commentaryDetail = await _unitOfWork.SubPageDetailsRepository.FindFirstAsync(x => x.SubPageID == (int)PageConfigurationSubFeature.Commentary && !x.IsDeleted && x.IsActive && x.IsFootNote);
            // Fetch non-deleted KPI modules
            var activeKpiModules = _unitOfWork.M_KpiModulesRepository
                .GetManyQueryable(x => !x.IsDeleted);
            var joinedData = activeKpiModules.Join(
                activeSubPageFields,
                kpiModule => kpiModule.PageConfigFieldName,
                subPageField => subPageField.Name,
                (kpiModule, subPageField) => new KpiModules
                {
                    ModuleID = kpiModule.ModuleID,
                    PageConfigAliasName = subPageField.AliasName,
                    SubPageId = subPageField.SubPageID
                }).ToList();
            PcAnalyticsHelper.AddCommentaryFootNotes(commentaryDetail, joinedData);
            return joinedData;
        }

        public async Task<List<FootNoteModel>> PortfolioFootNotes()
        {
            List<int> subPageIds = [(int)PageConfigurationSubFeature.KeyPerformanceIndicator, (int)PageConfigurationSubFeature.CompanyFinancials, (int)PageConfigurationSubFeature.CapTable, (int)PageConfigurationSubFeature.OtherKPIs];
            List<KpiModules> kpiModules = await GetPageConfigByKpiModules(subPageIds);
            var footNotes = _unitOfWork.FootNotesRepository.GetManyQueryable(x => !x.IsDeleted).ToList();
            return [.. footNotes.Join(
                    kpiModules,
                    footnote => footnote.ModuleId,
                    kpiModule => kpiModule.ModuleID,
                    (footnote, kpiModule) => new FootNoteModel
                    {
                        FootNote = PcAnalyticsHelper.RemoveHtmlTags(footnote.FootNote),
                        FootNoteId = footnote.FootNoteId,
                        CompanyId = footnote.CompanyId,
                        ModuleId = footnote.ModuleId,
                        ModuleName = kpiModule.PageConfigAliasName,
                        EncryptedFootNoteId = footnote.EncryptedFootNoteId,
                    })];
        }
        public async Task<List<SubPageFieldModel>> GetPageConfigActiveFieldsBySubPageId(List<int> subPageId)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && subPageId.Contains(x.SubPageID));
            return configurations.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
        }
        /// <summary>
        /// Retrieves the portfolio company details by section for a given user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="filter">The data analytics filter.</param>
        /// <returns>A list of dynamic objects representing the portfolio company details.</returns>
        public async Task<List<ExpandoObject>> GetPortfolioCompanyDetailsBySection(int userId, DataAnalyticsPcFilter filter)
        {

            if (userId == 0)
            {
                List<int> subPageIds = new() { (int)PageConfigurationSubFeature.StaticInformation, (int)PageConfigurationSubFeature.GeographicLocations, (int)PageConfigurationSubFeature.InvestmentProfessionals, (int)PageConfigurationSubFeature.Commentary };
                List<SubPageFieldModel> subPageFieldModels = await GetPageConfigActiveFieldsBySubPageId(subPageIds);
                List<DataAnalyticsPortfolioCompanyDetails> companyDetails = PortfolioStaticDetails();
                filter.CompanyId = filter.CompanyId?.Count > 0 ? filter.CompanyId : companyDetails.ConvertAll(x => x.PortfolioCompanyId).ToList();
                List<ExpandoObject> finalResponse = new();
                DataAnalyticStaticModel staticData = await GetStaticDataAnalyticsModel(filter);
                staticData.PortfolioCompanyDetails = companyDetails;
                staticData.SubPageFieldModels = subPageFieldModels;
                KpiValuesByCategory kpiValuesByCategory = await GetKpiValuesByCategory(filter);
                GeneratePortfolioCompanyAnalyticsResponse(filter, staticData, finalResponse, kpiValuesByCategory);
                return finalResponse;
            }
            else
            {
                var accesses = await _dapperGenericRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetSubFeaturePermissions, new { @FeatureId = (int)FeatureTypes.PortfolioCompany, @UserId = userId, @Id = 0 });
                var allowedCompanies = accesses?.GroupBy(x => x.CompanyId).Where(g => g.Any(x => x.CanView));
                List<int> subPageIds = new() { (int)PageConfigurationSubFeature.StaticInformation, (int)PageConfigurationSubFeature.GeographicLocations, (int)PageConfigurationSubFeature.InvestmentProfessionals, (int)PageConfigurationSubFeature.Commentary };
                List<SubPageFieldModel> subPageFieldModels = await GetPageConfigActiveFieldsBySubPageId(subPageIds);
                List<DataAnalyticsPortfolioCompanyDetails> companyDetails = PortfolioStaticDetails();
                var cIds = filter.CompanyId?.Count > 0 ? filter.CompanyId : companyDetails.ConvertAll(x => x.PortfolioCompanyId).ToList();
                filter.CompanyId = cIds.Intersect(allowedCompanies?.Select(g => g.Key).ToList() ?? new List<int>()).ToList();
                List<ExpandoObject> finalResponse = new();
                DataAnalyticStaticModel staticData = await GetStaticDataAnalyticsModel(filter);
                staticData.PortfolioCompanyDetails = companyDetails;
                staticData.SubPageFieldModels = subPageFieldModels;
                KpiValuesByCategory kpiValuesByCategory = await GetKpiValuesByCategory(filter);
                filter.AllowedCompanies = allowedCompanies?.ToList() ?? new List<IGrouping<int, SubFeatureAccessPermissionsModel>>();
                GeneratePortfolioCompanyAnalyticsResponse(filter, staticData, finalResponse, kpiValuesByCategory);
                return finalResponse;
            }
        }

        /// <summary>
        /// Get Pc details for reveal bi analytics
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<List<ExpandoObject>> GetPortfolioCompanyDetailsForAnalytics(DataAnalyticsBiFilter filter)
        {
            List<int> subPageIds = new() { (int)PageConfigurationSubFeature.StaticInformation, (int)PageConfigurationSubFeature.GeographicLocations };
            List<SubPageFieldModel> subPageFieldModels = await GetPageConfigActiveFieldsBySubPageId(subPageIds);
            List<DataAnalyticsPortfolioCompanyDetails> portfolioCompanyDetails = PortfolioStaticDetails();
            List<DataAnalyticsGeographicLocation> portfolioGeographicLocation = PortfolioGeographicLocation();
            filter.CompanyId = filter.CompanyId?.Count > 0 ? filter.CompanyId : portfolioCompanyDetails.ConvertAll(x => x.PortfolioCompanyId).ToList();
            return await CreateDataAnalyticsResponse(filter, subPageFieldModels, portfolioCompanyDetails, portfolioGeographicLocation);
        }
        /// <summary>
        /// Retrieves PC analytics details based on the provided filter.
        /// </summary>
        /// <param name="filter">The filter to apply to the analytics data.</param>
        /// <returns>A list of dictionaries representing the PC analytics details.</returns>
        public async Task<List<Dictionary<string, object>>> GetPcAnalyticsDetails(DataAnalyticsBiFilter filter)
        {
            List<Dictionary<string, object>> finalResult = new();
            List<int> subPageIds = filter.DataAnalyticsKpiFilter.PageConfigFields.Select(x => x.SubPageId).Distinct().ToList();
            List<int> fieldIds = filter.DataAnalyticsKpiFilter.PageConfigFields.Select(x => x.FieldId).Distinct().ToList();
            if (filter.ModuleIds is not null && filter.ModuleIds.Count > 0)
            {
                await GetPcKpiValues(filter, finalResult, subPageIds, fieldIds);
            }
            else
            {
                await GetPcStaticValues(filter, finalResult, subPageIds, fieldIds);
            }
            return finalResult;
        }
        /// <summary>
        /// Retrieves PC KPI values based on the provided filter.
        /// </summary>
        /// <param name="filter">The data analytics filter.</param>
        /// <param name="finalResult">The list to store the final result.</param>
        /// <param name="subPageIds">The list of sub page IDs.</param>
        /// <param name="fieldIds">The list of field IDs.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task GetPcKpiValues(DataAnalyticsBiFilter filter, List<Dictionary<string, object>> finalResult, List<int> subPageIds, List<int> fieldIds)
        {
            foreach (var moduleId in filter.ModuleIds)
            {
                List<dynamic>? result = null;
                string kpiIds = filter.GetKpiIdsBySection(moduleId);
                if (filter.IsParentChildGraph && filter.ModuleIds.Count == 1 && kpiIds?.Split(",")?.Length == 1)
                {
                    result = await GetParentChildGraphResult(filter, subPageIds, fieldIds, moduleId, kpiIds);
                }
                else
                {
                    result = await _dapperGenericRepository.Query<dynamic>(SqlConstants.QueryByGetAnalyticsAllKpiValues, new
                    {
                        @CompanyIds = filter.CompanyId != null ? string.Join(",", filter.CompanyId) : null,
                        @moduleId = moduleId,
                        @kpiIds = kpiIds,
                        @fromYear = Convert.ToDateTime(filter.StartDate).Year.ToString(),
                        @toYear = Convert.ToDateTime(filter.EndDate).Year.ToString(),
                        @subPageIds = subPageIds.Count > 0 ? string.Join(",", subPageIds) : null,
                        @fieldIds = fieldIds.Count > 0 ? string.Join(",", fieldIds) : null,
                        @ToCurrencyId = filter.ToCurrencyId,
                        @CurrencyRateType = filter.CurrencyRateSource
                    });
                }
                if (result is not null)
                {
                    var updatedResult = result.Select(item => ((IDictionary<string, object>)item)
                        .Where(property => !IsExcludedProperty(property.Key))
                        .ToDictionary(property => property.Key, property => property.Value))
                        .ToList();
                    finalResult.AddRange(updatedResult);
                }
            }
        }

        private async Task<List<dynamic>?> GetParentChildGraphResult(DataAnalyticsBiFilter filter, List<int> subPageIds, List<int> fieldIds, int moduleId, string kpiIds)
        {
            _ = int.TryParse(filter.GetKpiIdsBySection(moduleId), out int ParentKpiId);

            var result = await _dapperGenericRepository.Query<dynamic>(SqlConstants.QueryByGetAnalyticsParentChildValuesGetAnalyticsAllKpiValues, new
            {
                @CompanyIds = filter.CompanyId != null ? string.Join(",", filter.CompanyId) : null,
                @moduleId = moduleId,
                @kpiIds = kpiIds,
                @fromYear = Convert.ToDateTime(filter.StartDate).Year.ToString(),
                @toYear = Convert.ToDateTime(filter.EndDate).Year.ToString(),
                @subPageIds = subPageIds.Count > 0 ? string.Join(",", subPageIds) : null,
                @fieldIds = fieldIds.Count > 0 ? string.Join(",", fieldIds) : null,
                @ToCurrencyId = filter.ToCurrencyId,
                @CurrencyRateType = filter.CurrencyRateSource,
                @ParentKpiId = ParentKpiId
            });
            return result;
        }

        /// <summary>
        /// Retrieves PC static values based on the provided filter, subPageIds, and fieldIds.
        /// </summary>
        /// <param name="filter">The data analytics filter.</param>
        /// <param name="finalResult">The list to store the final result.</param>
        /// <param name="subPageIds">The list of subPageIds.</param>
        /// <param name="fieldIds">The list of fieldIds.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task GetPcStaticValues(DataAnalyticsBiFilter filter, List<Dictionary<string, object>> finalResult, List<int> subPageIds, List<int> fieldIds)
        {
            var resultStatic = await _dapperGenericRepository.Query<dynamic>(SqlConstants.QueryByGetAnalyticsAllStaticValues, new
            {
                @CompanyIds = filter.CompanyId != null ? string.Join(",", filter.CompanyId) : null,
                @subPageIds = subPageIds.Count > 0 ? string.Join(",", subPageIds) : null,
                @fieldIds = fieldIds.Count > 0 ? string.Join(",", fieldIds) : null
            });
            if (resultStatic is not null)
            {
                var updatedResult = resultStatic.Select(item => ((IDictionary<string, object>)item)
                      .ToDictionary(property => property.Key, property => property.Value))
                      .ToList();
                finalResult.AddRange(updatedResult);
            }
        }

        /// <summary>
        /// Determines if a property is excluded based on its name.
        /// </summary>
        /// <param name="propertyName">The name of the property to check.</param>
        /// <returns><c>true</c> if the property is excluded; otherwise, <c>false</c>.</returns>
        private static bool IsExcludedProperty(string propertyName)
        {
            string[] excludedProperties = [Constants.PcId, Constants.FundId, Constants.DealId, Constants.Quarter, Constants.Year];
            return excludedProperties.Contains(propertyName, StringComparer.OrdinalIgnoreCase);
        }
        /// <summary>
        /// This method returns a list of Static Sub page fields for deal, fund and investor.
        /// </summary>
        /// <returns>list of Static Sub page fields for deal, fund and investor.</returns>
        public async Task<StaticSubPageFieldModel> GetStaticFields()
        {
            StaticSubPageFieldModel StaticSubPageFieldModel = new();
            List<int> fundSubPageIds = new() { (int)PageConfigurationSubFeature.FundStaticInformation, (int)PageConfigurationSubFeature.FundTerms, (int)PageConfigurationSubFeature.TrackRecord, (int)PageConfigurationSubFeature.GeographicLocationsFund };
            List<SubPageFieldAnalyticsModel> fundSubPageFieldModels = await GetStaticFieldsBySubPageId(fundSubPageIds);
            StaticSubPageFieldModel.FundStaticFields = fundSubPageFieldModels;
            List<int> investorSubPageIds = new() { (int)PageConfigurationSubFeature.InvestorInformation, (int)PageConfigurationSubFeature.InvestorGeographicalLocations, (int)PageConfigurationSubFeature.InvestorFunds, (int)PageConfigurationSubFeature.CompanyPerformance, (int)PageConfigurationSubFeature.ValuationData, (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails, (int)PageConfigurationSubFeature.TrackRecord, (int)PageConfigurationSubFeature.ValuationData };
            List<SubPageFieldAnalyticsModel> investorSubPageFieldModels = await GetStaticFieldsBySubPageId(investorSubPageIds);
            StaticSubPageFieldModel.InvestorStaticFields = investorSubPageFieldModels;
            List<int> dealSubPageIds = new() { (int)PageConfigurationSubFeature.BasicDetails, (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails };
            List<SubPageFieldAnalyticsModel> dealSubPageFieldModels = await GetStaticFieldsBySubPageId(dealSubPageIds);
            StaticSubPageFieldModel.DealStaticFields = dealSubPageFieldModels;
            return StaticSubPageFieldModel;
        }

        /// <summary>
        /// This method returns a list of Fixed Static Sub page fields 
        /// </summary>
        /// <returns>list of Static Sub page fields for deal, fund and investor.</returns>
        public async Task<StaticSubPageFieldModel> GetFixedStaticFields()
        {
            StaticSubPageFieldModel staticSubPageFieldModel = new();
            List<SubPageFieldAnalyticsModel> result = new();
            List<SubPageFieldAnalyticsModel> subPageFieldModels = await GetFixedFieldData();
            subPageFieldModels = subPageFieldModels.OrderBy(x => x.SubPageID).ToList();
            var investorData = subPageFieldModels.FirstOrDefault(x => x.Name == Constants.InvestorName);
            var compData = subPageFieldModels.FirstOrDefault(x => x.Name == Constants.CompanyName);
            var fundData = subPageFieldModels.FirstOrDefault(x => x.Name == Constants.FundName);
            if (investorData != null)
                result.Add(investorData);
            if (fundData != null)
                result.Add(fundData);
            if (compData != null)
                result.Add(compData);
            staticSubPageFieldModel.FixedStaticFields = result;
            return staticSubPageFieldModel;
        }
        /// <summary>
        /// GetFixedFieldData
        /// </summary>
        /// <returns></returns>
        private async Task<List<SubPageFieldAnalyticsModel>> GetFixedFieldData()
        {
            List<int> subPageIds = new() { (int)PageConfigurationSubFeature.FundStaticInformation, (int)PageConfigurationSubFeature.StaticInformation, (int)PageConfigurationSubFeature.InvestorInformation };
            List<SubPageFieldAnalyticsModel> subPageFieldModels = await GetStaticFieldsBySubPageId(subPageIds);
            subPageFieldModels = subPageFieldModels.Where(x => x.Name == Constants.CompanyName || x.Name == Constants.FundName || x.Name == Constants.InvestorName).ToList();
            return subPageFieldModels;
        }

        /// <summary>
        /// This method returns a list of `SubPageFieldModel` objects for the specified sub-page IDs.
        /// </summary>
        /// <param name="subPageId">The list of sub-page IDs.</param>
        /// <returns>A list of `SubPageFieldModel` objects.</returns>
        public async Task<List<SubPageFieldAnalyticsModel>> GetStaticFieldsBySubPageId(List<int> subPageId)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && subPageId.Contains(x.SubPageID));
            return configurations.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldAnalyticsModel>(x)).ToList();
        }

        public List<ConsolidatedFundDetails> GetFundDetials(int fundId)
        {
            var fundIds = new List<int>() { fundId };
            return _dapperGenericRepository.Query<ConsolidatedFundDetails>(SqlConstants.QueryByGetConsolidatedFundDetails, new { FundIds = fundIds }).Result.ToList();
        }
        public async Task<List<ExpandoObject>> CreateDataAnalyticsResponse(DataAnalyticsBiFilter filter, List<SubPageFieldModel> subPageFieldModels, List<DataAnalyticsPortfolioCompanyDetails> portfolioCompanyDetails, List<DataAnalyticsGeographicLocation> portfolioGeographicLocation)
        {
            List<SubPageFieldModel> pageFieldModels = await PageConfigStaticFields(filter);
            var mergedList = new List<ExpandoObject>();
            foreach (var companyId in filter.CompanyId)
            {
                ExpandoObject expando = new();
                ExpandoObject expandoStatic = new();
                var portfolioResponse = expando as IDictionary<string, object>;
                portfolioResponse[Constants.PortfolioCompanyId] = companyId;
                await _staticDetailsServices.CreateStaticDetailsResponseDynamic(filter, pageFieldModels, portfolioCompanyDetails, portfolioGeographicLocation, companyId, expandoStatic);
                foreach (var section in filter.ModuleIds)
                {
                    switch (section)
                    {
                        case (int)KpiModuleType.Investment:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.Investment, "InvestmentKPIs");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "InvestmentKPIs", expandoStatic);
                            break;

                        case (int)KpiModuleType.Operational:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.Operational, "OperationalKPIs");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "OperationalKPIs", expandoStatic);
                            break;

                        case (int)KpiModuleType.TradingRecords:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.TradingRecords, "TradingRecords");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "TradingRecords", expandoStatic);
                            break;

                        case (int)KpiModuleType.CreditKPI:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.CreditKPI, "CreditKPI");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "CreditKPI", expandoStatic);
                            break;

                        case (int)KpiModuleType.Company:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.Company, "CompanyKPIs");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "CompanyKPIs", expandoStatic);
                            break;

                        case (int)KpiModuleType.ProfitAndLoss:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.ProfitAndLoss, "ProfitLoss");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "ProfitLoss", expandoStatic);
                            break;

                        case (int)KpiModuleType.BalanceSheet:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.BalanceSheet, "BalanceSheet");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "BalanceSheet", expandoStatic);
                            break;

                        case (int)KpiModuleType.CashFlow:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.CashFlow, "Cashflow");
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "Cashflow", expandoStatic);
                            break;
                        case (int)KpiModuleType.Impact:
                            await GetDataByModuleId(portfolioResponse, filter, companyId, (int)KpiModuleType.Impact, Constants.ImpactKPIs);
                            PcAnalyticsHelper.GetKeysBySection(mergedList, expando, "ImpactKPIs", expandoStatic);
                            break;
                    }
                }
                if (filter.ModuleIds.Count == 0)
                    mergedList.Add(expandoStatic);
            }
            return mergedList;
        }
        private async Task GetDataByModuleId(IDictionary<string, object> portfolioResponse, DataAnalyticsBiFilter filter, int companyId, int moduleId, string itemKey)
        {
            string query = PcAnalyticsHelper.GetQuery(filter, moduleId);
            string kpiIds = filter.GetKpiIdsBySection(moduleId);
            List<DataAnalyticsLineItemValues> result = new();
            if (filter.IsAnalytics && filter.ToCurrencyId > 0)
            {
                var financialData = await _dapperGenericRepository.QueryFirstAsync<DataAnalyticsCurrency>(SqlConstants.QueryByDataAnalyticsCurrencyData, new { companyId = companyId, moduleId = moduleId });
                int fye = financialData?.FinancialYearEnd != null ? PcAnalyticsHelper.GetFinancialYearEnd(financialData.FinancialYearEnd) : 12;
                var fromCurrencyId = financialData?.CurrencyId ?? 0;
                if (fromCurrencyId > 0 && fromCurrencyId != filter.ToCurrencyId)
                {
                    int fromYear = Convert.ToDateTime(filter.StartDate).Year;
                    int toYear = Convert.ToDateTime(filter.EndDate).Year;
                    result = await _dapperGenericRepository.Query<DataAnalyticsLineItemValues>(SqlConstants.QueryByConvertCurrencyValuesBasedOnFxRates, new { companyId, fye, toCurrencyId = filter.ToCurrencyId, fromCurrencyId, moduleId, kpiIds, currencyRateType = filter.CurrencyRateSource, fromYear, toYear });
                }
            }
            else
            {
                result = await GetAnalyticsKpiDataByQuery(companyId, query, kpiIds);
            }
            if (result?.Any() == true)
                portfolioResponse[itemKey] = result.ToList().KpiValuesToAnalyticsExpandoObjectList();
        }

        private async Task<List<DataAnalyticsLineItemValues>> GetAnalyticsKpiDataByQuery(int companyId, string query, string kpiIds)
        {
            return await _dapperGenericRepository.Query<DataAnalyticsLineItemValues>(query, new { companyId, kpiIds });
        }
        private void GeneratePortfolioCompanyAnalyticsResponse(DataAnalyticsPcFilter filter, DataAnalyticStaticModel staticModel, List<ExpandoObject> finalResponse, KpiValuesByCategory kpiValuesByCategory)
        {
            var sectionActions = new Dictionary<string, Action<string, int, IDictionary<string, object>>>
            {
                [PortfolioSections.StaticInformation] = (section, companyId, portfolioResponse) => PortfolioStaticDetails(staticModel, companyId, portfolioResponse, section),
                [PortfolioSections.GeographicLocations] = (section, companyId, portfolioResponse) => PcAnalyticsHelper.PortfolioLocationDetails(staticModel.GeographicLocation, companyId, portfolioResponse, section, staticModel.SubPageFieldModels),
                [PortfolioSections.InvestmentProfessionals] = (section, companyId, portfolioResponse) => PcAnalyticsHelper.PortfolioEmployeeDetails(staticModel.InvestmentProfessionals, companyId, portfolioResponse, section, staticModel.SubPageFieldModels),
                [PortfolioSections.InvestmentKPIs] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.Investment, companyId, portfolioResponse, section),
                [PortfolioSections.OperationalKPIs] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.Operational, companyId, portfolioResponse, section),
                [PortfolioSections.TradingRecords] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.Trading, companyId, portfolioResponse, section),
                [PortfolioSections.CreditKPI] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.Credit, companyId, portfolioResponse, section),
                [PortfolioSections.CompanyKPIs] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.Company, companyId, portfolioResponse, section),
                [PortfolioSections.ImpactKPIs] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.Impact, companyId, portfolioResponse, section),
                [PortfolioSections.Commentary] = (section, companyId, portfolioResponse) => CommentaryData(staticModel, companyId, portfolioResponse, section),
                [PortfolioSections.ProfitLoss] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.ProfitAndLoss, companyId, portfolioResponse, section),
                [PortfolioSections.BalanceSheet] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.BalanceSheet, companyId, portfolioResponse, section),
                [PortfolioSections.Cashflow] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.CashFlow, companyId, portfolioResponse, section),
                [PortfolioSections.FootNotes] = (section, companyId, portfolioResponse) => PcAnalyticsHelper.AddFootNoteKpiWise(section, staticModel.FootNoteModels, companyId, portfolioResponse, 0),
                [PortfolioSections.CustomTable1] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.CustomTable1, companyId, portfolioResponse, section),
                [PortfolioSections.CustomTable2] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.CustomTable2, companyId, portfolioResponse, section),
                [PortfolioSections.CustomTable3] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.CustomTable3, companyId, portfolioResponse, section),
                [PortfolioSections.CustomTable4] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.CustomTable4, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI1] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI1, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI2] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI2, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI3] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI3, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI4] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI4, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI5] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI5, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI6] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI6, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI7] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI7, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI8] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI8, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI9] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI9, companyId, portfolioResponse, section),
                [PortfolioSections.OtherKPI10] = (section, companyId, portfolioResponse) => AddKpiValuesToPortfolioResponse(kpiValuesByCategory.OtherKPI10, companyId, portfolioResponse, section)
            };

            List<string> staticDatas = new() { PortfolioSections.StaticInformation, PortfolioSections.GeographicLocations, PortfolioSections.InvestmentProfessionals, PortfolioSections.FootNotes };

            foreach (var companyId in filter.CompanyId)
            {
                var accesses = filter.AllowedCompanies?.Where(x => x.Key == companyId).SelectMany(g => g).ToList();
                ExpandoObject expandObject = new();
                var portfolioResponse = expandObject as IDictionary<string, object>;
                portfolioResponse[Constants.PortfolioCompanyId] = companyId;

                foreach (var section in filter.Sections)
                {
                    if (accesses == null || accesses?.Count() == 0)
                    {
                        if (sectionActions.TryGetValue(section, out var action))
                        {
                            action(section, companyId, portfolioResponse);
                        }
                    }
                    else
                    {
                        var access = accesses?.Where(x => x.PageConfigName == section);
                        var staticAccess = accesses?.Where(x => x.PageConfigName == Constants.StaticInformation);
                        if (access?.Count() > 0 && access.Any(x => x.CanView) && sectionActions.TryGetValue(section, out var action))
                        {
                            action(section, companyId, portfolioResponse);
                        }
                        else if (staticAccess.Count() > 0 && staticAccess.Any(x => x.CanView) && staticDatas.Contains(section) && sectionActions.TryGetValue(section, out var staticAction))
                        {
                            staticAction(section, companyId, portfolioResponse);
                        }
                    }
                }

                finalResponse.Add(expandObject);
            }
        }

        /// <summary>
        /// This method gets the static details of the portfolio company.
        /// </summary>
        /// <param name="portfolioCompanyDetails">The list of portfolio company details.</param>
        /// <param name="companyId">The ID of the portfolio company.</param>
        /// <param name="portfolioResponse">The response object.</param>
        /// <param name="x">The section name.</param>
        /// <param name="subPageFieldModels">The list of active fields.</param>
        /// <returns></returns>
        private void PortfolioStaticDetails(DataAnalyticStaticModel staticModel, int companyId, IDictionary<string, object> portfolioResponse, string section)
        {
            var companyDetails = staticModel.PortfolioCompanyDetails.Where(x => x.PortfolioCompanyId == companyId).ToList();
            var subPageFields = staticModel.SubPageFieldModels.Where(x => x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation).ToList();
            var customField = staticModel.PortfolioPageFieldValueModels.Where(x => x.PageFeatureId == companyId).ToList();
            var customListDetails = staticModel.PortfolioCompanyCustomList.Where(s => s.FeatureId == companyId).ToList();
            List<ExpandoObject> portfolioDetails = new();
            companyDetails.ForEach(item =>
            {
                ExpandoObject portfolioKeyValuePairs = new();
                ExpandoObjectExtensions.AddProperty(portfolioKeyValuePairs, Constants.PortfolioCompanyId, item.PortfolioCompanyId);
                subPageFields.ForEach(field => PcAnalyticsHelper.PcStaticDetails(item, field, portfolioKeyValuePairs, customField, customListDetails));
                portfolioDetails.Add(portfolioKeyValuePairs);
            });
            portfolioResponse[section] = portfolioDetails;
        }
        public void CommentaryData(DataAnalyticStaticModel staticModel, int companyId, IDictionary<string, object> portfolioResponse, string section)
        {
            List<ExpandoObject> commentaryDetails = new();
            var commentaryList = staticModel.CommentaryDetails.Where(X => X.PortfolioCompanyID == companyId)
                .Select(x => new CommentaryDetails
                {
                    AssessmentSection = PcAnalyticsHelper.RemoveHtmlTags(x.AssessmentSection),
                    SignificantEventsSection = PcAnalyticsHelper.RemoveHtmlTags(x.SignificantEventsSection),
                    ExitPlansSection = PcAnalyticsHelper.RemoveHtmlTags(x.ExitPlansSection),
                    ImpactSection = PcAnalyticsHelper.RemoveHtmlTags(x.ImpactSection),
                    Quarter = x.Quarter,
                    Year = x.Year,
                    Period = x.Period,
                    Month = x.Month

                }).ToList();
            var commentaryCustomList = staticModel.PageConfigCustomCommentary.Where(s => s.PageFeatureEntityId == companyId).Select(x => new PageConfigurationCommentaryCustomFieldValue
            {
                FieldValue = PcAnalyticsHelper.RemoveHtmlTags(x.FieldValue),
                FieldID = x.FieldID,
                Quarter = x.Quarter,
                Year = x.Year,
                Period = x.Period,
                Month = x.Month
            }).ToList();
            var subPageFields = staticModel.SubPageFieldModels.Where(s => s.SubPageID == (int)PageConfigurationSubFeature.Commentary);
            foreach (var item in commentaryList)
            {
                var keyValuePairs = PcAnalyticsHelper.CreateCommentaryCustom(item, subPageFields, commentaryCustomList);
                commentaryDetails.Add(keyValuePairs);
            }
            PcAnalyticsHelper.AddCustomCommentaryNonMatchingItems(commentaryDetails, commentaryList, commentaryCustomList, subPageFields);
            portfolioResponse[section] = commentaryDetails;
        }
        /// <summary>
        /// This method gets the custom list details for the portfolio company with the specified ID.
        /// </summary>
        /// <param name="companyId">The ID of the portfolio company.</param>
        /// <returns>The list of custom list details.</returns>
        public async Task<List<PortfolioCompanyCustomListDetails>> GetPortfolioCompanyCustomTypeListDetails(string companyIds)
        {
            List<PortfolioCompanyCustomListDetails> result = await _dapperGenericRepository.Query<PortfolioCompanyCustomListDetails>(SqlConstants.QueryByGetPortfolioCustomList, new { companyIds });
            return result.OrderBy(detail => detail.FieldId).ThenBy(detail => detail.DisplayOrder).ToList();
        }

        /// <summary>
        /// This method gets the static field values for the specified page and entity.
        /// </summary>
        /// <param name="pageID">The ID of the page.</param>
        /// <param name="entityID">The ID of the entity.</param>
        /// <returns>The list of static field values.</returns>
        public List<PageFieldValueModel> GetPageConfigStaticFieldValues(int pageID, int entityID)
        {
            var configurations = _unitOfWork.PageConfigurationFieldValueRepository.GetManyQueryable(x => x.PageID == pageID && x.PageFeatureId == entityID);
            return configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<PageFieldValueModel>(x)).ToList();
        }

        /// <summary>
        /// Retrieves a list of <see cref="SubPageFieldModel"/> objects based on the provided <see cref="DataAnalyticsBiFilter"/>.
        /// </summary>
        /// <param name="filter">The filter used to retrieve the page configuration static fields.</param>
        /// <returns>A list of <see cref="SubPageFieldModel"/> objects.</returns>
        public async Task<List<SubPageFieldModel>> PageConfigStaticFields(DataAnalyticsBiFilter filter)
        {
            List<SubPageFieldModel> pageFieldModels = new();
            if (filter?.DataAnalyticsKpiFilter?.PageConfigFields.Count > 0)
            {
                List<int> distinctSubPageIds = filter.DataAnalyticsKpiFilter.PageConfigFields.Select(x => x.SubPageId).Distinct().ToList();
                var selectedSubPageFieldModels = await GetPageConfigActiveFieldsBySubPageId(distinctSubPageIds);
                List<PageConfigFields> PageConfigFields = filter.DataAnalyticsKpiFilter.PageConfigFields;
                pageFieldModels = (from x in selectedSubPageFieldModels
                                   join y in PageConfigFields
                                   on new { X1 = x.SubPageID, X2 = x.Id } equals new { X1 = y.SubPageId, X2 = y.FieldId }
                                   select _mapper.Map<SubPageFieldModel>(x)).ToList();
            }
            return pageFieldModels;
        }
        public async Task<List<DataAnalyticsKpiValues>> GetKpiValuesByModuleAndCompany(int moduleId, string companyId)
        {
            return await _dapperGenericRepository.Query<DataAnalyticsKpiValues>(SqlConstants.QueryGetPluginKPIsValues, new { @ModuleId = moduleId, @CompanyId = companyId });
        }
        public async Task<KpiValuesByCategory> GetKpiValuesByCategory(DataAnalyticsPcFilter filter)
        {
            string companyIds = string.Join(",", filter.CompanyId.Select(n => n.ToString()).ToArray());
            return new KpiValuesByCategory
            {
                Investment = await GetKpiValuesIfSectionExists(PortfolioSections.InvestmentKPIs, KpiModuleType.Investment, filter.Sections, companyIds),
                Company = await GetKpiValuesIfSectionExists(PortfolioSections.CompanyKPIs, KpiModuleType.Company, filter.Sections, companyIds),
                Operational = await GetKpiValuesIfSectionExists(PortfolioSections.OperationalKPIs, KpiModuleType.Operational, filter.Sections, companyIds),
                Impact = await GetKpiValuesIfSectionExists(PortfolioSections.ImpactKPIs, KpiModuleType.Impact, filter.Sections, companyIds),
                Trading = await GetKpiValuesIfSectionExists(PortfolioSections.TradingRecords, KpiModuleType.TradingRecords, filter.Sections, companyIds),
                Credit = await GetKpiValuesIfSectionExists(PortfolioSections.CreditKPI, KpiModuleType.CreditKPI, filter.Sections, companyIds),
                ProfitAndLoss = await GetKpiValuesIfSectionExists(PortfolioSections.ProfitLoss, KpiModuleType.ProfitAndLoss, filter.Sections, companyIds),
                BalanceSheet = await GetKpiValuesIfSectionExists(PortfolioSections.BalanceSheet, KpiModuleType.BalanceSheet, filter.Sections, companyIds),
                CashFlow = await GetKpiValuesIfSectionExists(PortfolioSections.Cashflow, KpiModuleType.CashFlow, filter.Sections, companyIds),
                CustomTable1 = await GetKpiValuesIfSectionExists(PortfolioSections.CustomTable1, KpiModuleType.CustomTable1, filter.Sections, companyIds),
                CustomTable2 = await GetKpiValuesIfSectionExists(PortfolioSections.CustomTable2, KpiModuleType.CustomTable2, filter.Sections, companyIds),
                CustomTable3 = await GetKpiValuesIfSectionExists(PortfolioSections.CustomTable3, KpiModuleType.CustomTable3, filter.Sections, companyIds),
                CustomTable4 = await GetKpiValuesIfSectionExists(PortfolioSections.CustomTable4, KpiModuleType.CustomTable4, filter.Sections, companyIds),
                OtherKPI1 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI1, KpiModuleType.OtherKPI1, filter.Sections, companyIds),
                OtherKPI2 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI2, KpiModuleType.OtherKPI2, filter.Sections, companyIds),
                OtherKPI3 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI3, KpiModuleType.OtherKPI3, filter.Sections, companyIds),
                OtherKPI4 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI4, KpiModuleType.OtherKPI4, filter.Sections, companyIds),
                OtherKPI5 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI5, KpiModuleType.OtherKPI5, filter.Sections, companyIds),
                OtherKPI6 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI6, KpiModuleType.OtherKPI6, filter.Sections, companyIds),
                OtherKPI7 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI7, KpiModuleType.OtherKPI7, filter.Sections, companyIds),
                OtherKPI8 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI8, KpiModuleType.OtherKPI8, filter.Sections, companyIds),
                OtherKPI9 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI9, KpiModuleType.OtherKPI9, filter.Sections, companyIds),
                OtherKPI10 = await GetKpiValuesIfSectionExists(PortfolioSections.OtherKPI10, KpiModuleType.OtherKPI10, filter.Sections, companyIds)
            };
        }
        private async Task<List<DataAnalyticsKpiValues>> GetKpiValuesIfSectionExists(string sectionName, KpiModuleType moduleType, IEnumerable<string> sections, string companyIds)
        {
            return sections.Any(x => x == sectionName) ? await GetKpiValuesByModuleAndCompany((int)moduleType, companyIds) : new List<DataAnalyticsKpiValues>();
        }
        private void AddKpiValuesToPortfolioResponse(List<DataAnalyticsKpiValues> kpiValues, int companyId, IDictionary<string, object> portfolioResponse, string section)
        {
            portfolioResponse[section] = kpiValues.Where(x => x.PortfolioCompanyId == companyId && !string.IsNullOrEmpty(x.PeriodType)).ToList().KpiValuesToExpandoObjectList();
        }
        public async Task<List<CommentaryDetails>> GetCommentaryDetailsByCompany(string companyIds)
        {
            return await _dapperGenericRepository.Query<CommentaryDetails>(SqlConstants.QueryGetCommentary, new { companyIds });
        }
        public async Task<List<PageConfigurationCommentaryCustomFieldValue>> GetCustomCommentaryDetailsByCompany(string companyIds)
        {
            return await _dapperGenericRepository.Query<PageConfigurationCommentaryCustomFieldValue>(SqlConstants.QueryGetCustomCommentary, new { companyIds });
        }
        public List<PageFieldValueModel> FetchPageConfigStaticFieldValues(int pageID, List<int> entityID)
        {
            var configurations = _unitOfWork.PageConfigurationFieldValueRepository.GetManyQueryable(x => x.PageID == pageID && entityID.Contains(x.PageFeatureId)).AsNoTracking().OrderBy(x => x.SubPageID);
            return configurations.Select(x => _mapper.Map<PageFieldValueModel>(x)).ToList();
        }
        public async Task<DataAnalyticStaticModel> GetStaticDataAnalyticsModel(DataAnalyticsPcFilter filter)
        {
            string companyIds = string.Join(",", filter.CompanyId.Select(n => n.ToString()).ToArray());
            bool SectionExists(string section) => filter.Sections.Exists(x => x == section);
            return new DataAnalyticStaticModel
            {
                FootNoteModels = SectionExists(PortfolioSections.FootNotes) ? await PortfolioFootNotes() : new(),
                InvestmentProfessionals = SectionExists(PortfolioSections.InvestmentProfessionals) ? PortfolioInvestmentProfessionals() : new(),
                GeographicLocation = SectionExists(PortfolioSections.GeographicLocations) ? PortfolioGeographicLocation() : new(),
                CommentaryDetails = SectionExists(PortfolioSections.Commentary) ? await GetCommentaryDetailsByCompany(companyIds) : new(),
                PageConfigCustomCommentary = SectionExists(PortfolioSections.Commentary) ? await GetCustomCommentaryDetailsByCompany(companyIds) : new(),
                PortfolioCompanyCustomList = SectionExists(PortfolioSections.StaticInformation) ? await GetPortfolioCompanyCustomTypeListDetails(companyIds) : new(),
                PortfolioPageFieldValueModels = SectionExists(PortfolioSections.StaticInformation) ? FetchPageConfigStaticFieldValues((int)PageConfigurationFeature.PortfolioCompany, filter.CompanyId) : new(),
            };
        }
        /// <summary>
        /// Retrieves matching section names and their corresponding module IDs.
        /// </summary>
        /// <param name="sectionNamesToMatch">A list of section names to match against the available sections.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of SectionModule objects with matching section names and module IDs.</returns>
        /// <remarks>
        /// This method performs the following steps:
        /// 1. Asynchronously retrieves the ESG section sub-page details.
        /// 2. Adds default section names to the list of section names to match.
        /// 3. Initializes a dictionary to map section names to module IDs.
        /// 4. Populates the dictionary with section names and their corresponding module IDs from the retrieved page details.
        /// 5. Filters the section names to match against the keys in the dictionary.
        /// 6. Creates a list of SectionModule objects for the matching section names and their module IDs.
        /// </remarks>
        public async Task<List<SectionModule>> GetMatchingSectionNamesAndModuleIds(List<string> sectionNamesToMatch)
        {
            List<M_SubPageDetails> pageDetails = await GetEsgSectionSubPageDetails();
            AddDefaultSectionNames(sectionNamesToMatch, pageDetails);
            var SectionModuleMap = SectionModuleMapList() ?? new Dictionary<string, int>();
            pageDetails.ForEach(detail => { SectionModuleMap[detail.Name] = detail.SubPageID; });
            return sectionNamesToMatch
                .Where(SectionModuleMap.ContainsKey)
                .Select(sectionName => new SectionModule
                {
                    SectionName = sectionName,
                    ModuleId = SectionModuleMap[sectionName]
                })
                .ToList();
        }
        /// <summary>
        /// Adds default section names to the list of section names to match if the list is empty.
        /// </summary>
        /// <param name="sectionNamesToMatch">The list of section names to match against the available sections.</param>
        /// <param name="pageDetails">The list of sub-page details containing section names.</param>
        /// <remarks>
        /// If the list of section names to match is empty, this method adds all keys from the SectionModuleMapList and all section names from the provided page details to the list.
        /// </remarks>
        private void AddDefaultSectionNames(List<string> sectionNamesToMatch, List<M_SubPageDetails> pageDetails)
        {
            if (sectionNamesToMatch.Count == 0)
            {
                sectionNamesToMatch.AddRange(SectionModuleMapList().Keys);
                sectionNamesToMatch.AddRange(pageDetails.Select(x => x.Name));
            }
        }
        private async Task<List<M_SubPageDetails>> GetEsgSectionSubPageDetails()
        {
            return await _unitOfWork.SubPageDetailsRepository.FindAllAsync(x => x.IsActive && !x.IsDeleted && x.PageID == (int)PageConfigurationFeature.ESG);
        }
        /// <summary>
        /// Retrieves a dictionary of KPI masters grouped by section names based on the provided filter.
        /// </summary>
        /// <param name="pcFilter">The filter containing the sections to match against the available sections.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a dictionary where the keys are section names and the values are lists of KPI masters.</returns>
        /// <remarks>
        /// This method retrieves matching section names and their corresponding module IDs, queries the database to get all KPI masters by the module IDs, groups the KPI masters by their module IDs, and maps the grouped KPI masters to their corresponding section names.
        /// </remarks>
        public async Task<Dictionary<string, List<KpiMaster>>> GetMasterKpiList(DataAnalyticsPcFilter pcFilter)
        {
            List<SectionModule> sections = await GetMatchingSectionNamesAndModuleIds(pcFilter.Sections);
            var parameters = new DynamicParameters();
            parameters.Add("@ModuleIds", string.Join(",", sections.Select(n => n.ModuleId.ToString()).ToArray()));
            List<KpiMaster> allKpiList = await _dapperGenericRepository.Query<KpiMaster>(SqlConstants.QueryGetAllKPIsByModuleIds, parameters);
            var groupedKPIs = allKpiList.GroupBy(kpi => new { kpi.ModuleId })
                                 .Select(g => new GroupedKpiModel
                                 {
                                     ModuleId = g.Key.ModuleId,
                                     KpiMaster = [.. g]
                                 }).ToList();
            var kpiCategories = new Dictionary<string, List<KpiMaster>>();
            foreach (var group in groupedKPIs)
            {
                var sectionName = sections.Find(x => x.ModuleId == group.ModuleId)?.SectionName;
                if (sectionName != null)
                {
                    kpiCategories[sectionName] = group.KpiMaster.OrderBy(item => item.KPI).ToList();
                }
            }
            return kpiCategories;
        }
        /// <summary>
        /// Retrieves a dictionary mapping section names to their corresponding module IDs.
        /// </summary>
        /// <returns>A dictionary where the keys are section names and the values are module IDs.</returns>
        /// <remarks>
        /// This method returns a dictionary that maps predefined section names to their corresponding module IDs.
        /// </remarks>
        private Dictionary<string, int> SectionModuleMapList()
        {
            return new()
            {
                { PortfolioSections.BalanceSheet, (int)KpiModuleType.BalanceSheet },
                { PortfolioSections.Cashflow, (int)KpiModuleType.CashFlow },
                { PortfolioSections.CompanyKPIs, (int)KpiModuleType.Company },
                { PortfolioSections.CreditKPI, (int)KpiModuleType.CreditKPI },
                { PortfolioSections.ImpactKPIs, (int)KpiModuleType.Impact },
                { PortfolioSections.InvestmentKPIs, (int)KpiModuleType.Investment },
                { PortfolioSections.OperationalKPIs, (int)KpiModuleType.Operational },
                { PortfolioSections.ProfitLoss, (int)KpiModuleType.ProfitAndLoss },
                { PortfolioSections.TradingRecords, (int)KpiModuleType.TradingRecords },
                { Constants.CapTable1, (int)KpiModuleType.CapTable1 },
                { Constants.CapTable2, (int)KpiModuleType.CapTable2 },
                { Constants.CapTable3, (int)KpiModuleType.CapTable3 },
                { Constants.CapTable4, (int)KpiModuleType.CapTable4 },
                { Constants.CapTable5, (int)KpiModuleType.CapTable5 },
                { Constants.CustomTable1, (int)KpiModuleType.CustomTable1 },
                { Constants.CustomTable2, (int)KpiModuleType.CustomTable2 },
                { Constants.CustomTable3, (int)KpiModuleType.CustomTable3 },
                { Constants.CustomTable4, (int)KpiModuleType.CustomTable4 },
                { Constants.OtherKPI1, (int)KpiModuleType.OtherKPI1 },
                { Constants.OtherKPI2, (int)KpiModuleType.OtherKPI2 },
                { Constants.OtherKPI3, (int)KpiModuleType.OtherKPI3 },
                { Constants.OtherKPI4, (int)KpiModuleType.OtherKPI4 },
                { Constants.OtherKPI5, (int)KpiModuleType.OtherKPI5 },
                { Constants.OtherKPI6, (int)KpiModuleType.OtherKPI6 },
                { Constants.OtherKPI7, (int)KpiModuleType.OtherKPI7 },
                { Constants.OtherKPI8, (int)KpiModuleType.OtherKPI8 },
                { Constants.OtherKPI9, (int)KpiModuleType.OtherKPI9 },
                { Constants.OtherKPI10, (int)KpiModuleType.OtherKPI10},
            };
        }
        /// <summary>
        /// Retrieves a list of KPI responses based on the provided filter criteria.
        /// </summary>
        /// <param name="pcFilter">The filter criteria containing sections and company IDs.</param>
        /// <returns>A list of KPI responses grouped by company and module.</returns>
        public async Task<List<KpiResponse>> GetMappingKpiList(DataAnalyticsPcFilter pcFilter)
        {
            List<SectionModule> sections = await GetMatchingSectionNamesAndModuleIds(pcFilter.Sections);
            var parameters = CreateDynamicParameters(sections, pcFilter);
            List<KpiMaster> allKpiList = await QueryKpiList(parameters);
            var groupedKPIs = GroupKpiByCompanyAndModule(allKpiList);
            var kpiResponses = CreateKpiResponses(groupedKPIs, sections);
            PopulateKpiResponses(kpiResponses, groupedKPIs, sections);
            return kpiResponses;
        }
        /// <summary>
        /// Retrieves a list of companies that the user has access to view.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <returns>A list of companies that the user has access to view.</returns>
        public async Task<List<CompanyModel>> GetCompaniesByAccess(int userId)
        {
            _logger.LogInformation("GetCompaniesByAccess started for userId: {UserId}", userId);
            try
            {
                List<CompanyModel> companiesList = await _dapperGenericRepository.Query<CompanyModel>(SqlConstants.QueryByCompanyList, new { });
                if (userId == 0)
                {
                    _logger.LogInformation("Returning all companies for userId: {UserId}", userId);
                    return [.. companiesList.Distinct()];
                }

                var accesses = await _dapperGenericRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.ProcGetSubFeaturePermissions, new { @FeatureId = (int)FeatureTypes.PortfolioCompany, @UserId = userId, @Id = 0 });
                if (companiesList != null && accesses != null)
                {
                    _logger.LogInformation("Returning restricted companies for userId: {UserId}", userId);
                    return PcAnalyticsHelper.GetRestrictedCompanies(companiesList.Distinct().ToList(), accesses);
                }

                _logger.LogInformation("No companies or accesses found for userId: {UserId}", userId);
                return [];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while getting companies by access for userId: {UserId}", userId);
                return [];
            }
        }

        /// <summary>
        /// Creates dynamic parameters for the KPI query based on the sections and filter criteria.
        /// </summary>
        /// <param name="sections">The list of section modules containing module IDs.</param>
        /// <param name="pcFilter">The filter criteria containing company IDs.</param>
        /// <returns>A DynamicParameters object containing the module IDs and company IDs.</returns>
        private DynamicParameters CreateDynamicParameters(List<SectionModule> sections, DataAnalyticsPcFilter pcFilter)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@ModuleIds", string.Join(",", sections.Select(n => n.ModuleId.ToString()).ToArray()));
            parameters.Add("@CompanyIds", pcFilter.CompanyId != null ? string.Join(",", pcFilter.CompanyId) : null);
            return parameters;
        }

        private async Task<List<KpiMaster>> QueryKpiList(DynamicParameters parameters)
        {
            return await _dapperGenericRepository.Query<KpiMaster>(SqlConstants.QueryGetAllMappingKpiByModuleIds, parameters);
        }
        /// <summary>
        /// Groups the KPI list by company and module.
        /// </summary>
        /// <param name="allKpiList">The list of all KPIs to be grouped.</param>
        /// <returns>A list of grouped KPIs by company and module.</returns>
        private List<CompanyKpiGroup> GroupKpiByCompanyAndModule(List<KpiMaster> allKpiList)
        {
            return allKpiList.GroupBy(kpi => new { kpi.PortfolioCompanyId, kpi.CompanyName })
                             .Select(g => new CompanyKpiGroup
                             {
                                 PortfolioCompanyId = g.Key.PortfolioCompanyId,
                                 CompanyName = g.Key.CompanyName,
                                 ModuleGroups = g.GroupBy(kpi => kpi.ModuleId)
                                                 .Select(mg => new ModuleKpiGroup
                                                 {
                                                     ModuleId = mg.Key,
                                                     KpiMaster = mg.ToList()
                                                 }).ToList()
                             }).ToList();
        }
        /// <summary>
        /// Creates the initial list of KPI responses based on the grouped KPIs and sections.
        /// </summary>
        /// <param name="groupedKPIs">The list of grouped KPIs by company and module.</param>
        /// <param name="sections">The list of section modules containing section names and module IDs.</param>
        /// <returns>A list of KPI responses with initialized KPI dictionaries.</returns>
        private List<KpiResponse> CreateKpiResponses(List<CompanyKpiGroup> groupedKPIs, List<SectionModule> sections)
        {
            return groupedKPIs.Select(g => new KpiResponse
            {
                CompanyId = g.PortfolioCompanyId,
                CompanyName = g.CompanyName,
                Kpi = sections.Select(x => x.SectionName).Distinct().ToDictionary(type => type, type => new List<KpiMaster>())
            }).OrderBy(response => response.CompanyId).ToList();
        }
        /// <summary>
        /// Populates the KPI responses with the actual KPI data from the grouped KPIs.
        /// </summary>
        /// <param name="kpiResponses">The list of KPI responses to be populated.</param>
        /// <param name="groupedKPIs">The list of grouped KPIs by company and module.</param>
        /// <param name="sections">The list of section modules containing section names and module IDs.</param>
        private void PopulateKpiResponses(List<KpiResponse> kpiResponses, List<CompanyKpiGroup> groupedKPIs, List<SectionModule> sections)
        {
            foreach (var kpiResponse in kpiResponses)
            {
                var companyMappingKpi = groupedKPIs.First(g => g.PortfolioCompanyId == kpiResponse.CompanyId);
                foreach (var moduleGroup in companyMappingKpi.ModuleGroups)
                {
                    var sectionName = sections.Find(x => x.ModuleId == moduleGroup.ModuleId)?.SectionName;
                    if (sectionName != null && kpiResponse.Kpi.ContainsKey(sectionName))
                    {
                        kpiResponse.Kpi[sectionName].AddRange(moduleGroup.KpiMaster.OrderBy(x => x.DisplayOrder));
                    }
                }
            }
        }
        /// <summary>
        /// GetFxRates
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<List<FxRateModel>> GetFxRates(FxRateFilterModel filter)
        {
            return await _dapperGenericRepository.Query<FxRateModel>(SqlConstants.QueryByGetFxRate, new { filter.FromDate, filter.ToDate, filter.FilterSource, filter.FromCurrencyCode, filter.ToCurrencyCode });
        }
    }
}