using AutoMapper;
using Contract.Configuration;
using Contract.Employee;
using Contract.Funds;
using Contract.Pdf;
using DataAccessLayer.DBModel;
using Report.AutoMapper;
using Report.Enums;
using Shared;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utility.Helpers;
namespace Report.LpReport.Helpers
{
	/// <summary>
	/// Static helper class for processing LP Report data
	/// </summary>
	public static class LpDataProcessor
	{
		private static readonly IMapper _mapper = new MapperConfiguration(cfg => cfg.AddProfile<LpReportMappingProfile>()).CreateMapper();

		/// <summary>
		/// Processes company details and maps them to portfolio page config models
		/// </summary>
		/// <typeparam name="T">Type of company details</typeparam>
		/// <param name="companyDetails">List of company details</param>
		/// <param name="pageConfig">Page configuration</param>
		/// <param name="subFeatureIds">List of sub-feature IDs</param>
		/// <param name="customFields">Optional list of custom fields</param>
		/// <param name="companyCustomListDetails">Optional list of company custom list details</param>
		/// <returns>List of processed portfolio page config models</returns>
		public static List<PortfolioPageConfigModel> ProcessCompanyDetails<T>(List<T> companyDetails, PageConfigResult pageConfig, List<int> subFeatureIds, List<PageFieldValueModel> customFields = null, List<PortfolioCompanyCustomListDetails> companyCustomListDetails = null)
		{
			var portfolioModel = new List<PortfolioPageConfigModel>();
			companyDetails.ForEach(result =>
			{
				var fieldValueMap = GetFieldValueMap(result);
				var pageConfigFields = pageConfig.PageConfigMappedFields.Where(x => subFeatureIds.Contains(x.SubPageID))
					.Select(item =>
					{
						var newItem = new PageFieldValueModel
						{
							FieldID = item.FieldID,
							Name = item.Name,
							DisplayName = item.DisplayName,
							SubPageID = item.SubPageID,
							Value = item.Value,
							DataTypeId = item.DataTypeId,
							Sequence = item.Sequence,
						};
						if (fieldValueMap.TryGetValue(newItem.Name, out var valueFunc))
						{
							newItem.Value = valueFunc(result);
						}
						return newItem;
					})
					.ToList();
				var configModel = new PortfolioPageConfigModel
				{
					CompanyId = GetCompanyId(result),
					PageConfigMappedFields = pageConfigFields
				};
				portfolioModel.Add(configModel);
			});

			return portfolioModel;
		}

		/// <summary>
		/// Gets field value map based on the type of input data
		/// </summary>
		/// <typeparam name="T">Type of company details model</typeparam>
		/// <param name="result">Input data object</param>
		/// <returns>Dictionary mapping field names to value extraction functions</returns>
		private static Dictionary<string, Func<T, string>> GetFieldValueMap<T>(T result)
		{
			if (result is LpGeoLocationDetails geoLocationDetails)
			{
				return new Dictionary<string, Func<T, string>>{
					{ Constants.Region, r => (r as LpGeoLocationDetails).Region ?? Constants.NotAvailable },
					{ Constants.Country, r => (r as LpGeoLocationDetails).Country ?? Constants.NotAvailable },
					{ Constants.State, r => (r as LpGeoLocationDetails).State ?? Constants.NotAvailable },
					{ Constants.City, r => (r as LpGeoLocationDetails).City?? Constants.NotAvailable }
				};
			}
			else if (result is MappingEmployeeModel employeeDetails)
			{
				return new Dictionary<string, Func<T, string>>{
					{ Constants.Email, r => (r as MappingEmployeeModel).EmailId ?? Constants.NotAvailable },
					{ Constants.EmployeeName, r => (r as MappingEmployeeModel).EmployeeName ?? Constants.NotAvailable },
					{ Constants.Designation, r => (r as MappingEmployeeModel).Designation ?? Constants.NotAvailable },
					{ Constants.Education, r => (r as MappingEmployeeModel).Education?? Constants.NotAvailable },
					{ Constants.PastExperience, r => (r as MappingEmployeeModel).PastExperience?? Constants.NotAvailable }
				 };
			}
			return new Dictionary<string, Func<T, string>>();
		}

		/// <summary>
		/// Retrieves the company ID from various types of company detail objects
		/// </summary>
		/// <typeparam name="T">Type of company details model</typeparam>
		/// <param name="result">Input company detail object</param>
		/// <returns>Portfolio company ID, or 0 if not found</returns>
		private static int GetCompanyId<T>(T result)
		{
			if (result is LpGeoLocationDetails geoLocationDetails)
			{
				return geoLocationDetails.PortfolioCompanyId;
			}
			else if (result is MappingEmployeeModel employeeDetails)
			{
				return employeeDetails.PortfolioCompanyId;
			}
			return 0;
		}

		/// <summary>
		/// Processes static company details and maps them to portfolio page config models
		/// </summary>
		/// <param name="companyDetails">List of company details</param>
		/// <param name="subFeatureIds">List of sub-feature IDs</param>
		/// <param name="pageConfig">Page configuration</param>
		/// <param name="customFields">List of custom fields</param>
		/// <param name="companyCustomListDetails">List of company custom list details</param>
		/// <param name="files">List of files</param>
		/// <param name="portfolioModel">List of portfolio page config models</param>
		public static void ProcessCompanyStaticDetails(List<LpReportCompanyDetails> companyDetails, List<int> subFeatureIds, PageConfigResult pageConfig, List<PageFieldValueModel> customFields, List<PortfolioCompanyCustomListDetails> companyCustomListDetails, List<FolderInfo> files, List<PortfolioPageConfigModel> portfolioModel,string clientCode)
		{
			companyDetails.ForEach(result =>
			{
				result.CompanyLogo = ImageResizer.Resize(files.Find(x => x.Id == result.PortfolioCompanyId && x.Name == result.CompanyLogo)?.ImageBase64 ?? Constants.NotAvailable, 150, 100);
				var fieldValueMap = new Dictionary<string, Func<LpReportCompanyDetails, string>>{
					{ Constants.CompanyName, result => result.CompanyName ?? Constants.NotAvailable },
					{ Constants.BusinessDescription, result => result.BussinessDescription ?? Constants.NotAvailable },
					{ Constants.CompanyStatus, result => result.Status ?? Constants.NotAvailable },
					{ Constants.FinancialYearEnd, result => result.FinancialYearEnd?? Constants.NotAvailable },
					{ Constants.StockExchange_Ticker, result => result.StockExchangeTicker ?? Constants.NotAvailable },
					{ Constants.Currency, result => result.CurrencyCode ?? Constants.NotAvailable },
					{ Constants.Website, result => result.Website ?? Constants.NotAvailable },
					{ Constants.MasterCompanyName, result => result.MasterCompanyName ?? Constants.NotAvailable },
					{ Constants.CompanyGroupId, result => result.CompanyGroupId == null ? Constants.NotAvailable : result.MasterCompanyName },
					{ Constants.Sector, result => result.Sector?? Constants.NotAvailable },
					{ Constants.SubSector, result => result.SubSector ?? Constants.NotAvailable },
					{ Constants.FundId, result => result.FundName ?? Constants.NotAvailable },
					{ Constants.DealId, result => result.DealCustomId ?? Constants.NotAvailable },
					{ Constants.CompanyLogo, result => result.CompanyLogo ?? Constants.NotAvailable },
					{ Constants.PcInvestmentDate, result => result.PCInvestmentDate.ConvertToMonthYear()?? Constants.NotAvailable },
					{ Constants.CompanyLegalName, result => result.CompanyLegalName ?? Constants.NotAvailable },
				};		
				string status = result.Status ?? Constants.NotAvailable;
				bool showSharesOwnedField = status.ToLower() == HtmlConstants.CompanyPublicStatus;
                var pageConfigFields = pageConfig.PageConfigMappedFields
                    .Where(x => subFeatureIds.Contains(x.SubPageID))
                    // Filter out "Number Of Shares Owned" field for non-public companies
                    .Where(x => (clientCode != HtmlConstants.ClientCodeDev && clientCode!=HtmlConstants.ClientCodeTest && clientCode!=HtmlConstants.ClientCodeUat && clientCode!=HtmlConstants.ClientCodeHimera) || showSharesOwnedField || x.DisplayName != "Number Of Shares Owned")
					.Select(item =>
					{

						var newItem = new PageFieldValueModel
						{
							FieldID = item.FieldID,
							Name = item.Name,
							DisplayName = item.DisplayName,
							SubPageID = item.SubPageID,
							Value = item.Value,
							DataTypeId = item.DataTypeId,
							Sequence = item.Sequence,
						};
						if (fieldValueMap.TryGetValue(newItem.Name, out var valueFunc))
						{
							newItem.Value = valueFunc(result);
						}
						var target = customFields.Find(x => x.PageFeatureId==result.PortfolioCompanyId && x.FieldID == newItem.FieldID && x.DataTypeId != (int)PageSubFieldsDatatTypes.List);
						if (target != null)
						{
							newItem.Value = target.Value;
						}
						var customList = companyCustomListDetails.Find(x => x.FeatureId == result.PortfolioCompanyId && x.FieldId == newItem.FieldID);
						if (customList != null)
						{
							newItem.Value = string.Join(",", companyCustomListDetails.Where(s => s.FieldId == newItem.FieldID).Select(x => x.GroupName).ToArray());
						}
						return newItem;
					})
					.ToList();
				var configModel = new PortfolioPageConfigModel { CompanyId = result.PortfolioCompanyId, PageConfigMappedFields = pageConfigFields };
				portfolioModel.Add(configModel);
			});
		}

		/// <summary>
		/// Adds new company details with no data found for the given company IDs
		/// </summary>
		/// <typeparam name="T">Type of company details</typeparam>
		/// <param name="companyDetails">List of company details</param>
		/// <param name="companyIds">List of company IDs</param>
		public static void NoDataFound<T>(List<T> companyDetails, List<int> companyIds) where T : new()
		{
			var newDetails = companyIds.Select(companyId =>
			{
				T details = new T();
				typeof(T).GetProperty(Constants.PcId).SetValue(details, companyId);
				return details;
			}).ToList();
			companyDetails.AddRange(newDetails);
		}

		/// <summary>
		/// Updates KPI values with appropriate formatting based on their type (Currency, Multiple, etc.)
		/// and applies unit conversions as specified in the configuration model
		/// </summary>
		/// <param name="kpiValues">List of KPI values to be formatted</param>
		/// <param name="configModel">Configuration model containing unit type settings</param>
		public static void UpdateAndFormatKpiValues(List<LpReportKpiValuesModel> kpiValues, MappedLpReportConfigModel configModel,bool isStatic=false)
		{
			foreach (var kpi in kpiValues)
			{
				if (kpi.KPIInfo == Constants.KpiInfoCurrency)
				{
					kpi.KPIValue = kpi.KPIValue.ConvertValueByUnit(configModel.UnitType).ToString();
				}
				if (kpi.KPIInfo != Constants.KpiInfoText)
				{
					int decimalPlaces = kpi.KPIInfo.GetDecimalPlaces(isStatic);
					bool isSymbol = true;
					string formattedValue = LpUtilities.FormatValues(kpi.KPIValue, kpi.KPIInfo, decimalPlaces, isSymbol);
					kpi.KPIValue = LpUtilities.FormatNegativeValue(kpi.KPIValue, formattedValue, kpi.KPIInfo);
				}
			}
		}

		/// <summary>
		/// Gets distinct KPI headers from the list of KPI values
		/// </summary>
		/// <param name="kpiValues">List of KPI values</param>
		/// <returns>List of distinct KPI headers</returns>
		public static List<KpiHeader> GetDistinctKpiHeaders(List<LpReportKpiValuesModel> kpiValues)
		{
			var distinctKpiHeaders = new HashSet<string>();
			var kpiHeaders = new List<KpiHeader>();
			var orderMap = LpUtilities.GetOrderMap();
			var orderedItems = OrderKpiValues(kpiValues, orderMap);
			var newKpiHeaders = orderedItems
			.Where(kpi => distinctKpiHeaders.Add(kpi.PeriodType))
			.Select(x => _mapper.Map<KpiHeader>(x));
			kpiHeaders.AddRange(newKpiHeaders);
			return kpiHeaders;
		}

		/// <summary>
		/// Gets mapped periods for commentary from the list of mapped LP report configurations
		/// </summary>
		/// <param name="mappedLpReportConfigs">List of mapped LP report configurations</param>
		/// <returns>HashSet of mapped periods for commentary</returns>
		public static HashSet<string> GetMappedPeriodsForCommentary(List<MappedLpReportConfigModel> mappedLpReportConfigs)
		{
			return mappedLpReportConfigs
				.Where(x => x.SectionId == (int)LpSection.Commentary)
				.Select(x => x.CommentaryPeriod)
				.ToHashSet();
		}

		/// <summary>
		/// Orders KPI values based on a predefined ordering scheme
		/// </summary>
		/// <param name="kpiValues">List of KPI values to be ordered</param>
		/// <param name="orderMap">Dictionary defining the ordering priority for KPI values</param>
		/// <returns>Ordered list of KPI values</returns>
		public static List<LpReportKpiValuesModel> OrderKpiValues(List<LpReportKpiValuesModel> kpiValues, Dictionary<string, int> orderMap)
		{
            var dataTypeOrderMap = new Dictionary<string, int>
			{
			    { "M", 1 },  // Monthly
			    { "Q", 2 },  // Quarterly
			    { "Y", 3 }   // Yearly
			};
            return kpiValues
				.OrderBy(item =>
				{
					if (string.IsNullOrEmpty(item.HeaderValue) || !orderMap.TryGetValue(item.HeaderValue, out int order))
					{
						order = KpiOrderConstants.DefaultOrder;
					}
					return order;
				})
                .ThenBy(item =>
                {
                    if (string.IsNullOrEmpty(item.DataType) || !dataTypeOrderMap.TryGetValue(item.DataType, out int dataTypeOrder))
                    {
                        dataTypeOrder = KpiOrderConstants.DefaultOrder;
                    }
                    return dataTypeOrder;
                })
                .ToList();
		}

		/// <summary>
		/// Creates a new LP commentary model by mapping fields and retrieving custom commentary details
		/// </summary>
		/// <param name="subItem">Page field value model containing basic field information</param>
		/// <param name="item">Configuration model containing commentary mapping details</param>
		/// <param name="companyId">ID of the portfolio company</param>
		/// <param name="customCommentaryDetails">List of custom commentary field values</param>
		/// <returns>Populated LP commentary model</returns>
		public static LpCommentaryModel CreateLpCommentaryModel(PageFieldValueModel subItem, MappedLpReportConfigModel item, int companyId, List<PageConfigurationCommentaryCustomFieldValue> customCommentaryDetails)
		{
            var commentaryFieldValue = customCommentaryDetails?.Find(x => x.FieldID == subItem.FieldID && x.Period == item.CommentaryPeriod && x.PageFeatureEntityId == companyId);
            var newItem = _mapper.Map<LpCommentaryModel>(subItem);
			newItem.CommentarySectionMappingId = item.CommentarySectionMappingId;
			newItem.CompanyId = companyId;
			newItem.Value = commentaryFieldValue?.FieldValue ?? HtmlConstants.NoDataFoundHtml;
            newItem.Quarter = commentaryFieldValue?.Quarter;
            newItem.Year = commentaryFieldValue?.Year ?? 0;
            newItem.Month = commentaryFieldValue?.Month ?? 0;
			newItem.HashData = commentaryFieldValue != null && !string.IsNullOrEmpty(commentaryFieldValue.FieldValue);
            return newItem;
		}

		/// <summary>
		/// Loads an HTML template file from the specified location
		/// </summary>
		/// <param name="rootPath">Root path of the application</param>
		/// <param name="fileName">Name of the template file to load</param>
		/// <returns>Content of the HTML template as a string</returns>
		/// <remarks>
		/// The template files should be located in the PdfTemplates folder under the application root
		/// </remarks>
		public static async Task<string> LoadTemplateHtml(string rootPath, string fileName)
		{
			var templateDirPath = Path.Combine(rootPath.Replace("..", ""), HtmlConstants.PdfTemplatesFolder);
			return await File.ReadAllTextAsync(Path.Combine(templateDirPath, fileName));
		}
		public static void CreateInvestmentKPIColumnSection(LpReportConfigDataModel staticData, int companyId, StringBuilder htmlBuilder, MappedLpReportConfigModel configModel)
		{
			var lpReportKpiValues = KpiUtilities.GetFilteredKpiValues(staticData, companyId, configModel);
			lpReportKpiValues = lpReportKpiValues
        		.Where(x => !string.IsNullOrEmpty(x.KPIValue))
        		.ToList();
			if (!lpReportKpiValues.Any()) return;
			UpdateAndFormatKpiValues(lpReportKpiValues, configModel,true);
			var orderedKpis = lpReportKpiValues.OrderBy(x => x.DisplayOrder).ToList();
			string unitSuffix = LpUtilities.GetUnitSuffix(configModel.UnitType);
			var (leftItems, rightItems) = KpiUtilities.SplitKpiValues(orderedKpis);
			htmlBuilder.AppendKpiColumnRows(leftItems, rightItems,unitSuffix);
            LpHtmlGenerator.CreateFootnoteHtmlDetails(staticData, companyId, htmlBuilder, lpReportKpiValues.FirstOrDefault().ModuleId);
        }

		/// <summary>
		/// Generates the cover HTML content for the LP report.
		/// </summary>
		/// <param name="templateModel">The model containing the data and settings for the PDF generation.</param>
		/// <param name="latestQuarterAndYear">The latest quarter and year to be included in the cover.</param>
		/// <param name="coverPageTitle">The title to be displayed on the cover page.</param>
		/// <returns>A task representing the asynchronous operation, with a result of the generated cover HTML as a string.</returns>
		/// <remarks>
		/// This method loads the cover HTML template, replaces placeholders with actual values, and returns the final cover HTML content.
		/// </remarks>
		public static async Task<string> GenerateCoverHtml(PdfDownloadModel templateModel, string latestQuarterAndYear)
		{
			// Included ClientCodeTest to use the standard cover HTML template for testing purposes.
			string coverName=(templateModel.ClientCode == HtmlConstants.ClientCodeHimera || templateModel.ClientCode==HtmlConstants.ClientCodeTest) ? HtmlConstants.CoverHtmlFileName : HtmlConstants.CoverHtmlOtherFileName;
            var coverHtml = await LoadTemplateHtml(templateModel.RootPath, coverName);
			coverHtml = coverHtml.Replace(HtmlConstants.LogoPlaceholder, templateModel.RootPath.Replace("..", ""));
			coverHtml = coverHtml.Replace(HtmlConstants.CurrentQuarterPlaceholder, latestQuarterAndYear);
			return coverHtml;
		}
        public static List<KpiHeader> GetKpiDistinctKpiHeaders(List<LpReportKpiValuesModel> kpiValues, MappedLpReportConfigModel configModel)
        {
            var kpiHeaders = new HashSet<KpiHeader>(new KpiHeaderComparer());
            List<int> orderData = configModel.Sequence.ConvertCommaSeparatedStringToList();
            var orderedKpiValues = kpiValues
                .OrderBy(kpi => orderData.IndexOf(kpi.Sequence))
                .ThenBy(kpi => kpi.Year)
                .ThenBy(kpi => GetPeriodOrder(kpi))
                .ToList();
            foreach (var kpi in orderedKpiValues)
            {
                kpiHeaders.Add(_mapper.Map<KpiHeader>(kpi));
            }
            return kpiHeaders.ToList();
        }
        public static int GetPeriodOrder(LpReportKpiValuesModel kpi)
        {
            return kpi.DataType?.ToUpper() switch
            {
                Constants.MonthlyType when IsValidMonthly(kpi) => kpi.Month ?? 0,
                Constants.QuarterlyType when IsValidQuarterly(kpi) => int.Parse(kpi.Quarter[1..]),
                Constants.YearlyType when IsValidYearly(kpi) => 0,
                _ => 0
            };
        }

        public static bool IsValidMonthly(LpReportKpiValuesModel kpi) =>
            kpi.Year > 0 && (kpi.Month != 0 || kpi.Month != null) &&
            string.IsNullOrEmpty(kpi.Quarter);

        public static bool IsValidQuarterly(LpReportKpiValuesModel kpi) =>
            kpi.Year > 0 && !string.IsNullOrEmpty(kpi.Quarter) &&
            (kpi.Month == 0 || kpi.Month == null);

        public static bool IsValidYearly(LpReportKpiValuesModel kpi) =>
            kpi.Year > 0 && (kpi.Month == 0 || kpi.Month == null) &&
            string.IsNullOrEmpty(kpi.Quarter);
        public static List<IGrouping<string, KpiHeader>> GetGroupedHeaders(List<KpiHeader> headers)
        {
            var groupedHeaders = headers
                .Where(x => x.HeaderValue != Constants.LTM && x.HeaderValue != Constants.YTD)
                .GroupBy(x => NormalizeHeaderValue(x.HeaderValue))
                .ToList();
            return groupedHeaders;
        }

        public static string NormalizeHeaderValue(string headerValue)
        {
            if (string.IsNullOrWhiteSpace(headerValue))
                return headerValue;
            string normalizedValue = headerValue;
            foreach (var suffix in KpiOrderConstants.NormalizationSuffixes)
            {
                normalizedValue = normalizedValue
                    .Replace($" {suffix}", "")
                    .Replace(suffix, "")
                    .Trim();
            }
            return normalizedValue;
        }
        public static string GetLpCommentaryPeriod(LpCommentaryModel commentaryModel, string period)
        {
            return period switch
            {
                Constants.Monthly => $"{Common.GetMonthName(commentaryModel.Month)}'{commentaryModel.Year}",
                Constants.Quarterly => $"{commentaryModel.Quarter}'{commentaryModel.Year}",
                Constants.Annual => commentaryModel.Year.ToString(),
                _ => period
            };
        }
    }
    public  class KpiHeaderComparer : IEqualityComparer<KpiHeader>
    {
        public bool Equals(KpiHeader x, KpiHeader y)
        {
            if (ReferenceEquals(x, y)) return true;
            if (x is null || y is null) return false;

            return x.HeaderValue == y.HeaderValue &&
                   x.PeriodType == y.PeriodType &&
                   x.DataType == y.DataType &&
                   x.Year == y.Year &&
                   x.Quarter == y.Quarter &&
                   x.Month == y.Month;
        }

        public int GetHashCode(KpiHeader obj)
        {
            if (obj is null) return 0;
            return HashCode.Combine(
                obj.HeaderValue,
                obj.PeriodType,
                obj.DataType,
                obj.Year,
                obj.Quarter,
                obj.Month
            );
        }
    }
}