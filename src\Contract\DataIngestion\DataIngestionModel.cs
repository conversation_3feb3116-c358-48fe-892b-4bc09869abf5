using System;
using System.Collections.Generic;

namespace Contract.DataIngestion
{
    public class KpiList
    {
        public int KpiId { get; set; }
        public string KpiValue { get; set; }
        public string ValueType { get; set; }
        public int ValueTypeId { get; set; }
        public int? Month { get; set; }
        public int Year { get; set; }
        public string Quarter { get; set; }
        public string KPIInfo { get; set; }
        public int ModuleId { get; set; }
        public int MethodologyId { get; set; }
        public int CurrencyId { get; set; }
    }

    public class PublishModel
    {
        public List<KpiList> KpiList { get; set; } = [];
        public int CompanyId { get; set; }
        public int CurrencyId { get; set; }
        public int FundCurrencyId { get; set; }
        public string ConnectionString { get; set; }
        public int UserId { get; set; }
        public bool IsFinancial { get; set; }
        public int ModuleId { get; set; }
        public string UnitCurrency { get; set; }
        public int FundId { get; set; }
        public long ProcessId { get; set; }
    }
    public class FundSectiionKpis
    {
        public List<KpiList> FundKpis { get; set; }
    }
    public class SpecificKpiPublishModel 
    {
        public List<PCDataModel> CompaniesDetails { get; set; }
        public FundSectiionKpis FundKpiSections { get; set; }
        public int FundId { get; set; }
        public bool IsFinancial { get; set; }
        public string ConnectionString { get; set; }
        public int UserId { get; set; }
        public int FundCurrencyId { get; set; }
        public string FundUnitCurrency { get; set; }
        public long ProcessId { get; set; }
    }
    public class PCDataModel
    {
        public List<StaticDataModel> StaticFields { get; set; }
        public List<KpiList> TradingKpis { get; set; } = [];
        public List<KpiList> CreditKpis { get; set; } = [];
        public List<KpiList> InvestmentKpis { get; set; } = [];
        public List<KpiList> ImpactKpis { get; set; } = [];
        public List<KpiList> CompanyKpis { get; set; } = [];
        public List<KpiList> OperationalKpis { get; set; } = [];
        public List<KpiList> CustomKpis { get; set; } = [];
        public List<KpiList> OtherKpis { get; set; } = [];
        public List<KpiList> ProfitLossKpis { get; set; } = [];
        public List<KpiList> BalancesheetKpis { get; set; } = [];
        public List<KpiList> Cashflow { get; set; } = [];
        public int CompanyId { get; set; }
        public string UnitCurrency { get; set; }
        public int CurrencyId { get; set; }
    }
    public class StaticDataModel
    {
        public int FieldId { get; set; }
        public string FieldValue { get; set; }
        public int? Month { get; set; }
        public int Year { get; set; }
        public string Quarter { get; set; }
        public int ValueTypeId { get; set; }
    }

    public class DataIngestionDocumentMappingModel
    {
        public Guid Id { get; set; }
        public Guid ProcessId { get; set; }
        public int CompanyId { get; set; }
        public int FeatureId { get; set; }
        public int SourceTypeId { get; set; }
        public string S3Path { get; set; }
        public string FileName { get; set; }
        public string Extension { get; set; }
        public int? DocumentTypeId { get; set; }
        public string? PeriodType { get; set; }
        public int? Year { get; set; }
        public string? Month { get; set; }
        public string? Quarter { get; set; }

    }
}