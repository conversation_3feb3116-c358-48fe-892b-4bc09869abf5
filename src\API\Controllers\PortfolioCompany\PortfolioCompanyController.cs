using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using API.Attributes;
using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.City;
using Contract.Configuration;
using Contract.Country;
using Contract.Currency;
using Contract.Designation;
using Contract.Funds;
using Contract.MasterMapping;
using Contract.PortfolioCompany;
using Contract.Region;
using Contract.Repository;
using Contract.Sector;
using Contract.State;
using Contract.Utility;
using DinkToPdf.Contracts;
using Master;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using OfficeOpenXml;
using PortfolioCompany;
using Contract.Account;
using Shared;
using Utility.Helpers;
using Utility.Resource;
using Workflow.Interface;
using Microsoft.AspNetCore.Http;
using DataAccessLayer.DBModel;
using Microsoft.AspNetCore.Authorization;
using Contract.Deals;


namespace API.Controllers;
public class PortfolioCompanyController : BaseController
{
    #region [Public Variables]
    private readonly IWebHostEnvironment _hostingEnvironment;
    private readonly IFundService _fundService;
    private readonly IConverter _converter;
    private readonly IFileService _fileService;
    private readonly IReportTemplateService _reportTemplatService;
    private readonly IPageDetailsConfigurationService _pageDetailConfigService;
    private readonly IPageFieldValueDraftService _pageFieldValueDraftService;
    private readonly ICurrencyService _currencyService;
    private readonly Boolean IsWorkFloW;
    private readonly IFootNote iFootNote;
    private readonly IDesignationService _designationServices;
    private readonly ISectorService _sectorService;

    #endregion [Public Variables]

    public PortfolioCompanyController(IPortfolioCompanyService portfolioCompanyService,
        ISectorService sectorService,
        ICurrencyService currencyService,
        IDesignationService designationService,
        IMasterMappingService masterMappingService,
        IConverter converter, IWebHostEnvironment hostingEnvironment, IInjectedParameters InjectedParameters,
        IFileService fileService, IHelperService helperService, IReportTemplateService reportTemplateService, IPageDetailsConfigurationService pageDetailConfigService, IFundService fundService, IPageFieldValueDraftService pageFieldValueDraftService
        , IFootNote footNote) : base(InjectedParameters, helperService)
    {
        _converter = converter;
        _hostingEnvironment = hostingEnvironment;

        List<Lazy<IPortfolioCompanyService, PortfolioCompanyMetaData>> PortfolioCompanyServices = new()
            {
                new Lazy<IPortfolioCompanyService, PortfolioCompanyMetaData>(() => portfolioCompanyService, new PortfolioCompanyMetaData())
            };
        List<Lazy<ISectorService, SectorMetadata>> SectorServices = new()
            {
                new Lazy<ISectorService, SectorMetadata>(() => sectorService, new SectorMetadata())
            };
        _designationServices = designationService;
        List<Lazy<IMasterMappingService, MasterMappingMetadata>> MasterMappingServices = new()
            {
                new Lazy<IMasterMappingService, MasterMappingMetadata>(() => masterMappingService, new MasterMappingMetadata())
            };
        this.PortfolioCompanyServices = PortfolioCompanyServices;
        _sectorService = sectorService;
        this.DesignationServices = DesignationServices;
        this.MasterMappingServices = MasterMappingServices;
        _fileService = fileService;
        _reportTemplatService = reportTemplateService;
        _pageDetailConfigService = pageDetailConfigService;
        _fundService = fundService;
        _pageFieldValueDraftService = pageFieldValueDraftService;
        _currencyService = currencyService;
        this.IsWorkFloW = Boolean.Parse(_injectedParameters.Configuration.GetSection(Constants.IsWorkflow).Value);
        iFootNote = footNote;
    }


    [Route("portfolio-company/add")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.PortfolioCompany)]
    [ValidationFilter]
    public async Task<IActionResult> AddPortfolioCompany([FromBody] PortfolioCompanyModel portfolioCompanyModel)
    {
        var result = 0;
        int userId = GetCurrentUserId();
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (string.IsNullOrEmpty(portfolioCompanyModel.CompanyName))
        {
            return JsonResponse.Create(HttpStatusCode.Conflict, MessageConstants.CompanyNameNullCheck);
        }
        else if (!portfolioCompanyModel.CompanyName.ValueValidation())
        {
            return JsonResponse.Create(HttpStatusCode.Conflict, MessageConstants.CompanyNameValidate);
        }
        if (service != null)
        {
            if (portfolioCompanyModel.PortfolioCompanyID == 0)
            {
                portfolioCompanyModel.CreatedBy = userId;
                portfolioCompanyModel.CreatedOn = DateTime.Now;
                result = service.Value.AddPortfolioCompany(portfolioCompanyModel);
                if (portfolioCompanyModel.CustomFieldValueList != null && portfolioCompanyModel.CustomFieldValueList.Any())
                {
                    await this._pageDetailConfigService.SavePageConfigurationFieldValue(portfolioCompanyModel.CustomFieldValueList.Where(x => x.DataTypeId != 7).ToList(), result, userId, (int)Contract.Funds.PageConfigurationFeature.PortfolioCompany);
                }
                if (portfolioCompanyModel.CustomPortfolioGroupList?.Any() == true)
                {
                    await service.Value.AddOrUpdatePortfolioCustomList(portfolioCompanyModel.CustomPortfolioGroupList, userId, result);
                }
                if (result == -1)
                    return JsonResponse.Create(HttpStatusCode.Conflict, Messages.PC_Exist);
                else

                    return JsonResponse.Create(HttpStatusCode.OK, Messages.Add_PC_Success, new
                    {
                        PortfolioCompanyID = result

                    });
            }
            else
            {
                portfolioCompanyModel.ModifiedBy = userId;
                portfolioCompanyModel.ModifiedOn = DateTime.Now;
                result = service.Value.EditPortfolioCompany(portfolioCompanyModel);
                if (portfolioCompanyModel.CustomFieldValueList != null && portfolioCompanyModel.CustomFieldValueList.Any())
                {

                    if (this.IsWorkFloW && portfolioCompanyModel.WorkflowRequestId.HasValue)
                    {
                        await this._pageFieldValueDraftService.SavePageConfigurationFieldValue(portfolioCompanyModel.CustomFieldValueList.Where(x => x.DataTypeId != 7).ToList(),
                            userId, (int)Contract.Funds.PageConfigurationFeature.PortfolioCompany, portfolioCompanyModel.PortfolioCompanyID,
                            portfolioCompanyModel.WorkflowRequestId.Value);
                    }
                    else
                        await this._pageDetailConfigService.SavePageConfigurationFieldValue(portfolioCompanyModel.CustomFieldValueList.Where(x => x.DataTypeId != 7).ToList(), portfolioCompanyModel.PortfolioCompanyID, userId, (int)Contract.Funds.PageConfigurationFeature.PortfolioCompany);

                }
                if (portfolioCompanyModel.CustomPortfolioGroupList?.Any() == true)
                {
                    await service.Value.AddOrUpdatePortfolioCustomList(portfolioCompanyModel.CustomPortfolioGroupList, userId, portfolioCompanyModel.PortfolioCompanyID);
                }
                if (result == -1)
                    return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
                else

                    return JsonResponse.Create(HttpStatusCode.OK, Messages.Update_PC_Success);
            }

        }
        return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);

    }
    [Route("portfolioCompany/get")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.Repository, (int)Features.DataExtraction, (int)Features.Deal, (int)Features.KPIsMapping)]
    [ValidationFilter]
    public IActionResult GetPortfolioCompanyList([FromBody] PortfolioCompanyFilter filter)
    {
        var service = _portfolioCompanyServices.FirstOrDefault();
        var result = service.Value.GetPortfolioCompanyList(filter);
        if (result?.PortfolioCompanyList?.Any() == true)
            return JsonResponse.Create(HttpStatusCode.OK, result);
        else
            return JsonResponse.Create(HttpStatusCode.NoContent, Messages.NoRecordFound);
    }


    [Route("portfolioCompany/getCompanyNames")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.Repository, (int)Features.DataExtraction, (int)Features.Deal, (int)Features.KPIsMapping)]
    [ValidationFilter]
    public IActionResult GetPortfolioCompanyNames([FromBody] PortfolioCompanyFilter filter)
    {
        var service = _portfolioCompanyServices.FirstOrDefault();
        var result = service.Value.GetCompanyNames(filter);
        if (result?.PortfolioCompanyList?.Any() == true)
            return JsonResponse.Create(HttpStatusCode.OK, result);
        else
            return JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound);
    }
    [Route("company/get")]
    [UserFeatureAuthorize((int)Features.Repository, (int)Features.ESG, (int)Features.BulkUpload, (int)Features.KPIsMapping, (int)Features.ClientReporting)]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.ClientReporting, (int)Features.ESG, (int)Features.BulkUpload, (int)Features.KPIsMapping, (int)Features.Repository)]
    public async Task<IActionResult> GetPortfolioCompany()
    {
        var result = await _portfolioCompanyServices.FirstOrDefault()?.Value?.GetCompanyList();
        if (result?.Any() == true)
            return Ok(result);
        else
            return NoContent();
    }
    [Route("portfolioCompany/getbyidnew")]
    [HttpPost]
    [ValidationFilter]
    public async Task<IActionResult> GetPortfolioCompanyDetails([FromBody] StringValueModel portfolioCompanyId)
    {
        var decryptedPortfolioCompanyId = _injectedParameters.Encryption.Decrypt(portfolioCompanyId.Value);
        var result = new PortfolioCompanyModel();
        var service = _portfolioCompanyServices.FirstOrDefault();
        var companyIdList = await service.Value.GetCompanyListByUserId(GetCurrentUserId());
        if (!companyIdList.Contains(Convert.ToInt32(decryptedPortfolioCompanyId)))
        {
            return Unauthorized();
        }
        if (service != null)
        {
            result = await service.Value.GetPortfolioCompanyById(Convert.ToInt32(decryptedPortfolioCompanyId));
            result.Companylogo = await GetCompanyLogo(Convert.ToInt32(decryptedPortfolioCompanyId), result.ImagePath);
        }
        if (result?.PortfolioCompanyID > 0)
            return JsonResponse.Create(HttpStatusCode.OK, result);
        else
            return JsonResponse.Create(HttpStatusCode.NoContent, Messages.NoRecordFound);
    }

    [Route("portfolioCompany/getbyid")]
    [HttpPost]
    [ValidationFilter]
    [UserFeatureAuthorize((int)Features.PortfolioCompany, (int)Features.ESG)]
    public async Task<IActionResult> GetPortfolioCompanyDetails_New([FromBody] StringValueModel portfolioCompanyId)
    {
        var decryptedPortfolioCompanyId = _injectedParameters?.Encryption?.Decrypt(portfolioCompanyId?.Value);
        var service = _portfolioCompanyServices.FirstOrDefault();
        var companyIdList = await service.Value.GetCompanyListByUserId(GetCurrentUserId());


        if (!companyIdList.Contains(Convert.ToInt32(decryptedPortfolioCompanyId)))
        {
            if (portfolioCompanyId.Id != "esg")
            {
                return Unauthorized();
            }
        }
        var pcStaticFields = await GetAllSubFieldsOfPortfolioCompany();
        List<PageFieldValueModel> _PageFieldModelDTO = pcStaticFields?.Select(x => new PageFieldValueModel()
        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = Constants.NotAvailable,
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
            DataTypeId = x.DataTypeId,
            IsHighLight = x.IsHighLight,
            IsCustom = x.IsCustom
        })?.ToList();
        DealDataModel dealData = new();
        var dealStaticFields = await GetAllSubFieldsOfDealTrackRecord();
        if(dealStaticFields?.Count != 0)
        {
            _PageFieldModelDTO.AddRange(dealStaticFields);
            _ = int.TryParse(decryptedPortfolioCompanyId, out int companyId);
            dealData = await service.Value.GetDealId(companyId);
        }

        List<SubPageDetailModel> subPageDetailList = await _pageDetailConfigService.GetActiveSubPageSectionByPageId((int)PageConfigurationFeature.PortfolioCompany, true);
        List<PageFieldValueModel> customFields = await GetCustomStaticFieldValue(Convert.ToInt32(decryptedPortfolioCompanyId));
        var result = new PortfolioCompanyModel();
        var fundHoldingValues = await service.Value.GetFundHoldingValues(dealData?.DealId ?? 0);

        var commentaryPeriod = await this._pageFieldValueDraftService.GetCommentaryPeriodMSubSectionField();
        if (service != null)
        {
            result = await service.Value.GetPortfolioCompanyById(Convert.ToInt32(decryptedPortfolioCompanyId), commentaryPeriod != null ? commentaryPeriod : null);
            result.Companylogo = await GetCompanyLogo(Convert.ToInt32(decryptedPortfolioCompanyId), result.ImagePath);
        }
        if (result?.PortfolioCompanyID > 0)
        {
            _PageFieldModelDTO.ForEach(item =>
            {
                if (item.Name == Constants.CompanyLogo)
                    item.Value = result.Companylogo == null ? item.Value : result.Companylogo;
                if (item.Name == Constants.CompanyName)
                    item.Value = result.CompanyName == null ? item.Value : result.CompanyName;
                if (item.Name == Constants.BusinessDescription)
                    item.Value = result.BusinessDescription == null ? item.Value : result.BusinessDescription;
                if (item.Name == Constants.CompanyStatus)
                    item.Value = result.Status == null ? item.Value : result.Status;
                if (item.Name == Constants.FinancialYearEnd)
                    item.Value = result.FinancialYearEnd == null ? item.Value : GetFYEnd(result.FinancialYearEnd);
                if (item.Name == Constants.StockExchange_Ticker)
                    item.Value = result.StockExchange_Ticker == null ? item.Value : result.StockExchange_Ticker;
                if (item.Name == Constants.Currency)
                    item.Value = result.ReportingCurrencyDetail == null ? item.Value : result.ReportingCurrencyDetail.CurrencyCode;
                if (item.Name == Constants.Website)
                    item.Value = result.Website == null ? item.Value : result.Website;
                if (item.Name == Constants.MasterCompanyName)
                    item.Value = result.MasterCompanyName == null ? item.Value : result.MasterCompanyName;
                if (item.Name == Constants.CompanyGroupId)
                    item.Value = result.CompanyGroupId == null ? item.Value : result?.GroupName;
                if (item.Name == Constants.InvestorFundId)
                    item.Value = result.FundDetail == null ? item.Value : JsonConvert.SerializeObject(result.FundDetail);
                if (item.Name == Constants.DealId)
                    item.Value = result.DealDetail == null ? item.Value : JsonConvert.SerializeObject(result.DealDetail);
                if (item.Name == Constants.Sector)
                    item.Value = result.SectorDetail == null ? item.Value : result.SectorDetail.Sector;
                if (item.Name == Constants.SubSector)
                    item.Value = result.SubSectorDetail == null ? item.Value : result.SubSectorDetail.SubSector;
                if (item.Name == Constants.HeadquarterID)
                    item.Value = result.GeographicLocations == null ? item.Value : result.GeographicLocations.FirstOrDefault().ToString();
                if (item.Name == Constants.SignificantEvents)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.SignificantEventsSectionData == null ? item.Value : result.CommentaryData.SignificantEventsSectionData.Item4;
                if (item.Name == Constants.AssessmentPlan)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.AssessmentSectionData == null ? item.Value : result.CommentaryData.AssessmentSectionData.Item4;
                if (item.Name == Constants.ImpactHighlights)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.ImpactSectionData == null ? item.Value : result.CommentaryData.ImpactSectionData.Item4;
                if (item.Name == Constants.ExitPlan)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.ExitPlansSectionData == null ? item.Value : result.CommentaryData.ExitPlansSectionData.Item4;
                if (item.Name == Constants.CompanyLegalName)
                    item.Value = result.CompanyLegalName ?? item.Value;
                if (item.Name == Constants.PcInvestmentDate)
                    item.Value = result?.InvestmentDate == null ? item.Value : result.InvestmentDate.Value.ToString("MM/dd/yyyy");
                var target = customFields.Find(x => x.FieldID == item.FieldID);
                if (target != null)
                    item.Value = target.Value;
                if(item.SubPageID == (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails && fundHoldingValues!=null)
                {
                    SetDealItemValue(fundHoldingValues, item,dealData);
                }
            });
            return JsonResponse.Create(HttpStatusCode.OK, new
            {
                SubPageList = subPageDetailList.OrderBy(x=>x.SequenceNo).ToList(),
                FieldValueList = _PageFieldModelDTO,
                CompanyDetails = result,
                CommentaryPeriod = commentaryPeriod
            });

        }
        else
            return JsonResponse.Create(HttpStatusCode.NoContent, Messages.NoRecordFound);

    }
    private static void SetDealItemValue(PortfolioCompanyFundHoldingModel x, PageFieldValueModel item,DealDataModel dealData)
    {
        string NA = "NA";
        if (x != null && !item.IsCustom)
        {
            if (item.Name == Constants.InvestmentDate)
                item.Value = dealData.InvestmentDate == null ? NA : dealData.InvestmentDate.Value.ToString("MM/dd/yyyy");
            if (item.Name == Constants.RealizedValue)
                item.Value = x.RealizedValue == null ? NA : x.RealizedValue.ToString();
            if (item.Name == Constants.UnrealizedValue)
                item.Value = x.UnrealizedValue == null ? NA : x.UnrealizedValue.ToString();
            if (item.Name == Constants.Dpi)
                item.Value = x.Dpi == null ? NA : x.Dpi.ToString();
            if (item.Name == Constants.Rvpi)
                item.Value = x.Rvpi == null ? NA : x.Rvpi.ToString();
            if (item.Name == "Year")
                item.Value = x.Year == 0 ? NA : x.Year.ToString();
            if (item.Name == "Quarter")
                item.Value = x.Quarter == null ? NA : x.Quarter.ToString() + " " + x.Year.ToString();
            if (item.Name == Constants.GrossIRR)
                item.Value = x.GrossIRR == null ? NA : x.GrossIRR.ToString();
            if (item.Name == Constants.GrossMultiple)
                item.Value = x.GrossMultiple == null ? NA : x.GrossMultiple.ToString();
            if (item.Name == Constants.TotalValue)
                item.Value = x.TotalValue == null ? NA : x.TotalValue.ToString();
            if (item.Name == Constants.ValuationDate)
                item.Value = x.ValuationDate == null ? NA : x.ValuationDate.Value.ToString("MM/dd/yyyy");
            if (item.Name == Constants.InvestmentCost)
                item.Value = x.InvestmentCost == null ? NA : x.InvestmentCost.ToString();
            if (item.Name == Constants.DealStatus)
                item.Value = x.FundHoldingStatus == null ? NA : x.FundHoldingStatus?.Status?.ToString();
        }
        if (item.IsCustom)
        {
            var value = x.DealTrackRecordConfigurationData.FirstOrDefault(y => y.Year == x.Year && y.Quarter == x.Quarter && y.FieldID == item.FieldID && y.PageFeatureId == x.DealID)?.Value;
            item.Value = value ?? NA;
        }
    }

    private string GetFYEnd(string value)
    {
        if (value != null && value.Split(' ').Length > 3)
        {
            return value.Split(' ')[3];
        }
        else
            return value;
    }

    private void CopyValues<T>(T target, T source)
    {
        Type t = typeof(T);

        var properties = t.GetProperties();

        foreach (var prop in properties)
        {
            var value = prop.GetValue(source, null);
            if (value != null)
                prop.SetValue(target, value, null);
        }
    }


    private async Task<List<PageFieldValueModel>> GetCustomStaticFieldValue(int decryptedCompanyID)
    {
        var result = await _pageDetailConfigService.GetPageConfigFieldValueByFeatureId((int)PageConfigurationFeature.PortfolioCompany, decryptedCompanyID);
        return result.Where(x => x.PageID == (int)PageConfigurationFeature.PortfolioCompany && x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation).ToList();
    }

    private async Task<List<PageFieldValueModel>> GetCustomStaticFieldValueDraft(int decryptedCompanyID, int workFlowRequestId)
    {
        var result = await _pageFieldValueDraftService.GetPageConfigFieldValueByFeatureId((int)PageConfigurationFeature.PortfolioCompany, decryptedCompanyID, workFlowRequestId);

        return result.Where(x => x.PageID == (int)PageConfigurationFeature.PortfolioCompany && x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation).Select(x => _injectedParameters.Mapper.Map<PageFieldValueModel>(x)).ToList();

    }

    private List<PageFieldValueModel> GenerateCustomFieldHtml(PortfolioCompanyModel company)
    {
        var pcid = company.PortfolioCompanyID;
        List<PageFieldValueModel> customFieldValues = GetCustomStaticFieldValue(pcid).Result;
        var pcStaticFields = GetAllSubFieldsOfPortfolioCompany().Result;
        List<PageFieldValueModel> _staticCustomFields = pcStaticFields.FindAll(x => x.IsCustom && x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation).Select(x => new PageFieldValueModel()
        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = "N/A",
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
        }).ToList();
        _staticCustomFields.ForEach(item =>
        {
            var target = customFieldValues.Find(x => x.FieldID == item.FieldID);
            if (target != null)
                CopyValues(item, target);
        });
        return _staticCustomFields;

    }

    private async Task<List<SubPageFieldModel>> GetAllSubFieldsOfPortfolioCompany()
    {
        return await _pageDetailConfigService.GetActiveFieldsByPageId((int)PageConfigurationFeature.PortfolioCompany);
    }
    private async Task<List<PageFieldValueModel>> GetAllSubFieldsOfDealTrackRecord()
    {
        var result =  await _pageDetailConfigService.GetActiveFieldsBySubPageId((int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails);
        return result?.Where(x => x.IsPcLink)?.Select(x => new PageFieldValueModel()
        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = Constants.NotAvailable,
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
            DataTypeId = x.DataTypeId,
            IsHighLight = x.IsHighLight,
            IsCustom = x.IsCustom
        })?.ToList();
    }
    [Route("portfolioCompany/draft/getbyid/{companyId}/{workflowRequestId}")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.PortfolioCompany)]
    [ValidationFilter]
    public async Task<IActionResult> GetPortfolioCompanyDetailsDraft(string companyId, int workflowRequestId)
    {
        var decryptedPortfolioCompanyId = _injectedParameters.Encryption.Decrypt(companyId);
        var result = new PortfolioCompanyModel();
        var service = _portfolioCompanyServices.FirstOrDefault();
        bool isWorkFlow = this.IsWorkFloW;
        if (service != null)
        {
            result = await service.Value.GetPortfolioCompanyDraftById(Convert.ToInt32(decryptedPortfolioCompanyId), workflowRequestId);
            result.Companylogo = await GetCompanyLogo(Convert.ToInt32(decryptedPortfolioCompanyId), result.ImagePath);
        }
        var pcStaticFields = await _pageDetailConfigService.GetActiveFieldsByPageId((int)PageConfigurationFeature.PortfolioCompany);
        List<PageFieldValueModel> _PageFieldModelDTO = pcStaticFields.Select(x => new PageFieldValueModel()

        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = Constants.NotAvailable,
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
            IsCustom = x.IsCustom,
            IsMandatory = x.IsMandatory,
            DataTypeId = x.DataTypeId,
            IsHighLight = x.IsHighLight
        }).ToList();

        List<SubPageDetailModel> subPageDetailList = await _pageDetailConfigService.GetActiveSubPageSectionByPageId((int)PageConfigurationFeature.PortfolioCompany);
        List<PageFieldValueModel> customFields;
        if (isWorkFlow)
        {
            customFields = await GetCustomStaticFieldValueDraft(Convert.ToInt32(decryptedPortfolioCompanyId), workflowRequestId);
        }
        else
        {
            customFields = await GetCustomStaticFieldValue(Convert.ToInt32(decryptedPortfolioCompanyId));
        }
        customFields.ForEach(x => x.IsCustom = true);
        if (result?.PortfolioCompanyID > 0)
        {
            _PageFieldModelDTO.ForEach(item =>
            {
                switch (item.Name)
                {
                    case Constants.CompanyLogo:
                        item.Value = result.Companylogo == null ? item.Value : result.Companylogo;
                        break;

                    case Constants.CompanyName:
                        item.Value = result.CompanyName == null ? item.Value : result.CompanyName;
                        break;
                    case Constants.BusinessDescription:
                        item.Value = result.BusinessDescription == null ? item.Value : result.BusinessDescription;
                        break;
                    case Constants.CompanyStatus:
                        item.Value = result.Status == null ? item.Value : result.Status;
                        break;
                    case Constants.FinancialYearEnd:
                        item.Value = result.FinancialYearEnd == null ? item.Value : GetFYEnd(result.FinancialYearEnd);
                        break;
                    case Constants.StockExchange_Ticker:
                        item.Value = result.StockExchange_Ticker == null ? item.Value : result.StockExchange_Ticker;
                        break;
                    case Constants.Currency:
                        item.Value = result.ReportingCurrencyDetail == null ? item.Value : result.ReportingCurrencyDetail.CurrencyCode;
                        break;
                    case Constants.Website:
                        item.Value = result.Website == null ? item.Value : result.Website;
                        break;
                    case Constants.Sector:
                        item.Value = result.SectorDetail == null ? item.Value : result.SectorDetail.Sector;
                        break;
                    case Constants.SubSector:
                        item.Value = result.SubSectorDetail == null ? item.Value : result.SubSectorDetail.SubSector;
                        break;
                    case Constants.HeadquarterID:
                        item.Value = result.GeographicLocations == null ? item.Value : result?.GeographicLocations.FirstOrDefault()?.ToString();
                        break;
                    case Constants.MasterCompanyName:
                        item.Value = result.MasterCompanyName == null ? item.Value : result.MasterCompanyName;
                        break;
                    case Constants.CompanyGroupId:
                        item.Value = result.CompanyGroupId == null ? "0" : result.CompanyGroupId.ToString();
                        break;
                    case Constants.SignificantEvents:
                        item.Value = result.CommentaryData == null || result.CommentaryData?.SignificantEventsSectionData == null ? item.Value : result.CommentaryData.SignificantEventsSectionData.Item4;
                        break;
                    case Constants.AssessmentPlan:
                        item.Value = result.CommentaryData == null || result.CommentaryData?.AssessmentSectionData == null ? item.Value : result.CommentaryData.AssessmentSectionData.Item4;
                        break;
                    case Constants.ImpactHighlights:
                        item.Value = result.CommentaryData == null || result.CommentaryData?.ImpactSectionData == null ? item.Value : result.CommentaryData.ImpactSectionData.Item4;
                        break;
                    case Constants.ExitPlan:
                        item.Value = result.CommentaryData == null || result.CommentaryData?.ExitPlansSectionData == null ? item.Value : result.CommentaryData.ExitPlansSectionData.Item4;
                        break;
                    case Constants.Customfield:
                        var target = customFields.Find(x => x.FieldID == item.FieldID);
                        if (target != null)
                            item.Value = target.Value;
                        break;
                }

            });
            return JsonResponse.Create(HttpStatusCode.OK, new
            {
                SubPageList = subPageDetailList,
                FieldValueList = _PageFieldModelDTO,
                CompanyDetails = result,
                isWorkflow = isWorkFlow
            });
        }
        else
            return JsonResponse.Create(HttpStatusCode.NoContent, Messages.NoRecordFound);
    }
    private async Task<string> GetCompanyLogo(int companyId, string imageName)
    {
        if (string.IsNullOrEmpty(imageName))
        {
            return null;
        }
        string path = $"logos/{companyId}/";

        var files = await _fileService.GetFiles(path, "1");

        if (files != null && files?.Count > 0)
        {
            var result = files.Find(x => x.Key.ToLower().Trim() == imageName.ToLower().Trim());
            return result.Key != null ? result.Value : null;
        }
        return null;
    }

    [Route("portfolioCompany/importportfoliocompanydetails")]
    [HttpPost]
    public IActionResult UploadPortFolioCompany(PortFolioCompanyImportModel fileModel)
    {

        dynamic value = null;
        var result = 0;
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (service != null)
        {
            using (var scope = new TransactionScope(TransactionScopeOption.Required, TimeSpan.FromMinutes(20)))
            {
                var batchCount = 1;
                var companyBatch = fileModel.PortFolioCompanyDetails.Take(100).ToList();
                while (companyBatch.Any())
                {
                    result = service.Value.AddPortfolioCompanyList(companyBatch);

                    if (result == 1)
                    {

                        value = Ok(Messages.Bulk_Upload_Success);
                        companyBatch = fileModel.PortFolioCompanyDetails.Skip(batchCount * 100).Take(100).ToList();
                        batchCount++;
                    }
                    else if (result < 0)
                    {
                        value = JsonResponse.Create(HttpStatusCode.Conflict, Messages.PC_Exist);
                        break;
                    }
                    else
                    {
                        value = JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
                        break;
                    }
                }

                if (result == 1)
                    scope.Complete();
            }
        }
        return value;

    }


    [Route("portfolio-company/master-model/get")]
    [HttpGet]
    [UserFeatureAuthorize((int)Features.PortfolioCompany)]
    public async Task<IActionResult> GetPCMasterModel()
    {
        var model = new MasterPCModel();
            model.SectorList = _sectorService.GetAllSectors();
        model.ReportingCurrencyList = _currencyService.GetAllCurrencies();
        model.DesignationList = _designationServices.GetDesignations();
        var fundList = await _fundService.GetFundList();
        model.FundList = fundList.Select(f => new FundPCModel() { FundName = f.FundName }).ToList();
        return JsonResponse.Create(HttpStatusCode.OK, model);
    }

    [Route("portfolio-company/ingestion/{companyId}")]
    [HttpGet]
    public async Task<IActionResult> GetCompanyMasterModel(int companyId)
    {
        var companyModel = await _portfolioCompanyServices?.FirstOrDefault()?.Value?.GetPortfolioCompanyDetailsById(companyId);
        companyModel.Currencies = _currencyService.GetAllCurrencies().Select(x=>new CurrencyIngestionModel()
        {
            Currency = x.Currency,
            CurrencyCode = x.CurrencyCode,
            CurrencyID = x.CurrencyID
        }).ToList();
        return Ok(companyModel);
    }
    [HttpGet("ingestion/currency-list")]
    public IActionResult GetCurrencyList()
    {
        var Currencies = _currencyService.GetAllCurrencies().Select(x => new CurrencyIngestionModel()
        {
            Currency = x.Currency,
            CurrencyCode = x.CurrencyCode,
            CurrencyID = x.CurrencyID
        }).ToList();
        return Ok(Currencies);
    }
    [Route("portfolio-company/funding-type/get")]
    [HttpGet]
    public IActionResult GetFundingTypes()
    {
        List<FundingTypeModel> model = null;
        var portfolioCompanyService = _portfolioCompanyServices.FirstOrDefault();
        if (portfolioCompanyService != null)
            model = portfolioCompanyService.Value.GetAllFundingTypes();
        return JsonResponse.Create(HttpStatusCode.OK, model);
    }

    [Route("portfolioCompany/getoperationalKPI")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.KPIsMapping, (int)Features.Report)]
    [ValidationFilter]
    public IActionResult GetOperationalKPIListForPortfolioCompany([FromBody] StringValueModel portfolioCompanyId)
    {
        int intPrtfolioCompanyId = 0;
        if (!string.IsNullOrEmpty(portfolioCompanyId.Value))
            intPrtfolioCompanyId = Convert.ToInt32(portfolioCompanyId.Value);
        var result = new List<SectorwiseKpiDetails>();
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (service != null)
            result = service.Value.GetSectorwiseKPIListForPortfolioCompany(intPrtfolioCompanyId);
        if (result == null)
            return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
        else
            return JsonResponse.Create(HttpStatusCode.OK, result);
    }
    #region Bulk Upload
    [Produces("application/json")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.BulkUpload)]
    [Route("portfolio-company/import")]
    public ActionResult BulkUploadCompany([FromForm] IFormCollection form)
    {
        List<Status> moduleStatus = new List<Status>();
        Status status = new Status();
        IFormFile file = form.Files.FirstOrDefault();
        var moduleName = form["MODULENAME"];
        var guid = Guid.NewGuid();
        string webRootPath = Path.Combine(_injectedParameters.GlobalConfigurations.ExportFileUploadPath, guid.ToString());
        webRootPath += Path.DirectorySeparatorChar.ToString();

        if (!Directory.Exists(webRootPath))
        {
            Directory.CreateDirectory(webRootPath);
        }
        if (file.Length > 0)
        {
            string fileName = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"');
            string[] validFileTypes = { ".xls", ".xlsx" };
            var extension = Path.GetExtension(fileName).ToLower();
            if (validFileTypes.Contains(extension))
            {

                string fullPath = Path.Combine(webRootPath, fileName);
                using (var stream = new FileStream(fullPath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                using (var package = new ExcelPackage(new FileInfo(fullPath)))
                {
                    var checkHeader = new CheckExcelFileHeader(moduleName);
                    status = checkHeader.CheckUploadFileHeaderForModules(package, moduleName);
                    if (status.Code != "error")
                    {
                        moduleStatus = ValidatePortfolioCompanyBulkData(package, fileName, fullPath, extension);

                    }
                    else
                    {
                        moduleStatus.Add(status);
                    }
                }
            }
            else
            {
                status.Message = Messages.User_ValidFileFormatBulkUpload;
                status.Code = "error";
                moduleStatus.Add(status);
            }
        }

        return JsonResponse.Create(HttpStatusCode.OK, moduleStatus);

    }

    private List<Status> ValidatePortfolioCompanyBulkData(ExcelPackage package, string fileName, string fullPath, string extension)
    {
        int createdBy = GetCurrentUserId();

        List<Status> portfolioStatus = new List<Status>();
        Status status = new Status();
        var pClist = new List<BulkUploadPortFolioCompanyDetails>();
        ErrorListHandler errorList = new ErrorListHandler();
        List<PortfolioCompanyProfitabilityModel> quarterlyDataList = null;
        List<string> checkPCMndtoryFields = new List<string>();
        foreach (var workSheet in package.Workbook.Worksheets)
        {


            var sheetName = Convert.ToString(workSheet);

            if (status.Code == null)
            {
                if (sheetName == "Portfolio Details")
                {
                    status = errorList.CheckCompanyNameDuplicacy(workSheet);
                    if (status.Code == "error")
                        status.Message = new StringBuilder(status.Message + "<br/>  SheetName :- " + sheetName + "   <br/>" + Messages.Utility_CheckMissingFields + "<br/>").ToString();
                }
                if (status.Code != "error")
                {
                    if (sheetName == "Portfolio Details" && status.Code == null)
                    {

                        foreach (var wrkSheet in package.Workbook.Worksheets)
                        {
                            if (Convert.ToString(wrkSheet) == "Geo Location")
                            {
                                checkPCMndtoryFields = CheckPCBasicDetailsMandatoryMapping(wrkSheet);
                            }
                        }
                        pClist = PCBasicDetailsEntriesAsync(workSheet, checkPCMndtoryFields, createdBy, sheetName, pClist, out status);
                    }
                    else if (sheetName == "Geo Location" && pClist != null)
                    {
                        pClist = PCGeoLocationEntriesAsync(workSheet, sheetName, pClist, out status);
                    }
                    else if (sheetName == "Employee Details" && pClist != null)
                    {
                        pClist = PCEmployeeDetailsEntries(workSheet, sheetName, pClist, out status);
                        if (status.Code == "error")
                        {
                            status.Message = new StringBuilder(status.Message + "<br/>  SheetName :- " + sheetName + "   <br/>" + Messages.Utility_CheckMissingFields + "<br/>").ToString();
                            status.Code = "error";
                        }
                    }
                }
                if (!string.IsNullOrEmpty(status.Message)) portfolioStatus.Add(status);
            }
            if (sheetName == "QuarterlyData" && (pClist?.Any() != true || status.Code == null))
            {
                quarterlyDataList = ValidateQuarterlyPortFolioCompanyBulkData(workSheet, createdBy, pClist, status);

                if (!string.IsNullOrEmpty(status.Message))
                {
                    portfolioStatus.Add(status);
                }
            }
        }

        if (status.Code == "info")
        {
            portfolioStatus.Clear();
            status.Message = "<br/>" + Messages.Utility_BothSheetsAreEmptyOrMissingFields + "<br/>";
            status.Code = "error";
            portfolioStatus.Add(status);
        }
        else if (status.Code != "error" && pClist?.Any() == true)
        {

            PortFolioCompanyImportModel portFolioCompanyFileModel = new PortFolioCompanyImportModel
            {
                PortFolioCompanyDetails = pClist,
                PortfolioCompanyProfitabilityList = quarterlyDataList,
                FileName = fileName,
                FilePath = fullPath,
                Extension = extension,
                CreatedBy = createdBy,
                CreatedOn = DateTime.Now
            };
            status = SaveCompanyBulkRecords(portFolioCompanyFileModel);

            if (status.Code == "OK") status.Message = status.Message + "<li>" + pClist.Count + " record Uploaded.";
            if (!string.IsNullOrEmpty(status.Message)) portfolioStatus.Add(status);

        }

        return portfolioStatus;
    }

    private Status SaveCompanyBulkRecords(PortFolioCompanyImportModel fileModel)
    {
        Status status = new Status();
        var result = 0;
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (service != null)
        {
            using (var scope = new TransactionScope(TransactionScopeOption.Required, TimeSpan.FromMinutes(20)))
            {
                var batchCount = 1;
                var companyBatch = fileModel.PortFolioCompanyDetails.Take(100).ToList();
                while (companyBatch.Any())
                {
                    result = service.Value.AddPortfolioCompanyList(companyBatch);

                    if (result == 1)
                    {
                        status.Code = Convert.ToString(HttpStatusCode.OK);
                        status.Message = Messages.Bulk_Upload_Success;
                        companyBatch = fileModel.PortFolioCompanyDetails.Skip(batchCount * 100).Take(100).ToList();
                        batchCount++;
                    }
                    else if (result < 0)
                    {
                        status.Code = Convert.ToString(HttpStatusCode.Conflict);
                        status.Message = Messages.PC_Exist;

                        break;
                    }
                    else
                    {
                        status.Code = Convert.ToString(HttpStatusCode.InternalServerError);
                        status.Message = Messages.SomethingWentWrong;
                        break;
                    }
                }

                if (result == 1)
                    scope.Complete();
            }
        }
        return status;
    }

    private static List<string> CheckPCBasicDetailsMandatoryMapping(ExcelWorksheet workSheet)
    {
        var start = workSheet.Dimension.Start;
        var end = workSheet.Dimension.End;
        List<string> checkPCMndtoryFields = new List<string>();
        if (workSheet.Cells[1, 1].Text.ToLower().Trim() == "company name")
        {
            for (int row = start.Row + 1; row <= end.Row; row++)
            {

                checkPCMndtoryFields.Add(Convert.ToString(workSheet.Cells[row, 1].Text.ToLower().Trim()));
            }
        }
        return checkPCMndtoryFields;
    }

    private List<BulkUploadPortFolioCompanyDetails> PCBasicDetailsEntriesAsync(ExcelWorksheet workSheet, List<string> checkPCMndtoryFields, int createdBy, string sheetName, List<BulkUploadPortFolioCompanyDetails> list, out Status status)
    {
        status = new Status();
        var start = workSheet.Dimension.Start;
        var end = workSheet.Dimension.End;
        BulkUploadPortFolioCompanyDetails detail = null;

        var sectorListModel = new List<SectorModel>();
            sectorListModel = _sectorService.GetAllSectors();
        for (int row = start.Row + 1; row <= end.Row; row++)
        {
            detail = new BulkUploadPortFolioCompanyDetails
            {
                SheetName = sheetName,
                IsValid = true,
                CreatedOn = DateTime.Now,
                CreatedBy = createdBy
            };

            for (int col = start.Column; col <= end.Column; col++)
            {
                #region [Validate Company Name]
                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "company name")
                {
                    detail.CompanyName = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());
                    if (string.IsNullOrEmpty(detail.CompanyName))
                    {
                        detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_CompanyNameRequired)).ToString();
                        detail.IsValid = false;
                    }
                    else
                    {

                        decimal isNumericString = 0;
                        bool result = decimal.TryParse(detail.CompanyName, out isNumericString);

                        if (result || !Regex.IsMatch(detail.CompanyName, @"^(?!\s)(?!.*\s$)(?=.*[a-zA-Z0-9])[a-zA-Z0-9 '&()-\/,_ ]{2,}$", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250)))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_CompanyNameNotValid)).ToString();
                            detail.IsValid = false;
                        }
                        else if (detail.CompanyName.Length > 100)
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_PortFolioCompanyNameLengthExceeded)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {

                            bool isLocationFirmNameMappedWithBasicDetailsFirm = checkPCMndtoryFields.Contains(detail.CompanyName.ToLower().Trim());
                            if (!(isLocationFirmNameMappedWithBasicDetailsFirm))
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(" Company Name  " + detail.CompanyName + " is not mapped with any geographic location ")).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                detail.CompanyName = Convert.ToString(workSheet.Cells[row, col].Text);
                            }
                        }
                    }
                }
                #endregion [Validate Company Name]

                #region [Validate Business Description]
                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "business description")
                {
                    detail.BusinessDescription = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                    if (!string.IsNullOrEmpty(detail.BusinessDescription))
                        detail.BusinessDescription = Convert.ToString(workSheet.Cells[row, col].Text);
                }
                #endregion [Validate Business Description]

                #region [Validate Website]
                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "website")
                {
                    detail.Website = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                    if (!string.IsNullOrEmpty(detail.Website))
                    {
                        var result = Regex.IsMatch(detail.Website, @"^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));

                        if (result)
                        {
                            detail.Website = Convert.ToString(workSheet.Cells[row, col].Text);
                        }
                        else
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_WebsiteNotValid)).ToString();
                            detail.IsValid = false;
                        }
                    }
                }
                #endregion [Validate Website]

                #region [Status]

                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "status")
                {
                    if (workSheet.Cells[row, col].Text.ToLower().Trim() == "public" || workSheet.Cells[row, col].Text.ToLower().Trim() == "private")
                    {
                        detail.Status = workSheet.Cells[row, col].Text.ToLower().Trim();

                    }
                    else
                    {
                        detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_InvalidStatus)).ToString();
                        detail.IsValid = false;
                    }
                }
                #endregion [Status]

                #region [StockExchange]
                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "stock exchange")
                {
                    detail.StockExchange_Ticker = workSheet.Cells[row, col].Text.ToLower().Trim();
                }
                #endregion [StockExchange]

                #region [Sector]
                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "sector")
                {
                    detail.SectorDetail = new SectorModel();
                    detail.SectorDetail.Sector = Convert.ToString(workSheet.Cells[row, col].Text.Trim().ToLower());

                    if (!string.IsNullOrEmpty(detail.SectorDetail.Sector))
                    {
                        if (sectorListModel != null)
                        {
                            var sectorDetail = sectorListModel.FirstOrDefault(x => x.Sector.ToLower().Trim() == detail.SectorDetail.Sector);
                            if (sectorDetail == null)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_InvalidSectorFound)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                detail.SectorDetail = sectorDetail;
                            }
                        }
                    }
                    else
                    {
                        detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_NoSectorFound)).ToString();
                        detail.IsValid = false;
                    }
                }
                #endregion [Sector]
            }
            list.Add(detail);
        }
        if (list == null || list.Count == 0) { status.Message = status.Message + "<br/>  SheetName :- " + sheetName + "   <br/>" + Messages.Utility_CheckMissingFields + "<br/>"; status.Code = "info"; return list; } else if (list.Any(x => !x.IsValid)) { ErrorListHandler errorObj = new ErrorListHandler(); errorObj.ErrorDetailsWithStatus(sheetName, ref status, null, list, null); return list; } else { return list; }

    }

    private List<BulkUploadPortFolioCompanyDetails> PCGeoLocationEntriesAsync(ExcelWorksheet workSheet, string sheetName, List<BulkUploadPortFolioCompanyDetails> list, out Status status)
    {
        status = new Status();

        var start = workSheet.Dimension.Start;
        var end = workSheet.Dimension.End;
        BulkUploadPortFolioCompanyDetails detail = null;
        List<string> locationlst = new List<string>();
        List<LocationModel> locationModelList = new List<LocationModel>();

        for (int row = start.Row + 1; row <= end.Row; row++)
        {
            #region [Row by row]
            detail = new BulkUploadPortFolioCompanyDetails
            {
                SheetName = sheetName,
                IsValid = true
            };

            BulkUploadPortFolioCompanyDetails portFolioCompanyDetail = null;
            #endregion [Row by row]

            for (int col = start.Column; col <= end.Column; col++)
            {

                #region [Validate PortFolio Company Name]

                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "company name")
                {
                    detail.CompanyName = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                    if (string.IsNullOrEmpty(detail.CompanyName))
                    {
                        detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_CompanyNameRequired)).ToString();
                        detail.IsValid = false;
                    }
                    else
                    {
                        portFolioCompanyDetail = list.FirstOrDefault(x => x.CompanyName.ToLower().Trim() == detail.CompanyName);
                        if (portFolioCompanyDetail != null)
                        {
                            detail = portFolioCompanyDetail;

                            detail.MasterLocationModel = new MasterLocationModel();
                            detail.LocationModel = new LocationModel();
                            if (detail.GeographicLocations == null) detail.GeographicLocations = new List<LocationModel>();
                            detail.IsValid = true;
                            detail.StatusDescription = string.Empty;
                        }
                        else
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(" Company Name  " + detail.CompanyName + " is not mapped with any provided companies ")).ToString();
                            detail.IsValid = false;
                        }

                        detail.SheetName = sheetName;

                        col++;
                    }
                }
                #endregion [Validate PortFolio Company Name]

                if (portFolioCompanyDetail != null)
                {
                    #region [Validate Region]
                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "region")
                    {
                        detail.Region = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (!string.IsNullOrEmpty(detail.Region))
                        {
                            detail.LocationModel.Region = new RegionModel { Region = detail.Region };
                        }
                    }
                    #endregion [Validate Region]

                    #region [Validate Country]
                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "country")
                    {
                        detail.CountryName = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(detail.CountryName))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.User_CountryRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            detail.LocationModel.Country = new CountryModel { Country = detail.CountryName };
                        }
                    }
                    #endregion [Validate Country]

                    #region [State]

                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "state")
                    {
                        detail.State = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (!string.IsNullOrEmpty(detail.State) && !string.IsNullOrEmpty(detail.CountryName))
                        {
                            detail.LocationModel.State = new StateModel { State = detail.State };
                        }

                    }
                    #endregion [State]

                    #region [City]

                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "city")
                    {
                        detail.City = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (!string.IsNullOrEmpty(detail.CountryName) && !string.IsNullOrEmpty(detail.City))
                        {
                            detail.LocationModel.City = new CityModel { City = detail.City };

                        }

                    }
                    #endregion [City]

                    #region [isHeadQuarter]

                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "isheadquarter")
                    {
                        if (!string.IsNullOrEmpty(workSheet.Cells[row, col].Text.ToLower().Trim()))
                        {
                            if (workSheet.Cells[row, col].Text.ToLower().Trim() == "yes" || workSheet.Cells[row, col].Text.ToLower().Trim() == "no")
                            {
                                if (detail.GeographicLocations.Select(x => x.IsHeadquarter).Contains(true) && workSheet.Cells[row, col].Text.ToLower().Trim() == "yes")
                                {

                                    detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Utility_DuplicateHeadQuarter)).ToString();
                                    detail.IsValid = false;
                                }
                                else
                                {
                                    detail.LocationModel.IsHeadquarter = workSheet.Cells[row, col].Text.ToLower().Trim() == "yes";
                                }

                            }
                            else
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Firm_InvalidIsHeadQuarter)).ToString();
                                detail.IsValid = false;
                            }

                        }
                        else
                        {
                            detail.LocationModel.IsHeadquarter = false;
                        }
                    }

                    #endregion [isHeadQuarter]
                }
            }

            if (detail.LocationModel.Country != null && detail.City != null && detail.GeographicLocations.Any(geoLocation => geoLocation.Country.Country.ToLower().Trim() == detail.LocationModel.Country.Country.ToLower().Trim() && geoLocation.City.City.ToLower().Trim() == detail.City.ToLower().Trim()))
            {
                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Firm_GeoLocationAlreadyExists)).ToString();
                detail.IsValid = false;
            }

            if (detail.IsValid)
            {
                detail.LocationModel.LocationID = row - start.Row;
                locationModelList.Add(detail.LocationModel);
                detail.GeographicLocations.Add(detail.LocationModel);
            }
            locationlst.Add(detail.StatusDescription);
        }

        List<LocationModel> resultMapping = new List<LocationModel>();
        var service = _masterMappingServices.FirstOrDefault();
        if (service != null)
        {
            resultMapping = service.Value.FillMappingWithLocationIds(locationModelList);
        }
        if (resultMapping != null)
        {
            var errorLocations = resultMapping.Where(x => !((x.Region == null || x.Region.RegionId > 0) && (x.Country == null || x.Country.CountryId > 0) &&
               (x.State == null || x.State.StateId > 0) && (x.City == null || x.City.CityId > 0)));

            if (errorLocations.Any())
            {

                foreach (var errorLoc in errorLocations)
                {
                    if (locationlst.Count >= errorLoc.LocationID)
                    {
                        var statusDescription = locationlst[errorLoc.LocationID - 1];
                        if (errorLoc.Region != null && errorLoc.Region.RegionId <= 0)
                        {
                            statusDescription += HtmlUtility.ToListItem(Messages.Firm_NoRegionFound);
                        }
                        if (errorLoc.Country != null && errorLoc.Country.CountryId <= 0)
                        {
                            statusDescription += HtmlUtility.ToListItem(Messages.Firm_NoCountryFound);
                        }
                        else
                        {
                            if (errorLoc.State != null && errorLoc.State.StateId <= 0)
                            {
                                statusDescription += HtmlUtility.ToListItem(Messages.Firm_NoStateFound);
                            }
                            if (errorLoc.City != null && errorLoc.City.CityId <= 0)
                            {
                                statusDescription += HtmlUtility.ToListItem(Messages.Firm_NoCityFound);
                            }
                        }
                        locationlst[errorLoc.LocationID - 1] = statusDescription;
                    }
                }
            }
            else
            {

                list = list.Select(x =>
                {

                    x.GeographicLocations = x.GeographicLocations.Select(y =>
                    {
                        var mapp = resultMapping.FirstOrDefault(z => z.LocationID == y.LocationID);
                        if (mapp != null)
                        {
                            y = mapp;
                        }
                        return y;
                    }).ToList();

                    return x;
                }).ToList();
            }

        }
        else
        {
            locationlst = new List<string>();
        }

        if (locationlst.Count == 0) { status.Message = status.Message + "<br/>  SheetName :- " + sheetName + "  <br/>" + Messages.Utility_CheckMissingFields + "<br/>"; status.Code = "info"; return list; } else if (locationlst.Any(x => !String.IsNullOrEmpty(x))) { ErrorListHandler errorObj = new ErrorListHandler(); errorObj.ErrorDetailsWithStatus(sheetName, ref status, null, null, null, locationlst); return list; } else { return list; }
    }

    private List<BulkUploadPortFolioCompanyDetails> PCEmployeeDetailsEntries(ExcelWorksheet workSheet, string sheetName, List<BulkUploadPortFolioCompanyDetails> list, out Status status)
    {
        status = new Status();

        var start = workSheet.Dimension.Start;
        var end = workSheet.Dimension.End;
        BulkUploadPortFolioCompanyDetails detail = null;

        List<string> Employeelst = new List<string>();

        var desigModel = _designationServices.GetDesignations();

        for (int row = start.Row + 1; row <= end.Row; row++)
        {
            #region [Row by row]
            detail = new BulkUploadPortFolioCompanyDetails();
            detail.SheetName = sheetName;

            detail.IsValid = true;
            #endregion [Row by row]

            BulkUploadPortFolioCompanyDetails portFolioCompanydetail = null;

            for (int col = start.Column; col <= end.Column; col++)
            {
                #region [Validate PortFolio Company Name]

                if (workSheet.Cells[1, col].Text.ToLower().Trim() == "company name")
                {
                    detail.CompanyName = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                    if (string.IsNullOrEmpty(detail.CompanyName))
                    {
                        detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_CompanyNameRequired)).ToString();
                        detail.IsValid = false;
                    }
                    else
                    {
                        portFolioCompanydetail = list.FirstOrDefault(x => x.CompanyName.ToLower().Trim() == detail.CompanyName);
                        if (portFolioCompanydetail != null)
                        {
                            detail = portFolioCompanydetail;

                            detail.PortfolioCompanyEmployeeModel = new PortfolioCompanyEmployeeModel
                            {
                                Designation = new DesignationModel()
                            };
                            if (detail.PCEmployees == null) detail.PCEmployees = new List<PortfolioCompanyEmployeeModel>();
                            detail.IsActive = true;
                            detail.StatusDescription = string.Empty;
                        }
                        else
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(" Company Name  " + detail.CompanyName + " is not mapped with any provided company name. ")).ToString();
                            detail.IsValid = false;
                        }
                    }

                    detail.SheetName = sheetName;

                    col++;
                }
                #endregion [Validate PortFolio Company Name]

                if (portFolioCompanydetail != null)
                {
                    #region [EmployeeName]
                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "employee name")
                    {
                        detail.PortfolioCompanyEmployeeModel.EmployeeName = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(detail.PortfolioCompanyEmployeeModel.EmployeeName))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Firm_EmployeeNameRequired)).ToString();
                            detail.IsValid = false;

                        }
                        else
                        {
                            if (detail.PortfolioCompanyEmployeeModel.EmployeeName.Length > 50)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Firm_EmployeeNameLengthExceeded)).ToString();
                                detail.IsValid = false;
                            }
                            else if (!Regex.IsMatch(detail.PortfolioCompanyEmployeeModel.EmployeeName, @"^[a-zA-Z\s]+$", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250)))
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Firm_EmployeeNameNotValid)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                detail.PortfolioCompanyEmployeeModel.EmployeeName = Convert.ToString(workSheet.Cells[row, col].Text);
                            }
                        }
                    }
                    #endregion [EmployeeName]

                    #region [EmployeeEmail]

                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "employee email")
                    {
                        detail.PortfolioCompanyEmployeeModel.EmailId = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(detail.PortfolioCompanyEmployeeModel.EmailId))
                        {

                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.EmailRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            var isValidEmail = Regex.IsMatch(detail.PortfolioCompanyEmployeeModel.EmailId,
                                @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                                @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-\w]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                                RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));

                            if (!isValidEmail)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.InvalidEmailAddress)).ToString();
                                detail.IsValid = false;
                            }
                            else if (detail.PCEmployees.Exists(z => z.EmailId.ToLower().Trim() == detail.PortfolioCompanyEmployeeModel.EmailId.ToLower().Trim()))
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Utility_DuplicateEmployee)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                detail.PortfolioCompanyEmployeeModel.EmailId = Convert.ToString(workSheet.Cells[row, col].Text);
                            }
                        }
                    }
                    #endregion [EmployeeEmail]

                    #region [Designation]
                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "designation")
                    {
                        detail.PortfolioCompanyEmployeeModel.Designation.Designation = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (!string.IsNullOrEmpty(detail.PortfolioCompanyEmployeeModel.Designation.Designation))
                        {
                            if (desigModel != null)
                            {
                                var designationDetail = desigModel.FirstOrDefault(x => x.Designation.ToLower().Trim() == detail.PortfolioCompanyEmployeeModel.Designation.Designation);
                                if (designationDetail == null)
                                {
                                    detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Firm_NoDesignationFound)).ToString();
                                    detail.IsValid = false;
                                }
                                else
                                {
                                    detail.PortfolioCompanyEmployeeModel.Designation = designationDetail;
                                }
                            }
                        }
                        else
                        {
                            detail.PortfolioCompanyEmployeeModel.Designation = null;
                        }
                    }
                    #endregion [Designation]

                    #region [Education]
                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "education")
                    {
                        detail.PortfolioCompanyEmployeeModel.Education = Convert.ToString(workSheet.Cells[row, col].Text);
                    }

                    #endregion [Education]

                    #region [PastExperience]
                    if (workSheet.Cells[1, col].Text.ToLower().Trim() == "past experience")
                    {
                        detail.PortfolioCompanyEmployeeModel.PastExperience = Convert.ToString(workSheet.Cells[row, col].Text);
                    }
                    #endregion [PastExperience]

                }
            }

            if (detail.IsValid)
            {
                detail.PCEmployees.Add(detail.PortfolioCompanyEmployeeModel);
            }
            Employeelst.Add(detail.StatusDescription);

        }

        if (detail != null)
        {
            if (!String.IsNullOrEmpty(detail.StatusDescription)) { ErrorListHandler errorObj = new ErrorListHandler(); errorObj.ErrorDetailsWithStatus(sheetName, ref status, null, null, null, Employeelst); return new List<BulkUploadPortFolioCompanyDetails>(); } else { return list; }
        }
        return list;
    }

    private List<PortfolioCompanyProfitabilityModel> ValidateQuarterlyPortFolioCompanyBulkData(ExcelWorksheet workSheet, int createdBy, List<BulkUploadPortFolioCompanyDetails> list, Status status)
    {
        BulkUploadPortFolioCompanyDetails detail = new BulkUploadPortFolioCompanyDetails();
        List<BulkUploadPortFolioCompanyDetails> detailList = new List<BulkUploadPortFolioCompanyDetails>();

        List<string> quaterErrorlst = new List<string>();

        #region [One time fetch to minimize API hits]

        var start = workSheet.Dimension.Start;
        var end = workSheet.Dimension.End;

        var sheetName = Convert.ToString(workSheet);

        #endregion [One time fetch to minimize API hits]

        for (int row = start.Row + 1; row <= end.Row; row++)
        {

            detail = new BulkUploadPortFolioCompanyDetails();
            PortfolioCompanyProfitabilityModel pcDetail = new PortfolioCompanyProfitabilityModel();
            detail.IsValid = true;
            detail.CreatedOn = DateTime.Now;
            detail.CreatedBy = createdBy;

            pcDetail.CreatedOn = DateTime.Now;
            pcDetail.CreatedBy = createdBy;
            pcDetail.ModifiedBy = createdBy;
            pcDetail.ModifiedOn = DateTime.Now;
            pcDetail.IsActive = true;



            for (int col = start.Column; col <= end.Column; col++)
            {

                switch (workSheet.Cells[start.Row, col].Text.ToLower().Trim())
                {
                    case "company name":
                        #region [PortFolio Company]

                        detail.CompanyName = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(detail.CompanyName))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.Deal_PortfolioCompanyRequired)).ToString();
                            detail.IsValid = false;
                        }

                        #endregion [PortFolio Company]
                        break;

                    case "year":
                        #region [Year]

                        int yearCheck = 0;
                        var year = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(year))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.DealQuarter_QuarterlyYearRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            bool result = int.TryParse(year, out yearCheck);

                            if (!result)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.DealQuarter_InvalidNumericValueForQuarterlyYear)).ToString();
                                detail.IsValid = false;
                            }
                            else if (year.Length > 4)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.DealQuarter_YearLengthExceeded)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                pcDetail.Year = yearCheck;
                            }

                        }

                        #endregion [Year]
                        break;

                    case "quarter":
                        #region [Quarter]

                        var quarter = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(quarter))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.DealQuarter_QuarterRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            if (quarter == "q1" || quarter == "q2" || quarter == "q3" || quarter == "q4")
                            {
                                if (detailList?.Any() == true && detailList.Exists(x => x.PortfolioCompanyProfitabilityList.Exists(y => y.Year == pcDetail.Year && y.Quarter.ToLower().Trim() == quarter) && x.CompanyName.Trim().ToLower() == detail.CompanyName.Trim().ToLower()))
                                {
                                    detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_YearWithSameQuarterExistsInSheet)).ToString();
                                    detail.IsValid = false;
                                }
                                else
                                {
                                    pcDetail.Quarter = workSheet.Cells[row, col].Text;
                                    int quarterNumber = Convert.ToInt32(quarter.Substring(1));
                                    int currentQuater = DateTime.Now.Date.GetQuarter();
                                    DateTime currentQuaterLastDate = Common.GetLastDayOfQuarter(DateTime.Now.Year, currentQuater);

                                    DateTime providedQuaterYearLastDate = Common.GetLastDayOfQuarter(pcDetail.Year, quarterNumber);
                                    if (providedQuaterYearLastDate > currentQuaterLastDate)
                                    {
                                        detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolio_ValuationDateLessThanCurrentQuarter)).ToString();
                                        detail.IsValid = false;
                                    }

                                }
                            }
                            else
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.DealQuarter_InvalidQuarterValue)).ToString();
                                detail.IsValid = false;
                            }
                        }

                        #endregion [Quarter]
                        break;

                    case "ebitda":
                        #region [EBITDA]

                        decimal ebitda = 0;
                        var ebitdaValue = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(ebitdaValue))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_EbitdaRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else if (ebitdaValue.Length > 15)
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_EbitdaLengthExceeded)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            bool result = decimal.TryParse(ebitdaValue, out ebitda);

                            if (!result)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_InvalidNumericValueForEbitda)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                pcDetail.EBITDA = Math.Round(ebitda, 2);
                            }

                        }

                        #endregion [EBITDA]
                        break;

                    case "net debt":
                        #region [Net Debt]

                        decimal netDebtValueCheck = 0;
                        var netDebtValue = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(netDebtValue))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_NetDebtRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else if (netDebtValue.Length > 15)
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_NetDebtLengthExceeded)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            bool result = decimal.TryParse(netDebtValue, out netDebtValueCheck);

                            if (!result)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_InvalidNumericValueForNetDebt)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                pcDetail.NetDebt = Math.Round(netDebtValueCheck, 2);
                            }

                        }

                        #endregion [Net Debt]
                        break;

                    case "revenue":
                        #region [Revenue]

                        decimal revenueValueCheck = 0;
                        var revenueValue = Convert.ToString(workSheet.Cells[row, col].Text.ToLower().Trim());

                        if (string.IsNullOrEmpty(revenueValue))
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_RevenueRequired)).ToString();
                            detail.IsValid = false;
                        }
                        else if (revenueValue.Length > 15)
                        {
                            detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_RevenueLengthExceeded)).ToString();
                            detail.IsValid = false;
                        }
                        else
                        {
                            bool result = decimal.TryParse(revenueValue, out revenueValueCheck);

                            if (!result)
                            {
                                detail.StatusDescription = new StringBuilder(detail.StatusDescription + HtmlUtility.ToListItem(Messages.PortFolioCompany_InvalidNumericValueForRevenue)).ToString();
                                detail.IsValid = false;
                            }
                            else
                            {
                                pcDetail.Revenue = Math.Round(revenueValueCheck, 2);
                            }

                        }
                        #endregion [Revenue]
                        break;

                }
            }

            detail.PortfolioCompanyProfitabilityList.Add(pcDetail);
            detailList.Add(detail);
            quaterErrorlst.Add(detail.StatusDescription);

        }

        var companyList = detailList.Select(x => x.CompanyName.Trim().ToLower()).Distinct().ToList();

        detailList.SelectMany(x => x.PortfolioCompanyProfitabilityList).Where(x => !string.IsNullOrEmpty(x.Quarter)).ToList();

        PortfolioCompanyListModel listModel = null;
        PortfolioCompanyProfitabilityListModel profitabilityListModel = new();
        if (profitabilityListModel == null)
        {
            quaterErrorlst.Add(listModel?.Status.Message);
            detailList = detailList.Select(x => { x.IsValid = false; return x; }).ToList();
        }
        else
        {
            foreach (var companyDetail in detailList)
            {
                var index = detailList.IndexOf(companyDetail);

                var matchedDbDetail = listModel.PortfolioCompanyList.FirstOrDefault(x => x.CompanyName.ToLower().Trim() == companyDetail.CompanyName.Trim().ToLower());
                if (matchedDbDetail != null)
                {
                    companyDetail.PortfolioCompanyID = matchedDbDetail.PortfolioCompanyID;
                    companyDetail.PortfolioCompanyProfitabilityList = companyDetail.PortfolioCompanyProfitabilityList.Select(x =>
                    {
                        x.PortfolioCompanyID = matchedDbDetail.PortfolioCompanyID;
                        return x;
                    }).ToList();
                }
                else
                {
                    var company = list?.FirstOrDefault(x => x.CompanyName.ToLower().Trim() == companyDetail.CompanyName.Trim().ToLower());
                    if (company != null)
                    {
                        company.PortfolioCompanyProfitabilityList = companyDetail.PortfolioCompanyProfitabilityList;
                    }
                    else
                    {
                        quaterErrorlst[index] = new StringBuilder(quaterErrorlst[index] + HtmlUtility.ToListItem(Messages.PortFolioCompany_CompanyNotExistsinDB)).ToString();
                        companyDetail.IsValid = false;
                    }
                }

                var matchedDbHolding = profitabilityListModel.PortfolioCompanyProfitabilityList.FirstOrDefault(p => companyDetail.PortfolioCompanyProfitabilityList.Exists(profitability => p.Quarter != null && p.Year == profitability.Year && p.Quarter.ToLower().Trim() == profitability.Quarter.ToLower().Trim() && p.PortfolioCompanyID == profitability.PortfolioCompanyID));
                if (matchedDbHolding != null)
                {
                    quaterErrorlst[index] = new StringBuilder(quaterErrorlst[index] + HtmlUtility.ToListItem(Messages.PortFolioCompany_YearQuarterAlreadyExists)).ToString();
                    companyDetail.IsValid = false;
                }
            }
        }

        var resultList = detailList.SelectMany(x => x.PortfolioCompanyProfitabilityList).Where(x => x.PortfolioCompanyID > 0).ToList();

        if (quaterErrorlst.Count == 0) { status.Message = status.Message + "<br/>  SheetName :- " + sheetName + "  <br/>" + Messages.Utility_CheckMissingFields + "<br/>"; status.Code = "info"; return new List<PortfolioCompanyProfitabilityModel>(); } else if (detailList.Exists(x => !x.IsValid)) { ErrorListHandler errorObj = new ErrorListHandler(); errorObj.ErrorDetailsWithStatus(sheetName, ref status, null, null, null, quaterErrorlst); return new List<PortfolioCompanyProfitabilityModel>(); } else { return resultList; }
    }

    #endregion Bulk Upload

    [HttpPost]
    [Route("portfolio-company/export")]
    [UserFeatureAuthorize((int)Features.PortfolioCompany)]
    [ValidationFilter]
    public FileResult ExportCompanyList([FromBody] PortfolioCompanyFilter filter)
    {

        var guid = Guid.NewGuid();
        var subPath = _injectedParameters.GlobalConfigurations.ExportFileUploadPath + guid + Path.DirectorySeparatorChar;

        var model = new PortfolioCompanyListModel();
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (service != null)
            model = service.Value.GetPortfolioCompanyList(filter);
        if (model?.PortfolioCompanyList?.Any() == true)
        {
            var chartDetails = model.PortfolioCompanyList.ConvertAll(x => new
            {
                x.CompanyName,
                x.Website,
                x.Status,
                Headquarter = x.GeographicLocations.Where(y => y.IsHeadquarter).Select(y => (y.City != null ? y.City.City + ", " : "") + (y.State != null ? y.State.State + ", " : "") + (y.Country != null ? y.Country.Country + ", " : "") + (y.Region != null ? y.Region.Region : "")).Select(y => string.IsNullOrEmpty(y) ? "" : y.Trim().TrimEnd(',')).FirstOrDefault(),
                x.SectorDetail?.Sector,
                x.SubSectorDetail?.SubSector,
                x.StockExchange_Ticker,
                ReportingCurreny = x.ReportingCurrencyDetail?.CurrencyCode,
                x.BusinessDescription,
                x.MasterCompanyName

            });

            var chartTable = chartDetails.ToDataTable("PortfolioCompanies");

            foreach (DataColumn column in chartTable.Columns)
            {
                column.ColumnName = column.ColumnName.Replace("_", "").Replace(" ", "");
                column.ColumnName = column.ColumnName.PutSpaceBeforeCamelLetters();
                if (column.ColumnName == "Stock Exchange Ticker")
                    column.ColumnName = "Stock Exchange & Ticker";
            }

            DataSet ds = new DataSet();
            ds.Tables.Add(chartTable);

            string fileName = ExcelExport.ExportToExcel(ds, subPath, "PortfolioCompanyList", true);
            return new ExcelContentResult(fileName, Path.Combine(subPath, "PortfolioCompanyList", fileName));
        }

        return null;
    }
    [HttpPost("update/company/encrypt")]
    public async Task<IActionResult> UpdateEncryptPortfolioCompanyIds()
    {
        return Ok(await _portfolioCompanyServices.FirstOrDefault()?.Value.UpdateEncryptCompanyId());
    }
    [HttpPost("update/fund/encrypt")]
    public async Task<IActionResult> UpdateEncryptFundIds()
    {
        return Ok(await _portfolioCompanyServices.FirstOrDefault()?.Value.UpdateEncryptFundId());
    }
    [HttpPost("update/deal/encrypt")]
    public async Task<IActionResult> UpdateEncryptDealIds()
    {
        return Ok(await _portfolioCompanyServices.FirstOrDefault()?.Value.UpdateEncryptDealId());
    }
    [HttpGet("portfolioCompany/portfolioCompanyTypeList/{featureId}")]
    [UserFeatureAuthorize((int)Features.PortfolioCompany)]
    public async Task<IActionResult> GetPortfolioCompanyTypeListDetails(string featureId)
    {
        var decryptedPortfolioCompanyId = _injectedParameters.Encryption.Decrypt(featureId);
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (service != null)
        {
            List<CustomPortfolioGroupList> groupLists = await service.Value.GetPortfolioCompanyTypeListDetails(Convert.ToInt32(decryptedPortfolioCompanyId));
            if (groupLists.Any())
            {
                return Ok(groupLists);
            }
            else
            {
                return NoContent();
            }
        }
        else
        {
            return NoContent();
        }
    }


    /// <summary>
    /// Retrieves the draft details of a portfolio company based on the provided ID.
    /// </summary>
    /// <param name="portfolioCompanyId">The ID of the portfolio company.</param>
    /// <returns>An asynchronous task that represents the operation and holds the action result.</returns>
    [Route("portfolioCompany/getdraftbyid")]
    [HttpPost]
    [ValidationFilter]
    [UserFeatureAuthorize((int)Features.PortfolioCompany, (int)Features.ESG)]
    public async Task<IActionResult> GetPortfolioCompanyDraftDetails([FromBody] StringValueModel portfolioCompanyId)
    {
        var decryptedPortfolioCompanyId = _injectedParameters.Encryption.Decrypt(portfolioCompanyId.Value);
        var pcStaticFields = await GetAllSubFieldsOfPortfolioCompany();
        List<SubPageDetailModel> subPageDetailList = await _pageDetailConfigService.GetActiveSubPageSectionByPageId((int)PageConfigurationFeature.PortfolioCompany);
        List<PageFieldValueModel> customFields = await GetCustomStaticFieldValue(Convert.ToInt32(decryptedPortfolioCompanyId));
        var commentaryPeriod = await _pageFieldValueDraftService.GetCommentaryPeriodMSubSectionField();
        var result = new PortfolioCompanyModel();
        var _PageFieldModelDTO = new List<PageFieldValueModel>();
        var service = _portfolioCompanyServices.FirstOrDefault();
        if (service != null)
        {
            result = await service.Value.GetPortfolioCompanyDraftById(Convert.ToInt32(decryptedPortfolioCompanyId));
            _PageFieldModelDTO = service.Value.GetPageFieldModelDTOtDetails(pcStaticFields).ToList();
        }
        if (result?.PortfolioCompanyID > 0)
        {
            _PageFieldModelDTO.ForEach(item =>
            {

                if (item.Name == Constants.SignificantEvents)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.SignificantEventsSectionData == null ? item.Value : result.CommentaryData.SignificantEventsSectionData.Item4;
                if (item.Name == Constants.AssessmentPlan)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.AssessmentSectionData == null ? item.Value : result.CommentaryData.AssessmentSectionData.Item4;
                if (item.Name == Constants.ImpactHighlights)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.ImpactSectionData == null ? item.Value : result.CommentaryData.ImpactSectionData.Item4;
                if (item.Name == Constants.ExitPlan)
                    item.Value = result.CommentaryData == null || result.CommentaryData?.ExitPlansSectionData == null ? item.Value : result.CommentaryData.ExitPlansSectionData.Item4;
                var target = customFields.Find(x => x.FieldID == item.FieldID);
                if (target != null)
                    item.Value = target.Value;
            });

            return Ok(new { SubPageList = subPageDetailList, FieldValueList = _PageFieldModelDTO, CompanyDetails = result,CommentaryPeriod=commentaryPeriod });
        }
        else
            return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status204NoContent, Messages.NoRecordFound);
    }
    [Route("portfolio-company/logo")]
    [HttpPost]
    [ValidationFilter]
    public async Task<IActionResult> GetPortfolioCompanyLogo([FromBody] StringValueModel portfolioCompanyId)
    {
        var decryptedPortfolioCompanyId = _injectedParameters.Encryption.Decrypt(portfolioCompanyId.Value);
        var service = _portfolioCompanyServices.FirstOrDefault();
        var result = new PortfolioCompanyDetails();
        if (service != null)
        {
            result = await service.Value.FetchCompanyDetailsById(Convert.ToInt32(decryptedPortfolioCompanyId));
            result.ImagePath = await GetCompanyLogo(Convert.ToInt32(decryptedPortfolioCompanyId), result.ImagePath);
        }
        return Ok(result);
    }
}

