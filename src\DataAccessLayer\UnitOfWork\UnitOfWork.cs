using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DataCollection;
using DataAccessLayer.Models.Documents;
using DataAccessLayer.Models.MonthlyReport;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.Models.Workflow;
using Microsoft.EntityFrameworkCore;
using DataAccessLayer.Models.LpReport;
using DataAccessLayer.Models.GrowthReport;
using DataAccessLayer.Models.SDG;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.CLO.CLOPageConfig;
using DataAccessLayer.Models.CLO.Clo_Details;
using DataAccessLayer.Models.CLO.CloCommentries;
using DataAccessLayer.Models.CLO.InvestmentCompany;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.Models.Fund;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.Models.DashboardTracker;

namespace DataAccessLayer.UnitOfWork
{

    [ExcludeFromCodeCoverage]
    public partial class UnitOfWork : IDisposable, IUnitOfWork
    {
        #region Private member variables...

        private readonly DBEntities _context = null;
        private IGenericRepository<CommentaryDetails> _commentaryDetails;
        private IGenericRepository<Mapping_ImpactKPI_Order> _Mapping_ImpactKPI_Order;
        private IGenericRepository<MappingPortfolioOperationalKpi> _Mapping_OperationalKpi;
        private IGenericRepository<M_CompanyKpi> _m_CompanyKPI;
        private IGenericRepository<MImpactKpi> _m_ImpactKPI;
        private IGenericRepository<PCInvestmentKpiQuarterlyValue> _pcInvestmentKpiQuarterlyValue;
        private IGenericRepository<M_AccountType> _m_AccountType;
        private IGenericRepository<M_Country> _m_Country;
        private IGenericRepository<MFinancialKpi> _m_FinancialKPI;
        private IGenericRepository<M_OperationalKPI> _m_OperationalKPI;
        private IGenericRepository<M_Region> _m_Region;
        private IGenericRepository<M_StockExchange> _m_StockExchange;
        private IGenericRepository<M_Sector> _m_Sector;
        private IGenericRepository<M_FinancialStatus> _m_FinancialStatus;
        private IGenericRepository<M_OwnershipStatus> _m_OwnershipStatus;
        private IGenericRepository<M_InvestmentStage> _m_InvestmentStage;
        private IGenericRepository<PortfolioCompanyDetails> _portFolioCompanyDetail;
        private IGenericRepository<M_SubSector> _m_SubSector;
        private IGenericRepository<PortfolioCompanyProfitabilityDetails> _portFolioCompanyProfitabilityDetail;
        private IGenericRepository<Mapping_Locations> _mapping_Location;
        private IGenericRepository<M_Currency> _m_Currency;
        private IGenericRepository<Mapping_CountryCurrency> _mapping_CountryCurrency;
        private IGenericRepository<M_FirmType> _m_Firmtype;
        private IGenericRepository<M_State> _m_State;
        private IGenericRepository<M_City> _m_City;
        private IGenericRepository<M_Groups> _m_Groups;
        private IGenericRepository<M_FundingType> _m_FundingType;
        private IGenericRepository<CompanyFundingDetails> _m_CompanyFundingDetails;
        private IGenericRepository<FirmDetails> _firmDetail;
        private IGenericRepository<M_Designation> _m_Designation;
        private IGenericRepository<EmployeeDetails> _employeeDetail;
        private IGenericRepository<M_Strategy> _m_Strategy;
        private IGenericRepository<FundDetails> _fundDetail;
        private IGenericRepository<Mapping_FirmFund> _mapping_FirmFund;
        private IGenericRepository<PortfolioCompanyFundHoldingDetails> _portfolioCompanyFundHoldingDetail;
        private IGenericRepository<DealDetails> _dealDetail;
        private IGenericRepository<M_Geography> _m_Geography;
        private IGenericRepository<GlobalVariables> _globalVariables;
        private IGenericRepository<UserDetails> _userDetail;
        private IGenericRepository<Mapping_FirmGeographicLocation> _firmGeographyDetail;
        private IGenericRepository<Mapping_FirmEmployee> _firmEmployeeDetail;
        private IGenericRepository<Mapping_PCEmployee> _pcEmployeeDetail;
        private IGenericRepository<Mapping_PCGeographicLocation> _pcGeographyDetail;
        private IGenericRepository<Mapping_FirmHeadquarterLocation> _firmHeadquarterDetail;
        private IGenericRepository<FundTrackRecord> _fundTrackRecord;
        private IGenericRepository<FundIngestion> _fundIngestion;
        private IGenericRepository<PipelineDetails> _pipelineDetail;
        private IGenericRepository<M_DealBoardSeat> _m_DealBoardSeat;
        private IGenericRepository<M_DealExitMethod> _m_DealExitMethod;
        private IGenericRepository<M_DealInvestmentStage> _m_DealInvestmentStage;
        private IGenericRepository<M_DealSecurtyType> _m_DealSecurtyType;
        private IGenericRepository<M_DealSourcing> _m_DealSourcing;
        private IGenericRepository<M_DealTransactionRole> _m_DealTransactionRole;
        private IGenericRepository<M_DealValuationMethodology> _m_DealValuationMethodology;
        private IGenericRepository<Mapping_UserGroup> _m_UserGroups;
        private IGenericRepository<Mapping_GroupFeature> _m_GroupFeatures;
        private IGenericRepository<M_PipelineStatus> _m_PipelineStatus;
        private IGenericRepository<M_FundHoldingStatus> _m_FundHoldingStatus;
        private IGenericRepository<M_Features> _m_Feature;
        private IGenericRepository<DynamicQueries> _dynamicQuery;
        private IGenericRepository<DynamicQueriesCopy> _dynamicQueryCopy;
        private IGenericRepository<CashFlowFileLogs> _cashFlowRepo;
        private IGenericRepository<M_SectorwiseKPI> _m_SectorwiseKPI;
        private IGenericRepository<PortfolioCompanySectorwiseKPIValues> _portfolioCompanySectorwiseKPIValue;
        private IGenericRepository<PCOperationalKPIMonthlyValue> _pcOperationalKPIMonthlyValue;
        private IGenericRepository<PCFinancialKPIMonthlyValue> _pcFinancialKPIMonthlyValue;
        private IGenericRepository<PcCompanywiseKpiMonthlyValue> _pcCompanywiseKPIMonthlyValue;
        private IGenericRepository<PCSectorwiseKPIMonthlyValue> _pcSectorwiseKPIMonthlyValue;
        private IGenericRepository<PortfolioCompanySectorwiseKPIMonthly> _portfolioCompanySectorwiseKPIMonths;
        private IGenericRepository<View_LocationMapping> _view_LocationMapping;
        private IGenericRepository<View_RegionCountryMapping> _view_RegionCountryMapping;
        private IGenericRepository<EmailTemplate> _emailTemplateRepo;
        private IGenericRepository<AuditLog> _auditLogRepo;
        private IGenericRepository<CurrencyRates> _currencyRateRepo;
        private IGenericRepository<ApiCurrencyRates> _apiCurrencyRateRepo;
        private IGenericRepository<LoginDetails> _loginAttemptRepo;
        private IGenericRepository<CompanySummaryDetails> _companySummaryDetail;
        private IGenericRepository<KPIValuesFileLogs> _kpiValuesFileRepo;
        private IGenericRepository<M_Kpitypes> _m_kpiTypes;
        private IGenericRepository<MappingPortfolioCompanyKpi> _portfolioCompanyKpiMapping;
        private IGenericRepository<MappingPortfolioInvestmentKpi> _portfolioInvestmentKpiMapping;
        private IGenericRepository<DataAuditLog> _dataAuditlogRepo;
        private IGenericRepository<PortfolioCompanyOperationalKPIQuarter> _portfolioCompanyOperationalKPIQuarter;
        private IGenericRepository<PortfolioCompanyOperationalKPIValue> _portfolioCompanyOperationalKPIValue;
        private IGenericRepository<BalanceSheetValues> _pcBalanceSheetValues;
        private IGenericRepository<M_ProfitAndLoss_LineItems> _m_ProfitAndLoss_LineItems;
        private IGenericRepository<Mapping_CompanyProfitAndLossLineItems> _mapping_CompanyProfitAndLossLineItems;
        private IGenericRepository<M_BalanceSheet_LineItems> _m_BalanceSheet_LineItems;
        private IGenericRepository<Mapping_CompanyBalanceSheetLineItems> _mapping_CompanyBalanceSheetLineItems;
        private IGenericRepository<ProfitAndLossValues> _pcProfitAndLossValues;
        private IGenericRepository<PCFinancialsValues> _pcPCFinancialsValues;
        private IGenericRepository<M_CashFlow_LineItems> _M_CashFlow_LineItems;
        private IGenericRepository<Mapping_CompanyCashFlowLineItems> _Mapping_CompanyCashFlowLineItems;
        private IGenericRepository<CashFlowValues> _CashFlowValues;
        private IGenericRepository<PCCompanyKpiMonthlyValue> _pcCompanyKpiMonthlyValue;
        private IGenericRepository<PcImpactKpiQuarterlyValue> _pcImpactKpiQuarterlyValue;
        private IGenericRepository<PCTradingRecordValues> _pcTradingRecordValues;//BFB-1140
        private IGenericRepository<M_InvestmentKpi> _m_InvestmentKPI;
        private IGenericRepository<M_SubFeature> _m_SubFeature;
        private IGenericRepository<Mapping_SubFeatureAction> _mapping_SubFeatureAction;
        private IGenericRepository<Mapping_UserSubFeature> _mapping_SubFeature;
        private IGenericRepository<ImpactKPI_AnnualHistoricalData> _ImpactKPI_AnnualHistoricalData;
        private IGenericRepository<M_StandingDataItems> _M_StandingDataItems;
        private IGenericRepository<DocumentType> _DocumentTypes;
        private IGenericRepository<Document> _Documents;
        private IGenericRepository<DocumentStatus> _DocumentStatus;
        private IGenericRepository<UserReport> _UserReportsRepository;
        private IGenericRepository<M_LPReportConfiguration> _M_LPReportConfiguration;
        private IGenericRepository<M_FundReportConfiguration> _M_FundReportConfiguration;
        private IGenericRepository<Mapping_LPReportConfiguration> _Mapping_LPReportConfiguration;
        private IGenericRepository<M_KpiModules> _M_KpiModules;
        private IGenericRepository<M_ValueTypes> _M_ValueTypes;
        private IGenericRepository<M_MasterKpis> _M_MasterKpis;
        private IGenericRepository<MasterKpiAuditLog> _masterKpiAuditLogRepository;
        private IGenericRepository<ImpactKpiAuditLog> _impactKpiAuditLogRepository;
        private IGenericRepository<Mapping_Kpis> _Mapping_Kpis;
        private IGenericRepository<PCMasterKpiValues> _PCMasterKpiValues;
        private IGenericRepository<M_Methodology> _M_Methodology;
        private IGenericRepository<M_Notification> _M_Notification;
        private IGenericRepository<M_NotificationModules> _M_NotificationModules;
        private IGenericRepository<BalanceSheetICCaseValues> _BalanceSheetICCaseValues;
        private IGenericRepository<BalanceSheetForecastData> _BalanceSheetForecastData;
        private IGenericRepository<ProfitAndLossIccaseValues> _ProfitAndLossIccaseValues;
        private IGenericRepository<ProfitAndLossForecastData> _ProfitAndLossForecastData;
        private IGenericRepository<CashFlowICCaseValues> _CashFlowICCaseValues;
        private IGenericRepository<CashFlow_ForecastData> _CashFlow_ForecastData;
        private IGenericRepository<FinancialValueTypes> _FinancialValueTypes;
        private IGenericRepository<PageConfiguration> _ReportPageConfiguration;
        private IGenericRepository<ReportTemplateConfiguration> _ReportTemplateConfiguration;
        private IGenericRepository<MappingWorkflowStatus> _MappingWorkflowStatus;
        private IGenericRepository<MWorkflowStatus> _MWorkflowStatus;
        private IGenericRepository<MasterGroups> _MasterGroupsRepository;
        private IGenericRepository<WorkflowRequest> _WorkflowRequest;
        private IGenericRepository<MappingWorkflowRequest> _MappingWorkflowRequest;
        private IGenericRepository<MappingWorkflowRequestComments> _MappingWorkflowRequestComments;
        private IGenericRepository<PortfolioCompanyDetailsDraft> _PortfolioCompanyDetailsDraft;
        private IGenericRepository<ReportTemplateConfigurationMapping> _ReportTemplateConfigurationMapping;
        private IGenericRepository<PeriodTypes> _PeriodTypesRepo;
        private IGenericRepository<MappingPCGeographicLocationDraft> _MappingPCGeographicLocationDraft;
        private IGenericRepository<MappingPCEmployeeDraft> _MappingPCEmployeeDraft;
        private IGenericRepository<WorkflowHistory> _WorkflowHistory;
        private IGenericRepository<EmployeeDetailsDraft> _EmployeeDetailsDraft;
        private IGenericRepository<MappingWorkflowStatusGroup> _MappingWorkflowStatusGroup;
        private IGenericRepository<M_Action> _MAction;
        private IGenericRepository<PCCompanyKpiMonthlyValueDraft> _PCCompanyKpiMonthlyValueDraft;
        private IGenericRepository<PCInvestmentKpiQuarterlyValueDraft> _PCInvestmentKpiQuarterlyValueDraft;
        private IGenericRepository<DraftAuditLog> _DraftAuditLog;
        private IGenericRepository<ReportTypes> _ReportTypes;
        private IGenericRepository<PortfolioCompanyOperationalKPIQuartersDraft> _PCOperationalKpiQuarterDraftRepository;
        private IGenericRepository<Models.Workflow.PortfolioCompanyOperationalKPIValuesDraft> _PCOperationalKpiValuesDraftRepository;
        private IGenericRepository<FundReportTemplateConfiguration> _FundReportTemplateConfiguration;
        private IGenericRepository<FundReportTemplateConfigurationMapping> _FundReportTemplateConfigurationMapping;
        private IGenericRepository<WorkflowMappingHistory> _WorkflowMappingHistory;
        private IGenericRepository<M_PageDetails> _PageDetailsRepository;
        private IGenericRepository<M_SubPageDetails> _SubPageDetailsRepository;
        private IGenericRepository<M_SubPageFields> _SubPageFieldsRepository;
        private IGenericRepository<PageConfigurationFieldValue> _PageConfigurationFieldValueRepository;

        private IGenericRepository<PageConfigurationTrackRecordFieldValue> _PageConfigurationTrackRecordFieldValueRepository;
        private IGenericRepository<M_TrackRecordDataTypes> _M_TrackRecordDataTypesRepository;
        private IGenericRepository<MInvestorType> _M_InvestorTypeRepository;
        private IGenericRepository<InvestorType> _InvestorTypeRepositoryRepository;
        private IGenericRepository<FundInvestors> _fundInvestorsRepository;
        private IGenericRepository<MappingInvestorGeographicLocation> _MappingInvestor_GeographicLocationRepositoryRepository;
        private IGenericRepository<InvestorValuation> _InvestorValuation;
        private IGenericRepository<CompanyKPIForecastValues> _CompanyKPIForecastValues;
        private IGenericRepository<UnstructuredHistory> UnstructuredMappingHistory;
        private IGenericRepository<PageConfigurationFieldValueDraft> _PageConfigurationFieldValueDraftRepository;
        private IGenericRepository<FootNotes> _FootNotesRepository;
        private IGenericRepository<InternalReportConfiguration> _InternalReportConfigurationRepository;
        private IGenericRepository<MappingInternalReportConfiguration> _MappingInternalReportConfigurationRepository;
        private IGenericRepository<InternalReportFundPreference> _InternalReportFundPreferenceRepository;
        private IGenericRepository<InternalReportSectionPreference> _InternalReportSectionPreferenceRepository;
        private IGenericRepository<InternalReportValueTypePreference> _InternalReportValueTypePreferenceRepository;
        private IGenericRepository<InternalReportPeriodTypePreference> _InternalReportPeriodTypePreferenceRepository;
        private IGenericRepository<InternalReportCalculationPreference> _InternalReportCalculationPreferenceRepository;
        private IGenericRepository<MPeriod> _MPeriodRepository;
        private IGenericRepository<MCalculation> _MCalculationRepository;
        private IGenericRepository<MInternalReportExcelTemplate> _MInternalReportExcelTemplateRepository;
        private IGenericRepository<InternalReportExcelTemplatePreference> _InternalReportExcelTemplatePreferenceRepository;
        private IGenericRepository<InternalReportPeriodConfigPreference> _InternalReportPeriodConfigPreferenceRepository;
        private IGenericRepository<ReportDownloadType> _ReportDownloadTypeRepository;
        private IGenericRepository<ConsolidatedReportConfiguration> _ConsolidatedReportConfigurationRepository;
        private IGenericRepository<MappingConsolidatedReportConfiguration> _MappingConsolidatedReportConfigurationRepository;
        private IGenericRepository<ConsolidatedReportPreference> _ConsolidatedReportPreferenceRepository;
        private IGenericRepository<MConsolidatedReportExcelTemplate> _MConsolidatedReportExcelTemplateRepository;
        private IGenericRepository<PcMasterKpiValueDraft> _PcMasterKpiValueDraftRepository;
        private IGenericRepository<PortfolioCompanyOperationalKPIValuesDraft> _PortfolioCompanyOperationalKPIValuesDraft;
        private IGenericRepository<MGroupingList> _MGroupingListRepository;
        private IGenericRepository<PortfolioCompanyCustomListDetails> _PortfolioCompanyCustomListDetailsRepository;
        private IGenericRepository<ConsolidatedExcelTemplatePreference> _ConsolidatedExcelTemplatePreferenceRepository;
        private IGenericRepository<PageConfigurationCommentaryCustomFieldValue> _PageConfigurationCommentaryCustomFieldValueRepository;
        private IGenericRepository<ValuationModelDetail> _ValuationModelDetail;
        private IGenericRepository<MValuationFinancialKpi> _MValuationFinancialKpiRepository;
        private IGenericRepository<BackgroundJobReportHistory> _BackgroundJobReportHistoryRepository;
        private IGenericRepository<MSubSectionFields> _MSubSectionFields;
        private IGenericRepository<DataAnalytics> _DataAnalytics;
        private IGenericRepository<Valuation_ImpliedEVRecord> _ImpliedEVRecordRepository;
        private IGenericRepository<Valuation_AdjustmentDetail> _AdjustmentDetailRepository;
        private IGenericRepository<Valuation_TargetCompanyKPIRecord> _TargetCompanyKPIRecordRepository;
        private IGenericRepository<Valuation_UnselectedRecords> _Valuation_UnselectedRecordsRepository;
        private IGenericRepository<Mapping_EsgKpi> _mappingEsgKpi;
        private IGenericRepository<Esg_Kpi_DataRecords> _mappingEsgKpiDataRecords;
        private IGenericRepository<M_ESGKPI> _M_ESGKpis;
        private IGenericRepository<FinancialAuditLog> _FinancialAuditLogRpository;
        private IGenericRepository<File_Upload_Status> _File_Upload_Status;
        private IGenericRepository<ValuationCompanyEquityCalculation> _ValuationCompanyEquityCalculationRepository;
        private IGenericRepository<ValuationEquityValue> _ValuationEquityValueRepository;
        private IGenericRepository<MappingCompanyValuationHeaderType> _MappingCompanyValuationHeaderTypeRepository;
        private IGenericRepository<M_ValuationHeaderType> _M_ValuationHeaderTypeRepository;
        private IGenericRepository<DocumentsInformation> _DocumentsInformationRepository;
        private IGenericRepository<DocumentComments> _DocumentCommentsRepository;
        private IGenericRepository<PortfolioCompanyCommentaryDraft> _PortfolioCompanyCommentaryDraftRepository;
        private IGenericRepository<PcCustomCommentaryDraft> _PcCustomCommentaryDraftRepository;
        private IGenericRepository<MCapTable> _MCapTableRepository;
        private IGenericRepository<MappingCapTable> _MappingCapTableRepository;
        private IGenericRepository<MKpiType> _MKpiTypeRepository;
        private IGenericRepository<PcCapTableValues> _PcCapTableValuesRepository;
        private IGenericRepository<CapTablePeriod> _CapTablePeriodRepository;
        private IGenericRepository<CapTableAuditLog> _CapTableAuditLogRepository;
        private IGenericRepository<DataRequest> _DataRequestRepository;
        private IGenericRepository<DataRequestGroup> _DataRequestGroupRepository;
        private IGenericRepository<DataRequestReminders> _DataRequestReminderRepository;
        private IGenericRepository<DataRequestAttachments> _DataRequestAttachmentRepository;
        private IGenericRepository<SDGImages> _SDGImagesRepository;
        private IGenericRepository<ExternalUser> _ExternalUserRepository;
        private IGenericRepository<UserBrowserDetails> _UserBrowserDetailsRepository;
        private IGenericRepository<MMonthlyReport> _MMonthlyReportRepository;
        private IGenericRepository<MappingMonthlyReport> _MappingMonthlyReportRepository;
        private IGenericRepository<MLpReportTemplate> _MLpReportTemplateRepository;
        private IGenericRepository<MappingLpReportKpiSection> _MappingLpReportKpiSectionRepository;
        private IGenericRepository<MappingLpReportPeriodSection> _MappingLpReportPeriodSectionRepository;
        private IGenericRepository<MappingLpReportSection> _MappingLpReportSectionRepository;
        private IGenericRepository<MappingLpReportTemplate> _MappingLpReportTemplateRepository;
        private IGenericRepository<MLpReportSection> _MLpReportSectionRepository;
        private IGenericRepository<MappingLpReportCommentarySection> _MappingLpReportCommentarySectionRepository;
        private IGenericRepository<LPReportHeader> _LpReportHeaderRepository;
        private IGenericRepository<GrowthReportHeader> _GrowthReportHeaderRepository;
        private IGenericRepository<MappingGrowthReport> _MappingGrowthReportRepository;
        private IGenericRepository<GrowthReportHeaderModule> _GrowthReportHeaderModuleRepository;
        private IGenericRepository<GrowthReportHeaderKpi> _GrowthReportHeaderKpiRepository;
        private IGenericRepository<GrowthReportColumnKpi> _GrowthReportColumnKpiRepository;
        private IGenericRepository<DataExtractionTypes> _DataExtractionTypesRepository;
        private IGenericRepository<UserInformation> _UserInfoRepository;
        private IGenericRepository<User_Documents> _UserDocumentsRepository;
        private IGenericRepository<M_UserCategory> _MUserCategoryRepository;
        private IGenericRepository<MSourceTypes> _MSourceTypesRepository;

        #region CLO_Module
        private IGenericRepository<CLO_InvestmentCompanyDetails> _investmentCompanyDetails;
        private IGenericRepository<clo_commentries> _clocommentries;

        private IGenericRepository<CloDetails> _cloDetails;
        private IGenericRepository<TableFootnoteSchema> _tableFootnoteSchema;
        private IGenericRepository<M_clopagedetails> _clopageDetails;
        private IGenericRepository<CLO_M_Table> _clotableDetails;

        #endregion
        private IGenericRepository<DocCollectionFrequencyConfig> _DocCollectionConfigRepository;
        private IGenericRepository<DocumentCollectionStore> _DocumentCollectionRepository;
        private IGenericRepository<RepositoryDocumentMappingDetail> _DocumentMappingRepository;
        private IGenericRepository<MFundSectionKpi> _MFundSectionKpiRepository;
        private IGenericRepository<MappingFundSectionKpi> _MappingFundSectionKpiRepository;
        private IGenericRepository<FundMasterKpiValues> _FundMasterKpiValuesRepository;
        private IGenericRepository<FundKpiAuditLog> _FundKpiAuditLogRepository;
        private IGenericRepository<MFundKpiModules> _MFundKpiModulesRepository;

        private IGenericRepository<EmailNotificationGroup> _emailNotificationGroupRepository;
        private IGenericRepository<EmailListMember> _emailListMemberRepository;
        private IGenericRepository<CompanyEmailGroup> _emailGroupCompanyRepository;
        private IGenericRepository<EmailReminder> _emailReminderRepository;
        private IGenericRepository<EmailReminderRecipients> _emailReminderRecipientsRepository;
        private IGenericRepository<EmailReminderConfig> _emailReminderConfigRepository;
        private IGenericRepository<EmailReminderSchedule> _emailReminderScheduleRepository;
        private IGenericRepository<DashboardTrackerConfig> _DashboardTrackerConfigRepository;
        #endregion Private member variables...

        public UnitOfWork(DBEntities dBEntities)
        {
            _context = dBEntities;

        }

        public bool AutoDetectChangesEnabled
        {
            get
            {
                return _context.ChangeTracker.AutoDetectChangesEnabled;
            }
            set
            {
                _context.ChangeTracker.AutoDetectChangesEnabled = value;
                if (value)
                {
                    _context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.TrackAll;
                }
                else
                {
                    _context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                }

            }
        }

        #region Public Repository Creation properties...

        public IGenericRepository<MasterKpiAuditLog> MasterKpiAuditLogRepository
        {
            get
            {
               return _masterKpiAuditLogRepository ??= new GenericRepository<MasterKpiAuditLog>(_context);
            }
        }
        public IGenericRepository<ImpactKpiAuditLog> ImpactKpiAuditLogRepository
        {
            get
            {
               return _impactKpiAuditLogRepository ??= new GenericRepository<ImpactKpiAuditLog>(_context);
            }
        }

        public IGenericRepository<MappingPortfolioOperationalKpi> MappingPortfolioOperationalKpi_OrderRepository
        {
            get
            {
                return _Mapping_OperationalKpi ??= new GenericRepository<MappingPortfolioOperationalKpi>(_context);
            }
        }

        public IGenericRepository<Mapping_ImpactKPI_Order> Mapping_ImpactKPI_OrderRepository
        {
            get
            {
                return _Mapping_ImpactKPI_Order ??= new GenericRepository<Mapping_ImpactKPI_Order>(_context);
            }
        }

        public IGenericRepository<M_CompanyKpi> M_CompanyKPIRepository
        {
            get
            {
                return _m_CompanyKPI ??= new GenericRepository<M_CompanyKpi>(_context);
            }
        }

        public IGenericRepository<MappingPortfolioCompanyKpi> PortfolioCompanyKpiMappingRepository
        {
            get
            {
                return _portfolioCompanyKpiMapping ??= new GenericRepository<MappingPortfolioCompanyKpi>(_context);
            }
        }
        public IGenericRepository<MappingPortfolioInvestmentKpi> PortfolioInvestmentKpiMappingRepository
        {
            get
            {
                return _portfolioInvestmentKpiMapping ??= new GenericRepository<MappingPortfolioInvestmentKpi>(_context);
            }

        }
        public IGenericRepository<M_Kpitypes> KPITypesRepository
        {
            get
            {
                return _m_kpiTypes ??= new GenericRepository<M_Kpitypes>(_context);
            }
        }

        public IGenericRepository<LoginDetails> LoginAttemptRepository
        {
            get
            {
                return _loginAttemptRepo ??= new GenericRepository<LoginDetails>(_context);
            }
        }

        public IGenericRepository<DataAuditLog> DataAuditLogRepository
        {
            get
            {
               return _dataAuditlogRepo ??= new GenericRepository<DataAuditLog>(_context);
            }
        }

        public IGenericRepository<CurrencyRates> CurrencyRateRepository
        {
            get
            {
                return _currencyRateRepo ??= new GenericRepository<CurrencyRates>(_context);
            }
        }

        public IGenericRepository<ApiCurrencyRates> ApiCurrencyRateRepository
        {
            get
            {
                return _apiCurrencyRateRepo ??= new GenericRepository<ApiCurrencyRates>(_context);
            }

        }
        public IGenericRepository<AuditLog> AuditLogRepository
        {
            get
            {
                return _auditLogRepo ??= new GenericRepository<AuditLog>(_context);
            }
        }

        public IGenericRepository<EmailTemplate> EmailTemplateRepository
        {
            get
            {
                return _emailTemplateRepo ??= new GenericRepository<EmailTemplate>(_context);
            }
        }

        public IGenericRepository<View_RegionCountryMapping> View_RegionCountryMappingRepository
        {
            get
            {
                return _view_RegionCountryMapping ??= new GenericRepository<View_RegionCountryMapping>(_context);
            }
        }

        public IGenericRepository<View_LocationMapping> View_LocationMappingRepository
        {
            get
            {
                return _view_LocationMapping ??= new GenericRepository<View_LocationMapping>(_context);
            }
        }

        public IGenericRepository<CashFlowFileLogs> CashflowRepository
        {
            get
            {
                return _cashFlowRepo ??= new GenericRepository<CashFlowFileLogs>(_context);
            }
        }

        public IGenericRepository<M_PipelineStatus> PipelineStatusRepository
        {
            get
            {
                return _m_PipelineStatus ??= new GenericRepository<M_PipelineStatus>(_context);
            }
        }

        public IGenericRepository<PipelineDetails> PipelineRepository
        {
            get
            {
                return _pipelineDetail ??= new GenericRepository<PipelineDetails>(_context);
            }
        }

        public IGenericRepository<DynamicQueries> DynamicQueryRepository
        {
            get
            {
                return _dynamicQuery ??= new GenericRepository<DynamicQueries>(_context);
            }
        }

        public IGenericRepository<DynamicQueriesCopy> DynamicQueryCopyRepository
        {
            get
            {
                return _dynamicQueryCopy ??= new GenericRepository<DynamicQueriesCopy>(_context);
            }
        }

        public IGenericRepository<Mapping_FirmHeadquarterLocation> FirmHeadquarterRepository
        {
            get
            {
                return _firmHeadquarterDetail ??= new GenericRepository<Mapping_FirmHeadquarterLocation>(_context);
            }
        }

        public IGenericRepository<Mapping_FirmGeographicLocation> FirmGeographyRepository
        {
            get
            {
                return _firmGeographyDetail ??= new GenericRepository<Mapping_FirmGeographicLocation>(_context);
            }
        }

        public IGenericRepository<Mapping_PCGeographicLocation> PCGeographyRepository
        {
            get
            {
                return _pcGeographyDetail ??= new GenericRepository<Mapping_PCGeographicLocation>(_context);
            }
        }

        public IGenericRepository<UserDetails> UserRepository
        {
            get
            {
                return _userDetail ??= new GenericRepository<UserDetails>(_context);
            }
        }

        public IGenericRepository<M_Country> M_CountryRepository
        {
            get
            {
                return _m_Country ??= new GenericRepository<M_Country>(_context);
            }
        }

        public IGenericRepository<MFinancialKpi> M_FinancialKPIRepository
        {
            get
            {
                return _m_FinancialKPI ??= new GenericRepository<MFinancialKpi>(_context);
            }
        }

        public IGenericRepository<M_OperationalKPI> M_OperationalKPIRepository
        {
            get
            {
                return _m_OperationalKPI ??= new GenericRepository<M_OperationalKPI>(_context);
            }
        }

        public IGenericRepository<M_Region> M_RegionRepository
        {
            get
            {
                return _m_Region ??= new GenericRepository<M_Region>(_context);
            }
        }

        public IGenericRepository<M_StockExchange> M_StockExchangeRepository
        {
            get
            {
                return _m_StockExchange ??= new GenericRepository<M_StockExchange>(_context);
            }
        }

        public IGenericRepository<M_Sector> M_SectorRepository
        {
            get
            {
                return _m_Sector ??= new GenericRepository<M_Sector>(_context);
            }
        }

        public IGenericRepository<M_InvestmentStage> M_InvestmentStageRepository
        {
            get
            {
                return _m_InvestmentStage ??= new GenericRepository<M_InvestmentStage>(_context);
            }
        }

        public IGenericRepository<M_FinancialStatus> M_FinancialStatusRepository
        {
            get
            {
                return _m_FinancialStatus ??= new GenericRepository<M_FinancialStatus>(_context);
            }
        }

        public IGenericRepository<M_OwnershipStatus> M_OwnershipStatusRepository
        {
            get
            {
                return _m_OwnershipStatus ??= new GenericRepository<M_OwnershipStatus>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyDetails> PortfolioCompanyDetailRepository
        {
            get
            {
                return _portFolioCompanyDetail ??= new GenericRepository<PortfolioCompanyDetails>(_context);
            }
        }

        public IGenericRepository<CompanySummaryDetails> CompanySummaryDetailRepository
        {
            get
            {
                return _companySummaryDetail ??= new GenericRepository<CompanySummaryDetails>(_context);
            }
        }

        public IGenericRepository<M_Currency> M_CurrencyRepository
        {
            get
            {
                return _m_Currency ??= new GenericRepository<M_Currency>(_context);
            }
        }

        public IGenericRepository<Mapping_CountryCurrency> Mapping_CountryCurrencyRepository
        {
            get
            {
                return _mapping_CountryCurrency ??= new GenericRepository<Mapping_CountryCurrency>(_context);
            }
        }

        public IGenericRepository<M_FirmType> Mapping_M_FirmTypeRepository
        {
            get
            {
                return _m_Firmtype ??= new GenericRepository<M_FirmType>(_context);
            }
        }

        public IGenericRepository<M_State> M_StateRepository
        {
            get
            {
                return _m_State ??= new GenericRepository<M_State>(_context);
            }
        }

        public IGenericRepository<M_City> M_CityRepository
        {
            get
            {
                return _m_City ??= new GenericRepository<M_City>(_context);
            }
        }

        public IGenericRepository<FirmDetails> FirmDetailRepository
        {
            get
            {
                return _firmDetail ??= new GenericRepository<FirmDetails>(_context);
            }
        }

        public IGenericRepository<M_Groups> GroupRepository
        {
            get
            {
                return _m_Groups ??= new GenericRepository<M_Groups>(_context);
            }
        }

        public IGenericRepository<M_FundingType> FundingTypeRepository
        {
            get
            {
                return _m_FundingType ??= new GenericRepository<M_FundingType>(_context);
            }
        }

        public IGenericRepository<CompanyFundingDetails> CompanyFundingDetailsRepository
        {
            get
            {
                return _m_CompanyFundingDetails ??= new GenericRepository<CompanyFundingDetails>(_context);
            }
        }

        public IGenericRepository<M_Features> FeatureRepository
        {
            get
            {
                return _m_Feature ??= new GenericRepository<M_Features>(_context);
            }
        }

        public IGenericRepository<Mapping_UserGroup> Mapping_UserGroupRepository
        {
            get
            {
                return _m_UserGroups ??= new GenericRepository<Mapping_UserGroup>(_context);
            }
        }

        public IGenericRepository<Mapping_GroupFeature> Mapping_GroupFeatureRepository
        {
            get
            {
                return _m_GroupFeatures ??= new GenericRepository<Mapping_GroupFeature>(_context);
            }
        }

        public IGenericRepository<M_Designation> M_DesignationRepository
        {
            get
            {
                return _m_Designation ??= new GenericRepository<M_Designation>(_context);
            }
        }

        public IGenericRepository<EmployeeDetails> EmployeeDetailRepository
        {
            get
            {
                return _employeeDetail ??= new GenericRepository<EmployeeDetails>(_context);
            }
        }

        public IGenericRepository<Mapping_FirmEmployee> FirmEmployeeDetailRepository
        {
            get
            {
                return _firmEmployeeDetail ??= new GenericRepository<Mapping_FirmEmployee>(_context);
            }
        }

        public IGenericRepository<Mapping_PCEmployee> PCEmployeeDetailRepository
        {
            get
            {
                return _pcEmployeeDetail ??= new GenericRepository<Mapping_PCEmployee>(_context);
            }
        }

        public IGenericRepository<M_Strategy> M_StrategyRepository
        {
            get
            {
                return _m_Strategy ??= new GenericRepository<M_Strategy>(_context);
            }
        }

        public IGenericRepository<FundDetails> FundDetailRepository
        {
            get
            {
                return _fundDetail ??= new GenericRepository<FundDetails>(_context);
            }
        }

        public IGenericRepository<Mapping_FirmFund> Mapping_FirmFundRepository
        {
            get
            {
                return _mapping_FirmFund ??= new GenericRepository<Mapping_FirmFund>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyFundHoldingDetails> PortfolioCompanyFundHoldingDetailRepository
        {
            get
            {
                return _portfolioCompanyFundHoldingDetail ??= new GenericRepository<PortfolioCompanyFundHoldingDetails>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyProfitabilityDetails> PortfolioCompanyProfitabilityDetailRepository
        {
            get
            {
                return _portFolioCompanyProfitabilityDetail ??= new GenericRepository<PortfolioCompanyProfitabilityDetails>(_context);
            }
        }

        public IGenericRepository<DealDetails> DealDetailRepository
        {
            get
            {
                return _dealDetail ??= new GenericRepository<DealDetails>(_context);
            }
        }

        public IGenericRepository<M_Geography> M_GeographyRepository
        {
            get
            {
                return _m_Geography ??= new GenericRepository<M_Geography>(_context);
            }
        }

        public IGenericRepository<M_AccountType> M_AccountTypeRepository
        {
            get
            {
                return _m_AccountType ??= new GenericRepository<M_AccountType>(_context);
            }
        }

        public IGenericRepository<GlobalVariables> GlobalVariableRepository
        {
            get
            {
                return _globalVariables ??= new GenericRepository<GlobalVariables>(_context);
            }
        }

        public IGenericRepository<Mapping_Locations> Mapping_LocationRepository
        {
            get
            {
                return _mapping_Location ??= new GenericRepository<Mapping_Locations>(_context);
            }
        }

        public IGenericRepository<M_SubSector> M_SubSectorRepository
        {
            get
            {
                return _m_SubSector ??= new GenericRepository<M_SubSector>(_context);
            }
        }

        public IGenericRepository<M_DealBoardSeat> M_DealBoardSeatRepository
        {
            get
            {
                return _m_DealBoardSeat ??= new GenericRepository<M_DealBoardSeat>(_context);
            }
        }

        public IGenericRepository<M_DealExitMethod> M_DealExitMethodRepository
        {
            get
            {
                return _m_DealExitMethod ??= new GenericRepository<M_DealExitMethod>(_context);
            }
        }

        public IGenericRepository<M_DealInvestmentStage> M_DealInvestmentStageRepository
        {
            get
            {
                return _m_DealInvestmentStage ??= new GenericRepository<M_DealInvestmentStage>(_context);
            }
        }

        public IGenericRepository<M_DealSecurtyType> M_DealSecurtyTypeRepository
        {
            get
            {
                return _m_DealSecurtyType ??= new GenericRepository<M_DealSecurtyType>(_context);
            }
        }

        public IGenericRepository<M_DealSourcing> M_DealSourcingRepository
        {
            get
            {
                return _m_DealSourcing ??= new GenericRepository<M_DealSourcing>(_context);
            }
        }

        public IGenericRepository<M_DealTransactionRole> M_DealTransactionRoleRepository
        {
            get
            {
                return _m_DealTransactionRole ??= new GenericRepository<M_DealTransactionRole>(_context);
            }
        }

        public IGenericRepository<M_DealValuationMethodology> M_DealValuationMethodologyRepository
        {
            get
            {
                return _m_DealValuationMethodology ??= new GenericRepository<M_DealValuationMethodology>(_context);
            }
        }

        public IGenericRepository<M_FundHoldingStatus> M_FundHoldingStatusRepository
        {
            get
            {
                return _m_FundHoldingStatus ??= new GenericRepository<M_FundHoldingStatus>(_context);
            }
        }

        public IGenericRepository<FundTrackRecord> FundTrackRecordRepository
        {
            get
            {
                return _fundTrackRecord ??= new GenericRepository<FundTrackRecord>(_context);
            }
        }

        public IGenericRepository<FundIngestion> FundIngestionRepository
        {
            get
            {
                return _fundIngestion ??= new GenericRepository<FundIngestion>(_context);
            }
        }

        public IGenericRepository<M_SectorwiseKPI> M_SectorwiseKPIRepository
        {
            get
            {
                return _m_SectorwiseKPI ??= new GenericRepository<M_SectorwiseKPI>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanySectorwiseKPIValues> PortfolioCompanySectorwiseKPIValueRepository
        {
            get
            {
                return _portfolioCompanySectorwiseKPIValue ??= new GenericRepository<PortfolioCompanySectorwiseKPIValues>(_context);
            }
        }

        public IGenericRepository<PCOperationalKPIMonthlyValue> PCOperationalKPIMonthlyValueRepository
        {
            get
            {
                return _pcOperationalKPIMonthlyValue ??= new GenericRepository<PCOperationalKPIMonthlyValue>(_context);
            }
        }

        public IGenericRepository<PCFinancialKPIMonthlyValue> PCFinancialKPIMonthlyValueRepository
        {
            get
            {
                return _pcFinancialKPIMonthlyValue ??= new GenericRepository<PCFinancialKPIMonthlyValue>(_context);
            }
        }

        public IGenericRepository<PCSectorwiseKPIMonthlyValue> PCSectorwiseKPIMonthlyValueRepository
        {
            get
            {
                return _pcSectorwiseKPIMonthlyValue ??= new GenericRepository<PCSectorwiseKPIMonthlyValue>(_context);
            }
        }

        public IGenericRepository<PcCompanywiseKpiMonthlyValue> PCCompanywiseKPIMonthlyValueRepository
        {
            get
            {
                return _pcCompanywiseKPIMonthlyValue ??= new GenericRepository<PcCompanywiseKpiMonthlyValue>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanySectorwiseKPIMonthly> PortfolioCompanySectorwiseKPIQuarterRepository
        {
            get
            {
                return _portfolioCompanySectorwiseKPIMonths ??= new GenericRepository<PortfolioCompanySectorwiseKPIMonthly>(_context);
            }
        }

        public IGenericRepository<KPIValuesFileLogs> KPIValuesFileRepository
        {
            get
            {
                return _kpiValuesFileRepo ??= new GenericRepository<KPIValuesFileLogs>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyOperationalKPIQuarter> PortfolioCompanyOperationalKPIQuarterRepository
        {
            get
            {
                return _portfolioCompanyOperationalKPIQuarter ??= new GenericRepository<PortfolioCompanyOperationalKPIQuarter>(_context);
            }
        }
        public IGenericRepository<PortfolioCompanyOperationalKPIValue> PortfolioCompanyOperationalKPIValueRepository
        {
            get
            {
                return _portfolioCompanyOperationalKPIValue ??= new GenericRepository<PortfolioCompanyOperationalKPIValue>(_context);
            }
        }
        public IGenericRepository<BalanceSheetValues> BalanceSheetValuesRepository
        {
            get
            {
                return _pcBalanceSheetValues ??= new GenericRepository<BalanceSheetValues>(_context);
            }
        }

        public IGenericRepository<M_ProfitAndLoss_LineItems> M_ProfitAndLoss_LineItemsRepository
        {
            get
            {
                return _m_ProfitAndLoss_LineItems ??= new GenericRepository<M_ProfitAndLoss_LineItems>(_context);
            }
        }

        public IGenericRepository<Mapping_CompanyProfitAndLossLineItems> Mapping_CompanyProfitAndLossLineItemsRepository
        {
            get
            {
                return _mapping_CompanyProfitAndLossLineItems ??= new GenericRepository<Mapping_CompanyProfitAndLossLineItems>(_context);
            }
        }

        public IGenericRepository<Mapping_EsgKpi> Mapping_EsgKpiRepository
        {
            get
            {
                return _mappingEsgKpi ??= new GenericRepository<Mapping_EsgKpi>(_context);
            }
        }

        public IGenericRepository<Esg_Kpi_DataRecords> Esg_Kpi_DataRecordsRepository
        {
            get
            {
                return _mappingEsgKpiDataRecords ??= new GenericRepository<Esg_Kpi_DataRecords>(_context);
            }
        }

        public IGenericRepository<M_BalanceSheet_LineItems> M_BalanceSheet_LineItemsRepository
        {
            get
            {
                return _m_BalanceSheet_LineItems ??= new GenericRepository<M_BalanceSheet_LineItems>(_context);
            }
        }
        public IGenericRepository<Mapping_CompanyBalanceSheetLineItems> Mapping_CompanyBalanceSheetLineItemsRepository
        {
            get
            {
                return _mapping_CompanyBalanceSheetLineItems ??= new GenericRepository<Mapping_CompanyBalanceSheetLineItems>(_context);
            }
        }
        public IGenericRepository<ProfitAndLossValues> ProfitAndLossValuesRepository
        {
            get
            {
                return _pcProfitAndLossValues ??= new GenericRepository<ProfitAndLossValues>(_context);
            }
        }
        public IGenericRepository<PCFinancialsValues> PCFinancialsValuesRepository
        {
            get
            {
                if (this._pcPCFinancialsValues == null)
                    this._pcPCFinancialsValues = new GenericRepository<PCFinancialsValues>(_context);
                return _pcPCFinancialsValues;
            }
        }
        public IGenericRepository<M_CashFlow_LineItems> M_CashFlow_LineItemsRepository
        {
            get
            {
                return _M_CashFlow_LineItems ??= new GenericRepository<M_CashFlow_LineItems>(_context);
            }
        }

        public IGenericRepository<Mapping_CompanyCashFlowLineItems> Mapping_CompanyCashFlowLineItemsRepository
        {
            get
            {
                return _Mapping_CompanyCashFlowLineItems ??= new GenericRepository<Mapping_CompanyCashFlowLineItems>(_context);
            }
        }

        public IGenericRepository<CashFlowValues> CashFlowValuesRepository
        {
            get
            {
                return _CashFlowValues ??= new GenericRepository<CashFlowValues>(_context);
            }
        }

        public IGenericRepository<PCInvestmentKpiQuarterlyValue> PCInvestmentKpiQuarterlyValueRepository
        {
            get
            {
                return _pcInvestmentKpiQuarterlyValue ??= new GenericRepository<PCInvestmentKpiQuarterlyValue>(_context);
            }
        }

        public IGenericRepository<PCCompanyKpiMonthlyValue> PCCompanyKpiMonthlyValueRepository
        {
            get
            {
                return _pcCompanyKpiMonthlyValue ??= new GenericRepository<PCCompanyKpiMonthlyValue>(_context);
            }
        }

        public IGenericRepository<PcImpactKpiQuarterlyValue> PCImpactKpiQuarterlyValueRepository
        {
            get
            {
                return _pcImpactKpiQuarterlyValue ??= new GenericRepository<PcImpactKpiQuarterlyValue>(_context);
            }
        }


        public IGenericRepository<MImpactKpi> M_ImpactKPIRepository
        {
            get
            {
                return _m_ImpactKPI ??= new GenericRepository<MImpactKpi>(_context);
            }
        }
        public IGenericRepository<M_InvestmentKpi> M_InvestmentKPIRepository
        {
            get
            {
                return _m_InvestmentKPI ??= new GenericRepository<M_InvestmentKpi>(_context);
            }
        }

        public IGenericRepository<M_SubFeature> M_SubFeatureRepository
        {
            get
            {
                return _m_SubFeature ??= new GenericRepository<M_SubFeature>(_context);
            }
        }

        public IGenericRepository<Mapping_SubFeatureAction> Mapping_SubFeatureActionRepository
        {
            get
            {
               return _mapping_SubFeatureAction ??= new GenericRepository<Mapping_SubFeatureAction>(_context);
            }
        }

        public IGenericRepository<Mapping_UserSubFeature> Mapping_UserSubFeatureRepository
        {
            get
            {
              return  _mapping_SubFeature ??= new GenericRepository<Mapping_UserSubFeature>(_context);
            }
        }
        public IGenericRepository<ImpactKPI_AnnualHistoricalData> ImpactKPI_AnnualHistoricalDataRepository
        {
            get
            {
                return _ImpactKPI_AnnualHistoricalData ??= new GenericRepository<ImpactKPI_AnnualHistoricalData>(_context);
            }
        }
        public IGenericRepository<CommentaryDetails> CommentaryDetailsRepository
        {
            get
            {
                return _commentaryDetails ??= new GenericRepository<CommentaryDetails>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyCommentaryDraft> PortfolioCompanyCommentaryDraftRepository
        {
            get
            {
                return _PortfolioCompanyCommentaryDraftRepository ??= new GenericRepository<PortfolioCompanyCommentaryDraft>(_context);
            }
        }

        public IGenericRepository<PcCustomCommentaryDraft> PcCustomCommentaryDraftRepository
        {
            get
            {
                return _PcCustomCommentaryDraftRepository ??= new GenericRepository<PcCustomCommentaryDraft>(_context);
            }
        }
        public IGenericRepository<M_StandingDataItems> M_StandingDataItemsRepository
        {
            get
            {
                return _M_StandingDataItems ??= new GenericRepository<M_StandingDataItems>(_context);
            }
        }

        public IGenericRepository<DocumentType> DocumentTypesRepository
        {
            get
            {
                return _DocumentTypes ??= new GenericRepository<DocumentType>(_context);
            }
        }

        public IGenericRepository<Document> DocumentsRepository
        {
            get
            {
                return _Documents ??= new GenericRepository<Document>(_context);
            }
        }

        public IGenericRepository<DocumentStatus> DocumentStatusRepository
        {
            get
            {
                return _DocumentStatus ??= new GenericRepository<DocumentStatus>(_context);
            }
        }

        public IGenericRepository<UserReport> UserReportsRepository
        {
            get
            {
                return _UserReportsRepository ??= new GenericRepository<UserReport>(_context);
            }
        }
        public IGenericRepository<M_LPReportConfiguration> M_LPReportConfigurationRepository
        {
            get
            {
                return _M_LPReportConfiguration ??= new GenericRepository<M_LPReportConfiguration>(_context);
            }
        }

        public IGenericRepository<M_FundReportConfiguration> M_FundReportConfigurationRepository
        {
            get
            {
                return _M_FundReportConfiguration ??= new GenericRepository<M_FundReportConfiguration>(_context);
            }
        }
        public IGenericRepository<Mapping_LPReportConfiguration> Mapping_LPReportConfigurationRepository
        {
            get
            {
                return _Mapping_LPReportConfiguration ??= new GenericRepository<Mapping_LPReportConfiguration>(_context);
            }
        }
        public IGenericRepository<M_KpiModules> M_KpiModulesRepository
        {
            get
            {
                return _M_KpiModules ??= new GenericRepository<M_KpiModules>(_context);
            }
        }
        public IGenericRepository<M_ValueTypes> M_ValueTypesRepository
        {
            get
            {
                return _M_ValueTypes ??= new GenericRepository<M_ValueTypes>(_context);
            }
        }
        public IGenericRepository<M_MasterKpis> M_MasterKpisRepository
        {
            get
            {
                return _M_MasterKpis ??= new GenericRepository<M_MasterKpis>(_context);
            }
        }
        public IGenericRepository<M_ESGKPI> M_ESGKpisRepository
        {
            get
            {
                return _M_ESGKpis ??= new GenericRepository<M_ESGKPI>(_context);
            }
        }
        public IGenericRepository<Mapping_Kpis> Mapping_KpisRepository
        {
            get
            {
                return _Mapping_Kpis ??= new GenericRepository<Mapping_Kpis>(_context);
            }
        }
        public IGenericRepository<PCMasterKpiValues> PCMasterKpiValuesRepository
        {
            get
            {
                return _PCMasterKpiValues ??= new GenericRepository<PCMasterKpiValues>(_context);
            }
        }
        public IGenericRepository<M_Methodology> M_MethodologyRepository
        {
            get
            {
                return _M_Methodology ??= new GenericRepository<M_Methodology>(_context);
            }
        }
        public IGenericRepository<M_NotificationModules> M_NotificationModulesRepository
        {
            get
            {
                return _M_NotificationModules ??= new GenericRepository<M_NotificationModules>(_context);
            }
        }
        public IGenericRepository<M_Notification> M_NotificationRepository
        {
            get
            {
                return _M_Notification ??= new GenericRepository<M_Notification>(_context);
            }
        }
        public IGenericRepository<BalanceSheetICCaseValues> BalanceSheetICCaseValuesRepository
        {
            get
            {
                return _BalanceSheetICCaseValues ??= new GenericRepository<BalanceSheetICCaseValues>(_context);
            }
        }
        public IGenericRepository<BalanceSheetForecastData> BalanceSheetForecastDataRepository
        {
            get
            {
                return _BalanceSheetForecastData ??= new GenericRepository<BalanceSheetForecastData>(_context);
            }
        }
        public IGenericRepository<ProfitAndLossIccaseValues> ProfitAndLossIccaseValuesRepository
        {
            get
            {
                return _ProfitAndLossIccaseValues ??= new GenericRepository<ProfitAndLossIccaseValues>(_context);
            }
        }
        public IGenericRepository<ProfitAndLossForecastData> ProfitAndLossForecastDataRepository
        {
            get
            {
                return _ProfitAndLossForecastData ??= new GenericRepository<ProfitAndLossForecastData>(_context);
            }
        }
        public IGenericRepository<CashFlowICCaseValues> CashFlowICCaseValuesRepository
        {
            get
            {
                return _CashFlowICCaseValues ??= new GenericRepository<CashFlowICCaseValues>(_context);
            }
        }
        public IGenericRepository<CashFlow_ForecastData> CashFlow_ForecastDataRepository
        {
            get
            {
                return _CashFlow_ForecastData ??= new GenericRepository<CashFlow_ForecastData>(_context);
            }
        }

        public IGenericRepository<FinancialValueTypes> FinancialValueTypesRepository
        {
            get
            {
              return  _FinancialValueTypes ??= new GenericRepository<FinancialValueTypes>(_context);
            }
        }
        public IGenericRepository<PageConfiguration> ReportPageConfigurationRepository
        {
            get
            {
              return  _ReportPageConfiguration ??= new GenericRepository<PageConfiguration>(_context);
            }
        }


        public IGenericRepository<ReportTemplateConfiguration> ReportTemplateRepository
        {
            get
            {
               return _ReportTemplateConfiguration ??= new GenericRepository<ReportTemplateConfiguration>(_context);
            }
        }
        public IGenericRepository<MappingWorkflowStatus> MappingWorkflowStatusRepository
        {
            get
            {
              return  _MappingWorkflowStatus ??= new GenericRepository<MappingWorkflowStatus>(_context);
            }
        }
        public IGenericRepository<MWorkflowStatus> MWorkflowStatusRepository
        {
            get
            {
               return _MWorkflowStatus ??= new GenericRepository<MWorkflowStatus>(_context);
            }
        }

        public IGenericRepository<MappingWorkflowStatusGroup> MappingWorkflowStatusGroupRepository
        {
            get
            {
              return  _MappingWorkflowStatusGroup ??= new GenericRepository<MappingWorkflowStatusGroup>(_context);
            }
        }
        public IGenericRepository<WorkflowRequest> WorkflowRequestRepository
        {
            get
            {
               return _WorkflowRequest ??= new GenericRepository<WorkflowRequest>(_context);
            }
        }
        public IGenericRepository<MappingWorkflowRequest> MappingWorkflowRequestRepository
        {
            get
            {
               return _MappingWorkflowRequest ??= new GenericRepository<MappingWorkflowRequest>(_context);
            }
        }
        public IGenericRepository<MappingWorkflowRequestComments> MappingWorkflowRequestCommentsRepository
        {
            get
            {
               return _MappingWorkflowRequestComments ??= new GenericRepository<MappingWorkflowRequestComments>(_context);
            }
        }
        public IGenericRepository<PortfolioCompanyDetailsDraft> PortfolioCompanyDetailsDraftRepository
        {
            get
            {
               return _PortfolioCompanyDetailsDraft ??= new GenericRepository<PortfolioCompanyDetailsDraft>(_context);
            }
        }

        public IGenericRepository<ReportTemplateConfigurationMapping> ReportTemplateMappingRepository
        {
            get
            {
              return  _ReportTemplateConfigurationMapping ??= new GenericRepository<ReportTemplateConfigurationMapping>(_context);
            }
        }
        public IGenericRepository<MasterGroups> MasterGroupsRepository
        {
            get
            {
              return  _MasterGroupsRepository ??= new GenericRepository<MasterGroups>(_context);
            }
        }
        public IGenericRepository<PeriodTypes> PeriodTypesRepository
        {
            get
            {
              return  _PeriodTypesRepo ??= new GenericRepository<PeriodTypes>(_context);
            }
        }
        public IGenericRepository<MappingPCGeographicLocationDraft> MappingPCGeographicLocationDraftRepository
        {
            get
            {
               return _MappingPCGeographicLocationDraft ??= new GenericRepository<MappingPCGeographicLocationDraft>(_context);
            }
        }
        public IGenericRepository<MappingPCEmployeeDraft> MappingPCEmployeeDraftRepository
        {
            get
            {
              return  _MappingPCEmployeeDraft ??= new GenericRepository<MappingPCEmployeeDraft>(_context);
            }
        }
        public IGenericRepository<WorkflowHistory> WorkflowHistoryRepository
        {
            get
            {
              return  _WorkflowHistory ??= new GenericRepository<WorkflowHistory>(_context);
            }
        }
        public IGenericRepository<EmployeeDetailsDraft> EmployeeDetailsDraftRepository
        {
            get
            {
               return _EmployeeDetailsDraft ??= new GenericRepository<EmployeeDetailsDraft>(_context);
            }
        }
        public IGenericRepository<M_Action> MActionRepository
        {
            get
            {
               return _MAction ??= new GenericRepository<M_Action>(_context);
            }
        }
        public IGenericRepository<PCInvestmentKpiQuarterlyValueDraft> PCInvestmentKpiQuarterlyValueDraftRepository
        {
            get
            {
              return  _PCInvestmentKpiQuarterlyValueDraft ??= new GenericRepository<PCInvestmentKpiQuarterlyValueDraft>(_context);
            }
        }
        public IGenericRepository<PCCompanyKpiMonthlyValueDraft> PCCompanyKpiMonthlyValueDraftRepository
        {
            get
            {
              return  _PCCompanyKpiMonthlyValueDraft ??= new GenericRepository<PCCompanyKpiMonthlyValueDraft>(_context);
            }
        }
        public IGenericRepository<DraftAuditLog> DraftAuditLogRepository
        {
            get
            {
              return  _DraftAuditLog ??= new GenericRepository<DraftAuditLog>(_context);
            }
        }
        public IGenericRepository<ReportTypes> ReportTypesRepository
        {
            get
            {
               return _ReportTypes ??= new GenericRepository<ReportTypes>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyOperationalKPIQuartersDraft> PCOperationalKpiQuarterDraftRepository
        {
            get
            {
               return _PCOperationalKpiQuarterDraftRepository ??= new GenericRepository<PortfolioCompanyOperationalKPIQuartersDraft>(_context);
            }
        }

        public IGenericRepository<PortfolioCompanyOperationalKPIValuesDraft> PCOperationalKpiValuesDraftRepository
        {
            get
            {
              return  _PCOperationalKpiValuesDraftRepository ??= new GenericRepository<PortfolioCompanyOperationalKPIValuesDraft>(_context);
            }
        }
        public IGenericRepository<FundReportTemplateConfiguration> FundReportTemplateConfigurationRepository
        {
            get
            {
              return  _FundReportTemplateConfiguration ??= new GenericRepository<FundReportTemplateConfiguration>(_context);
            }
        }
        public IGenericRepository<FundReportTemplateConfigurationMapping> FundReportTemplateConfigurationMappingRepository
        {
            get
            {
              return  _FundReportTemplateConfigurationMapping ??= new GenericRepository<FundReportTemplateConfigurationMapping>(_context);
            }
        }
        public IGenericRepository<WorkflowMappingHistory> WorkflowMappingHistoryRepository
        {
            get
            {
              return  _WorkflowMappingHistory ??= new GenericRepository<WorkflowMappingHistory>(_context);

            }
        }

        public IGenericRepository<M_PageDetails> PageDetailsRepository
        {
            get
            {
              return  _PageDetailsRepository ??= new GenericRepository<M_PageDetails>(_context);
            }
        }

        public IGenericRepository<M_SubPageDetails> SubPageDetailsRepository
        {
            get
            {
               return _SubPageDetailsRepository ??= new GenericRepository<M_SubPageDetails>(_context);
            }
        }

        public IGenericRepository<M_SubPageFields> SubPageFieldsRepository
        {
            get
            {
               return _SubPageFieldsRepository ??= new GenericRepository<M_SubPageFields>(_context);
            }
        }
        public IGenericRepository<PageConfigurationFieldValue> PageConfigurationFieldValueRepository
        {
            get
            {
               return _PageConfigurationFieldValueRepository ??= new GenericRepository<PageConfigurationFieldValue>(_context);
            }
        }

        public IGenericRepository<PageConfigurationTrackRecordFieldValue> PageConfigurationTrackRecordFieldValueRepository
        {
            get
            {
              return  _PageConfigurationTrackRecordFieldValueRepository ??= new GenericRepository<PageConfigurationTrackRecordFieldValue>(_context);
            }
        }
        public IGenericRepository<M_TrackRecordDataTypes> M_TrackRecordDataTypesRepository
        {
            get
            {
              return  _M_TrackRecordDataTypesRepository ??= new GenericRepository<M_TrackRecordDataTypes>(_context);
            }
        }
        public IGenericRepository<MInvestorType> M_InvestorTypeRepository
        {
            get
            {
               return _M_InvestorTypeRepository ??= new GenericRepository<MInvestorType>(_context);
            }
        }
        public IGenericRepository<InvestorType> InvestorTypeRepositoryRepository
        {
            get
            {
               return _InvestorTypeRepositoryRepository ??= new GenericRepository<InvestorType>(_context);
            }
        }
        public IGenericRepository<FundInvestors> FundInvestorsRepository
        {
            get
            {
               return _fundInvestorsRepository ??= new GenericRepository<FundInvestors>(_context);
            }
        }
        public IGenericRepository<MappingInvestorGeographicLocation> MappingInvestor_GeographicLocationRepositoryRepository
        {
            get
            {
              return  _MappingInvestor_GeographicLocationRepositoryRepository ??= new GenericRepository<MappingInvestorGeographicLocation>(_context);
            }
        }

        public IGenericRepository<InvestorValuation> InvestorValuationRepositoryRepository
        {
            get
            {
               return _InvestorValuation ??= new GenericRepository<InvestorValuation>(_context);
            }
        }

        public IGenericRepository<PageConfigurationFieldValueDraft> PageConfigurationFieldValueDraftRepository
        {
            get
            {
               return _PageConfigurationFieldValueDraftRepository ??= new GenericRepository<PageConfigurationFieldValueDraft>(_context);
            }
        }

        public IGenericRepository<CompanyKPIForecastValues> CompanyKPIForecastValuesRepository
        {
            get
            {
               return _CompanyKPIForecastValues ??= new GenericRepository<CompanyKPIForecastValues>(_context);
            }
        }
        public IGenericRepository<UnstructuredHistory> UnstructuredMappingHistoryRepository
        {
            get
            {
               return UnstructuredMappingHistory ??= new GenericRepository<UnstructuredHistory>(_context);
            }
        }
        public IGenericRepository<FootNotes> FootNotesRepository
        {
            get
            {
               return _FootNotesRepository ??= new GenericRepository<FootNotes>(_context);
            }
        }
        public IGenericRepository<InternalReportConfiguration> InternalReportConfigurationRepository
        {
            get
            {
               return _InternalReportConfigurationRepository ??= new GenericRepository<InternalReportConfiguration>(_context);
            }
        }
        public IGenericRepository<MappingInternalReportConfiguration> MappingInternalReportConfigurationRepository
        {
            get
            {
               return _MappingInternalReportConfigurationRepository ??= new GenericRepository<MappingInternalReportConfiguration>(_context);
            }
        }
        public IGenericRepository<InternalReportFundPreference> InternalReportFundPreferenceRepository
        {
            get
            {
               return _InternalReportFundPreferenceRepository ??= new GenericRepository<InternalReportFundPreference>(_context);
            }
        }
        public IGenericRepository<InternalReportSectionPreference> InternalReportSectionPreferenceRepository
        {
            get
            {
                return _InternalReportSectionPreferenceRepository ??= new GenericRepository<InternalReportSectionPreference>(_context);
            }
        }
        public IGenericRepository<InternalReportValueTypePreference> InternalReportValueTypePreferenceRepository
        {
            get
            {
               return _InternalReportValueTypePreferenceRepository ??= new GenericRepository<InternalReportValueTypePreference>(_context);
            }
        }
        public IGenericRepository<InternalReportPeriodTypePreference> InternalReportPeriodTypePreferenceRepository
        {
            get
            {
              return  _InternalReportPeriodTypePreferenceRepository ??= new GenericRepository<InternalReportPeriodTypePreference>(_context);
            }
        }
        public IGenericRepository<InternalReportCalculationPreference> InternalReportCalculationPreferenceRepository
        {
            get
            {
               return _InternalReportCalculationPreferenceRepository ??= new GenericRepository<InternalReportCalculationPreference>(_context);
            }
        }
        public IGenericRepository<MPeriod> MPeriodRepository
        {
            get
            {
                return _MPeriodRepository ??= new GenericRepository<MPeriod>(_context);
            }
        }
        public IGenericRepository<MCalculation> MCalculationRepository
        {
            get
            {
                return _MCalculationRepository ??= new GenericRepository<MCalculation>(_context);
            }
        }
        public IGenericRepository<MInternalReportExcelTemplate> MInternalReportExcelTemplateRepository
        {
            get
            {
               return _MInternalReportExcelTemplateRepository ??= new GenericRepository<MInternalReportExcelTemplate>(_context);
            }
        }
        public IGenericRepository<InternalReportExcelTemplatePreference> InternalReportExcelTemplatePreferenceRepository
        {
            get
            {
               return _InternalReportExcelTemplatePreferenceRepository ??= new GenericRepository<InternalReportExcelTemplatePreference>(_context);
            }
        }
        public IGenericRepository<InternalReportPeriodConfigPreference> InternalReportPeriodConfigPreferenceRepository
        {
            get
            {
               return _InternalReportPeriodConfigPreferenceRepository ??= new GenericRepository<InternalReportPeriodConfigPreference>(_context);
            }
        }
        public IGenericRepository<ReportDownloadType> ReportDownloadTypeRepository
        {
            get
            {
                return _ReportDownloadTypeRepository ??= new GenericRepository<ReportDownloadType>(_context);
            }
        }
        public IGenericRepository<ConsolidatedReportConfiguration> ConsolidatedReportConfigurationRepository
        {
            get
            {
                return _ConsolidatedReportConfigurationRepository ??= new GenericRepository<ConsolidatedReportConfiguration>(_context);
            }
        }
        public IGenericRepository<MappingConsolidatedReportConfiguration> MappingConsolidatedReportConfigurationRepository
        {
            get
            {
                return _MappingConsolidatedReportConfigurationRepository ??= new GenericRepository<MappingConsolidatedReportConfiguration>(_context);
            }
        }
        public IGenericRepository<ConsolidatedReportPreference> ConsolidatedReportPreferenceRepository
        {
            get
            {
                return _ConsolidatedReportPreferenceRepository ??= new GenericRepository<ConsolidatedReportPreference>(_context);
            }
        }
        public IGenericRepository<MConsolidatedReportExcelTemplate> MConsolidatedReportExcelTemplateRepository
        {
            get
            {
                return _MConsolidatedReportExcelTemplateRepository ??= new GenericRepository<MConsolidatedReportExcelTemplate>(_context);
            }
        }

        public IGenericRepository<PcMasterKpiValueDraft> PcMasterKpiValueDraftRepository
        {
            get
            {
               return _PcMasterKpiValueDraftRepository ??= new GenericRepository<PcMasterKpiValueDraft>(_context);
            }
        }
        public IGenericRepository<PortfolioCompanyOperationalKPIValuesDraft> PortfolioCompanyOperationalKPIValuesDraftRepository
        {
            get
            {
               return _PortfolioCompanyOperationalKPIValuesDraft ??= new GenericRepository<PortfolioCompanyOperationalKPIValuesDraft>(_context);
            }
        }
        public IGenericRepository<MGroupingList> MGroupingListRepository
        {
            get
            {
                return _MGroupingListRepository ??= new GenericRepository<MGroupingList>(_context);
            }
        }
        public IGenericRepository<PortfolioCompanyCustomListDetails> PortfolioCustomListRepository
        {
            get
            {
                return _PortfolioCompanyCustomListDetailsRepository ??= new GenericRepository<PortfolioCompanyCustomListDetails>(_context);
            }
        }
        public IGenericRepository<ConsolidatedExcelTemplatePreference> ConsolidatedExcelTemplatePreferenceRepository
        {
            get
            {
                return _ConsolidatedExcelTemplatePreferenceRepository ??= new GenericRepository<ConsolidatedExcelTemplatePreference>(_context);
            }
        }

        IGenericRepository<PCTradingRecordValues> IUnitOfWork.PCTradingRecordValuesRepository
        {
            get
            {
                return _pcTradingRecordValues ??= new GenericRepository<PCTradingRecordValues>(_context);
            }
        }
        IGenericRepository<PageConfigurationCommentaryCustomFieldValue> IUnitOfWork.PageConfigurationCommentaryCustomFieldValueRepository
        {
            get
            {
                return _PageConfigurationCommentaryCustomFieldValueRepository ??= new GenericRepository<PageConfigurationCommentaryCustomFieldValue>(_context);
            }
        }

        public IGenericRepository<ValuationModelDetail> ValuationModelDetail
        {
            get
            {
               return _ValuationModelDetail ??= new GenericRepository<ValuationModelDetail>(_context);
            }
        }
        public IGenericRepository<MValuationFinancialKpi> MValuationFinancialKpiRepository
        {
            get
            {
                return _MValuationFinancialKpiRepository ??= new GenericRepository<MValuationFinancialKpi>(_context);
            }
        }
        public IGenericRepository<BackgroundJobReportHistory> BackgroundJobReportHistoryRepository
        {
            get
            {
                return _BackgroundJobReportHistoryRepository ??= new GenericRepository<BackgroundJobReportHistory>(_context);
            }
        }
        public IGenericRepository<MSubSectionFields> MSubSectionFieldsRepository
        {
            get
            {
                return _MSubSectionFields ??= new GenericRepository<MSubSectionFields>(_context);
            }
        }
        public IGenericRepository<DataAnalytics> DataAnalyticsRepository
        {
            get
            {
                return _DataAnalytics ??= new GenericRepository<DataAnalytics>(_context);
            }
        }

        public IGenericRepository<Valuation_ImpliedEVRecord> ImpliedEVRecordRepository
        {
            get
            {
              return _ImpliedEVRecordRepository ??= new GenericRepository<Valuation_ImpliedEVRecord>(_context);
            }
        }
        public IGenericRepository<Valuation_AdjustmentDetail> AdjustmentDetailRepository
        {
            get
            {
                return _AdjustmentDetailRepository ??= new GenericRepository<Valuation_AdjustmentDetail>(_context);
            }
        }

        public IGenericRepository<Valuation_TargetCompanyKPIRecord> TargetCompanyKPIRecordRepository
        {
            get
            {
               return _TargetCompanyKPIRecordRepository ??= new GenericRepository<Valuation_TargetCompanyKPIRecord>(_context);
            }
        }

        public IGenericRepository<Valuation_UnselectedRecords> Valuation_UnselectedRecordsRepository
        {
            get
            {
                return _Valuation_UnselectedRecordsRepository ??= new GenericRepository<Valuation_UnselectedRecords>(_context);
            }
        }

        public IGenericRepository<ValuationCompanyEquityCalculation> ValuationCompanyEquityCalculationRepository
        {
            get
            {
               return _ValuationCompanyEquityCalculationRepository ??= new GenericRepository<ValuationCompanyEquityCalculation>(_context);
            }
        }

        public IGenericRepository<ValuationEquityValue> ValuationEquityValueRepository
        {
            get
            {
                return _ValuationEquityValueRepository ??= new GenericRepository<ValuationEquityValue>(_context);
            }
        }


        public IGenericRepository<FinancialAuditLog> FinancialAuditLogRepository
        {
            get
            {
                return _FinancialAuditLogRpository ??= new GenericRepository<FinancialAuditLog>(_context);
            }
        }
        public IGenericRepository<MappingCompanyValuationHeaderType> MappingCompanyValuationHeaderTypeRepository
        {
            get
            {
                return _MappingCompanyValuationHeaderTypeRepository ??= new GenericRepository<MappingCompanyValuationHeaderType>(_context);
            }
        }
        public IGenericRepository<M_ValuationHeaderType> M_ValuationHeaderTypeRepository
        {
            get
            {
                return _M_ValuationHeaderTypeRepository ??= new GenericRepository<M_ValuationHeaderType>(_context);
            }
        }
        public IGenericRepository<DocumentsInformation> DocumentsInformationRepository
        {
            get
            {
                return _DocumentsInformationRepository ??= new GenericRepository<DocumentsInformation>(_context);
            }
        }
        public IGenericRepository<DocumentComments> DocumentCommentsRepository
        {
            get
            {
                return _DocumentCommentsRepository ??= new GenericRepository<DocumentComments>(_context);
            }
        }
        public IGenericRepository<MCapTable> MCapTableRepository
        {
            get
            {
                return _MCapTableRepository ??= new GenericRepository<MCapTable>(_context);
            }
        }
        public IGenericRepository<MappingCapTable> MappingCapTableRepository
        {
            get
            {
                return _MappingCapTableRepository ??= new GenericRepository<MappingCapTable>(_context);
            }
        }
        public IGenericRepository<MKpiType> MKpiTypeRepository
        {
            get
            {
                return _MKpiTypeRepository ??= new GenericRepository<MKpiType>(_context);
            }
        }
        public IGenericRepository<PcCapTableValues> PcCapTableValuesRepository
        {
            get
            {
                return _PcCapTableValuesRepository ??= new GenericRepository<PcCapTableValues>(_context);
            }
        }

        public IGenericRepository<CapTablePeriod> CapTablePeriodRepository
        {
            get
            {
                return _CapTablePeriodRepository ??= new GenericRepository<CapTablePeriod>(_context);
            }
        }
        public IGenericRepository<CapTableAuditLog> CapTableAuditLogRepository
        {
            get
            {
                return _CapTableAuditLogRepository ??= new GenericRepository<CapTableAuditLog>(_context);
            }
        }
        public IGenericRepository<DataRequest> DataRequestRepository
        {
            get
            {
                return _DataRequestRepository ??= new GenericRepository<DataRequest>(_context);
            }
        }
        public IGenericRepository<DataRequestGroup> DataRequestGroupRepository
        {
            get
            {
                return _DataRequestGroupRepository ??= new GenericRepository<DataRequestGroup>(_context);
            }
        }
        public IGenericRepository<DataRequestReminders> DataRequestReminderRepository
        {
            get
            {
                return _DataRequestReminderRepository ??= new GenericRepository<DataRequestReminders>(_context);
            }
        }
        /// <summary>
        /// Gets the repository for SDGImages.
        /// </summary>
        public IGenericRepository<SDGImages> SDGImagesRepository
        {
            get
            {
                return _SDGImagesRepository ??= new GenericRepository<SDGImages>(_context);
            }
        }
        public IGenericRepository<DataRequestAttachments> DataRequestAttachmentRepository
        {
            get
            {
                return _DataRequestAttachmentRepository ??= new GenericRepository<DataRequestAttachments>(_context);
            }
        }
        public IGenericRepository<ExternalUser> ExternalUserRepository
        {
            get
            {
                return _ExternalUserRepository ??= new GenericRepository<ExternalUser>(_context);
            }
        }
        public IGenericRepository<File_Upload_Status> File_Upload_StatusRepository
        {
            get
            {
                return _File_Upload_Status ??= new GenericRepository<File_Upload_Status>(_context);
            }
        }

        public IGenericRepository<UserBrowserDetails> UserBrowserDetailsRepository
        {
            get
            {
                return _UserBrowserDetailsRepository ??= new GenericRepository<UserBrowserDetails>(_context);
            }
        }
        public IGenericRepository<MMonthlyReport> MMonthlyReportRepository
        {
            get
            {
                return _MMonthlyReportRepository ??= new GenericRepository<MMonthlyReport>(_context);
            }
        }
        public IGenericRepository<MappingMonthlyReport> MappingMonthlyReportRepository
        {
            get
            {
                return _MappingMonthlyReportRepository ??= new GenericRepository<MappingMonthlyReport>(_context);
            }
        }
        public IGenericRepository<MLpReportTemplate> MLpReportTemplateRepository
        {
            get
            {
                return _MLpReportTemplateRepository ??= new GenericRepository<MLpReportTemplate>(_context);
            }
        }
        public IGenericRepository<LPReportHeader> LpReportHeaderRepository
        {
            get
            {
                return _LpReportHeaderRepository ??= new GenericRepository<LPReportHeader>(_context);
            }
        }

        public IGenericRepository<DashboardTrackerConfig> DashboardTrackerConfigRepository
        {
            get
            {
                return _DashboardTrackerConfigRepository ??= new GenericRepository<DashboardTrackerConfig>(_context);
            }
        }
        public IGenericRepository<MappingLpReportKpiSection> MappingLpReportKpiSectionRepository
        {
            get
            {
                return _MappingLpReportKpiSectionRepository ??= new GenericRepository<MappingLpReportKpiSection>(_context);
            }
        }
        public IGenericRepository<MappingLpReportPeriodSection> MappingLpReportPeriodSectionRepository
        {
            get
            {
                return _MappingLpReportPeriodSectionRepository ??= new GenericRepository<MappingLpReportPeriodSection>(_context);
            }
        }
        public IGenericRepository<MappingLpReportSection> MappingLpReportSectionRepository
        {
            get
            {
                return _MappingLpReportSectionRepository ??= new GenericRepository<MappingLpReportSection>(_context);
            }
        }
        public IGenericRepository<MappingLpReportTemplate> MappingLpReportTemplateRepository
        {
            get
            {
                return _MappingLpReportTemplateRepository ??= new GenericRepository<MappingLpReportTemplate>(_context);
            }
        }
        public IGenericRepository<MLpReportSection> MLpReportSectionRepository
        {
            get
            {
                return _MLpReportSectionRepository ??= new GenericRepository<MLpReportSection>(_context);
            }
        }
        public IGenericRepository<MappingLpReportCommentarySection> MappingLpReportCommentarySectionRepository
        {
            get
            {
                return _MappingLpReportCommentarySectionRepository ??= new GenericRepository<MappingLpReportCommentarySection>(_context);
            }
        }

        public IGenericRepository<GrowthReportHeader> GrowthReportHeaderRepository
        {
            get
            {
                return _GrowthReportHeaderRepository ??= new GenericRepository<GrowthReportHeader>(_context);
            }
        }
        public IGenericRepository<MappingGrowthReport> MappingGrowthReportRepository
        {
            get
            {
                return _MappingGrowthReportRepository ??= new GenericRepository<MappingGrowthReport>(_context);
            }
        }
        public IGenericRepository<GrowthReportHeaderModule> GrowthReportHeaderModuleRepository
        {
            get
            {
                return _GrowthReportHeaderModuleRepository ??= new GenericRepository<GrowthReportHeaderModule>(_context);
            }
        }
        public IGenericRepository<GrowthReportHeaderKpi> GrowthReportHeaderKpiRepository
        {
            get
            {
                return _GrowthReportHeaderKpiRepository ??= new GenericRepository<GrowthReportHeaderKpi>(_context);
            }
        }
        public IGenericRepository<GrowthReportColumnKpi> GrowthReportColumnKpiRepository
        {
            get
            {
                return _GrowthReportColumnKpiRepository ??= new GenericRepository<GrowthReportColumnKpi>(_context);
            }
        }
        public IGenericRepository<DataExtractionTypes> DataExtractionTypesRepository
        {
            get
            {
                return _DataExtractionTypesRepository ??= new GenericRepository<DataExtractionTypes>(_context);
            }
        }


        public IGenericRepository<UserInformation> UserInfoRepository
        {
            get
            {
                return _UserInfoRepository ??= new GenericRepository<UserInformation>(_context);
            }
        }

        public IGenericRepository<User_Documents> UserDocumentsRepository
        {
            get
            {
                return _UserDocumentsRepository ??= new GenericRepository<User_Documents>(_context);
            }
        }
        public IGenericRepository<M_UserCategory> MUserCategoryRepository
        {
            get
            {
                return _MUserCategoryRepository ??= new GenericRepository<M_UserCategory>(_context);
            }
        }
        public IGenericRepository<MSourceTypes> MSourceTypesRepository
        {
            get
            {
                return _MSourceTypesRepository ??= new GenericRepository<MSourceTypes>(_context);
            }
        }
        public IGenericRepository<CLO_InvestmentCompanyDetails> InvestmentCompanyDetailsRepository
        {
            get
            {
                return _investmentCompanyDetails ??= new GenericRepository<CLO_InvestmentCompanyDetails>(_context);
            }
        }
        public IGenericRepository<clo_commentries> CloCommentriesRepository
        {
            get
            {
                return _clocommentries ??= new GenericRepository<clo_commentries>(_context);
            }
        }
        public IGenericRepository<CloDetails> CloDetailsRepository
        {
            get
            {
                return _cloDetails ??= new GenericRepository<CloDetails>(_context);
            }
        }

        public IGenericRepository<TableFootnoteSchema> TableFootnoteSchemaRepository
        {
            get
            {
                return _tableFootnoteSchema ??= new GenericRepository<TableFootnoteSchema>(_context);
            }
        }

        IGenericRepository<DocCollectionFrequencyConfig> IUnitOfWork.DocCollectionConfigRepository
        {
            get
            {
                return _DocCollectionConfigRepository ??= new GenericRepository<DocCollectionFrequencyConfig>(_context);
            }
        }

        public IGenericRepository<DocumentCollectionStore> DocumentCollectionRepository
        {
            get
            {
                return _DocumentCollectionRepository ??= new GenericRepository<DocumentCollectionStore>(_context);
            }
        }


        public IGenericRepository<RepositoryDocumentMappingDetail> DocumentMappingRepository
        {
            get
            {
                return _DocumentMappingRepository ??= new GenericRepository<RepositoryDocumentMappingDetail>(_context);
            }
        }
        public IGenericRepository<MFundSectionKpi> MFundSectionKpiRepository
        {
            get
            {
                return _MFundSectionKpiRepository ??= new GenericRepository<MFundSectionKpi>(_context);
            }
        }
        public IGenericRepository<MappingFundSectionKpi> MappingFundSectionKpiRepository
        {
            get
            {
                return _MappingFundSectionKpiRepository ??= new GenericRepository<MappingFundSectionKpi>(_context);
            }
        }
        public IGenericRepository<FundMasterKpiValues> FundMasterKpiValuesRepository
        {
            get
            {
                return _FundMasterKpiValuesRepository ??= new GenericRepository<FundMasterKpiValues>(_context);
            }
        }
        public IGenericRepository<FundKpiAuditLog> FundKpiAuditLogRepository
        {
            get
            {
                return _FundKpiAuditLogRepository ??= new GenericRepository<FundKpiAuditLog>(_context);
            }
        }
        public IGenericRepository<MFundKpiModules> MFundKpiModulesRepository
        {
            get
            {
                return _MFundKpiModulesRepository ??= new GenericRepository<MFundKpiModules>(_context);
            }
        }
        #endregion Public Repository Creation properties...

        #region Public member methods

        public int Save()
        {
            return _context.SaveChanges();
        }
        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }
        #endregion

        #region Implementing IDiosposable...

        #region private dispose variable declaration...
        private bool disposed = false;
        #endregion

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed && disposing)
            {
                _context.Dispose();
            }
            disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        #endregion

        public object DynamicSQLQuery(string sql, params object[] parameters)
        {
            return _context.Database.GetDbConnection().DynamicSqlQuery(sql, parameters);
        }

        public object DynamicSQLPreviewQuery(string sql, params object[] parameters)
        {
            return _context.Database.GetDbConnection().DynamicSQLPreviewQuery(sql, parameters);
        }

        public object SQLEditorQuery(string sql, params object[] parameters)
        {
            return _context.Database.GetDbConnection().SQLEditorQuery(sql, parameters);
        }
        public IGenericRepository<M_clopagedetails> CLOPageDetailsRepository
        {
            get
            {
                return _clopageDetails ??= new GenericRepository<M_clopagedetails>(_context);
            }
        }
        public IGenericRepository<CLO_M_Table> CLOTableDetailsRepository
        {
            get
            {
                return _clotableDetails ??= new GenericRepository<CLO_M_Table>(_context);
            }
        }
        public IGenericRepository<EmailNotificationGroup> EmailNotificationGroupRepository
        {
            get
            {
                return _emailNotificationGroupRepository ??= new GenericRepository<EmailNotificationGroup>(_context);
            }
        }

        public IGenericRepository<EmailListMember> EmailListMemberRepository
        {
            get
            {
                return _emailListMemberRepository ??= new GenericRepository<EmailListMember>(_context);
            }
        }

        public IGenericRepository<CompanyEmailGroup> CompanyEmailGroupRepository
        {
            get
            {
                return _emailGroupCompanyRepository ??= new GenericRepository<CompanyEmailGroup>(_context);
            }
        }

        public IGenericRepository<EmailReminder> EmailReminderRepository
        {
            get
            {
                return _emailReminderRepository ??= new GenericRepository<EmailReminder>(_context);
            }
        }

        public IGenericRepository<EmailReminderRecipients> EmailReminderRecipientsRepository
        {
            get
            {
                return _emailReminderRecipientsRepository ??= new GenericRepository<EmailReminderRecipients>(_context);
            }
        }

        public IGenericRepository<EmailReminderConfig> EmailReminderConfigRepository
        {
            get
            {
                return _emailReminderConfigRepository ??= new GenericRepository<EmailReminderConfig>(_context);
            }
        }

        public IGenericRepository<EmailReminderSchedule> EmailReminderScheduleRepository
        {
            get
            {
                return _emailReminderScheduleRepository ??= new GenericRepository<EmailReminderSchedule>(_context);
            }
        }
    }

}